#!/usr/bin/env python3
"""
代码收集脚本
将项目中所有Python文件的代码合并到一个txt文件中

使用方法:
python collect_code.py

输出文件: all_code.txt
"""

import os
import sys
from pathlib import Path
from datetime import datetime


class CodeCollector:
    """代码收集器"""
    
    def __init__(self):
        self.project_root = Path(__file__).parent.absolute()
        self.output_file = self.project_root / "all_code.txt"
        
        # 需要排除的目录
        self.exclude_dirs = {
            '__pycache__',
            '.git',
            '.vscode',
            'build',
            'dist',
            '.pytest_cache',
            'node_modules',
            '.idea'
        }
        
        # 需要排除的文件
        self.exclude_files = {
            'collect_code.py',  # 排除自己
            '__init__.py'       # 可选：排除空的__init__.py文件
        }
        
        # 支持的Python文件扩展名
        self.python_extensions = {'.py', '.pyw'}
    
    def should_exclude_dir(self, dir_path):
        """判断是否应该排除目录"""
        dir_name = dir_path.name
        return dir_name in self.exclude_dirs or dir_name.startswith('.')
    
    def should_exclude_file(self, file_path):
        """判断是否应该排除文件"""
        file_name = file_path.name
        
        # 排除特定文件
        if file_name in self.exclude_files:
            return True
        
        # 只处理Python文件
        if file_path.suffix not in self.python_extensions:
            return True
        
        # 排除空的__init__.py文件
        if file_name == '__init__.py':
            try:
                content = file_path.read_text(encoding='utf-8', errors='ignore').strip()
                if not content or content.startswith('#') and len(content.split('\n')) <= 5:
                    return True
            except:
                return True
        
        return False
    
    def get_python_files(self):
        """获取所有Python文件"""
        python_files = []
        
        for root, dirs, files in os.walk(self.project_root):
            root_path = Path(root)
            
            # 排除不需要的目录
            dirs[:] = [d for d in dirs if not self.should_exclude_dir(root_path / d)]
            
            for file in files:
                file_path = root_path / file
                
                if not self.should_exclude_file(file_path):
                    python_files.append(file_path)
        
        # 按路径排序
        python_files.sort(key=lambda x: str(x.relative_to(self.project_root)))
        
        return python_files
    
    def read_file_content(self, file_path):
        """读取文件内容"""
        try:
            with open(file_path, 'r', encoding='utf-8', errors='ignore') as f:
                return f.read()
        except Exception as e:
            return f"# 读取文件失败: {e}\n"
    
    def collect_code(self):
        """收集所有代码"""
        print("开始收集Python代码...")
        
        python_files = self.get_python_files()
        
        if not python_files:
            print("未找到Python文件!")
            return False
        
        print(f"找到 {len(python_files)} 个Python文件")
        
        # 创建输出内容
        output_lines = []
        
        # 添加头部信息
        output_lines.append("=" * 80)
        output_lines.append("北斗网格转换工具 - 完整源代码")
        output_lines.append("=" * 80)
        output_lines.append(f"生成时间: {datetime.now().strftime('%Y-%m-%d %H:%M:%S')}")
        output_lines.append(f"文件总数: {len(python_files)}")
        output_lines.append("=" * 80)
        output_lines.append("")
        
        # 添加文件列表
        output_lines.append("文件列表:")
        for i, file_path in enumerate(python_files, 1):
            rel_path = file_path.relative_to(self.project_root)
            output_lines.append(f"  {i:2d}. {rel_path}")
        output_lines.append("")
        output_lines.append("=" * 80)
        output_lines.append("")
        
        # 处理每个文件
        for i, file_path in enumerate(python_files, 1):
            rel_path = file_path.relative_to(self.project_root)
            print(f"处理文件 {i}/{len(python_files)}: {rel_path}")
            
            # 添加文件分隔符
            output_lines.append("=" * 80)
            output_lines.append(f"文件 {i}: {rel_path}")
            output_lines.append("=" * 80)
            output_lines.append("")
            
            # 读取文件内容
            content = self.read_file_content(file_path)
            
            # 添加行号
            lines = content.split('\n')
            for line_num, line in enumerate(lines, 1):
                output_lines.append(f"{line_num:4d}: {line}")
            
            output_lines.append("")
            output_lines.append("")
        
        # 写入输出文件
        try:
            with open(self.output_file, 'w', encoding='utf-8') as f:
                f.write('\n'.join(output_lines))
            
            print(f"代码收集完成!")
            print(f"输出文件: {self.output_file}")
            print(f"文件大小: {self.output_file.stat().st_size / 1024:.1f} KB")
            
            return True
            
        except Exception as e:
            print(f"写入文件失败: {e}")
            return False
    
    def create_summary(self):
        """创建代码统计摘要"""
        python_files = self.get_python_files()
        
        total_lines = 0
        total_files = len(python_files)
        file_stats = []
        
        for file_path in python_files:
            try:
                content = self.read_file_content(file_path)
                lines = len(content.split('\n'))
                total_lines += lines
                
                rel_path = file_path.relative_to(self.project_root)
                file_stats.append((str(rel_path), lines))
            except:
                continue
        
        # 按行数排序
        file_stats.sort(key=lambda x: x[1], reverse=True)
        
        print("\n" + "=" * 60)
        print("代码统计摘要")
        print("=" * 60)
        print(f"总文件数: {total_files}")
        print(f"总行数: {total_lines:,}")
        print(f"平均每文件行数: {total_lines/total_files:.1f}")
        print("\n文件行数排行:")
        
        for i, (file_path, lines) in enumerate(file_stats[:10], 1):
            print(f"  {i:2d}. {file_path:<40} {lines:>6} 行")
        
        if len(file_stats) > 10:
            print(f"  ... 还有 {len(file_stats) - 10} 个文件")


def main():
    """主函数"""
    print("北斗网格转换工具 - 代码收集脚本")
    print("=" * 50)
    
    collector = CodeCollector()
    
    try:
        # 收集代码
        success = collector.collect_code()
        
        if success:
            # 显示统计信息
            collector.create_summary()
            print("\n✅ 代码收集完成!")
        else:
            print("\n❌ 代码收集失败!")
            sys.exit(1)
            
    except KeyboardInterrupt:
        print("\n用户中断操作")
        sys.exit(1)
    except Exception as e:
        print(f"\n发生错误: {e}")
        import traceback
        traceback.print_exc()
        sys.exit(1)


if __name__ == "__main__":
    main()
