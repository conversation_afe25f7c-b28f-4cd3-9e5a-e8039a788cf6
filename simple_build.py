#!/usr/bin/env python3
"""
简化的打包脚本 - 解决命令行过长问题
"""

import os
import sys
import shutil
import subprocess
from pathlib import Path

def check_pyinstaller():
    """检查PyInstaller是否已安装"""
    try:
        import PyInstaller
        print(f"✓ PyInstaller已安装，版本: {PyInstaller.__version__}")
        return True
    except ImportError:
        print("✗ PyInstaller未安装")
        print("请运行: pip install pyinstaller")
        return False

def clean_build_dirs():
    """清理之前的构建目录"""
    dirs_to_clean = ['build', 'dist', '__pycache__']
    
    for dir_name in dirs_to_clean:
        if os.path.exists(dir_name):
            print(f"清理目录: {dir_name}")
            shutil.rmtree(dir_name)
    
    # 清理.spec文件
    spec_files = list(Path('.').glob('*.spec'))
    for spec_file in spec_files:
        print(f"删除spec文件: {spec_file}")
        spec_file.unlink()

def create_spec_file():
    """创建PyInstaller spec文件"""
    spec_content = '''# -*- mode: python ; coding: utf-8 -*-

a = Analysis(
    ['main.py'],
    pathex=[],
    binaries=[],
    datas=[
        ('core', 'core'),
        ('data', 'data'),
        ('gui', 'gui'),
    ],
    hiddenimports=[
        'rasterio',
        'rasterio.crs',
        'rasterio.transform',
        'rasterio.features',
        'fiona',
        'fiona.crs',
        'shapely',
        'shapely.geometry',
        'numpy',
        'scipy',
        'scipy.ndimage',
        'pyproj',
        'tkinter',
        'tkinter.ttk',
        'tkinter.filedialog',
        'tkinter.messagebox',
    ],
    hookspath=[],
    hooksconfig={},
    runtime_hooks=[],
    excludes=[
        'matplotlib',
        'pandas',
        'IPython',
        'jupyter',
        'PyQt5',
        'PyQt6',
        'PySide2',
        'PySide6',
    ],
    noarchive=False,
    optimize=0,
)

pyz = PYZ(a.pure)

exe = EXE(
    pyz,
    a.scripts,
    a.binaries,
    a.datas,
    [],
    name='北斗网格转换工具',
    debug=False,
    bootloader_ignore_signals=False,
    strip=False,
    upx=True,
    upx_exclude=[],
    runtime_tmpdir=None,
    console=False,
    disable_windowed_traceback=False,
    argv_emulation=False,
    target_arch=None,
    codesign_identity=None,
    entitlements_file=None,
)
'''
    
    with open('beidou_tool.spec', 'w', encoding='utf-8') as f:
        f.write(spec_content)
    
    print("✓ 已创建spec文件: beidou_tool.spec")

def create_onedir_spec_file():
    """创建目录版本的spec文件"""
    spec_content = '''# -*- mode: python ; coding: utf-8 -*-

a = Analysis(
    ['main.py'],
    pathex=[],
    binaries=[],
    datas=[
        ('core', 'core'),
        ('data', 'data'),
        ('gui', 'gui'),
    ],
    hiddenimports=[
        'rasterio',
        'rasterio.crs',
        'rasterio.transform',
        'rasterio.features',
        'fiona',
        'fiona.crs',
        'shapely',
        'shapely.geometry',
        'numpy',
        'scipy',
        'scipy.ndimage',
        'pyproj',
        'tkinter',
        'tkinter.ttk',
        'tkinter.filedialog',
        'tkinter.messagebox',
    ],
    hookspath=[],
    hooksconfig={},
    runtime_hooks=[],
    excludes=[
        'matplotlib',
        'pandas',
        'IPython',
        'jupyter',
        'PyQt5',
        'PyQt6',
        'PySide2',
        'PySide6',
    ],
    noarchive=False,
    optimize=0,
)

pyz = PYZ(a.pure)

exe = EXE(
    pyz,
    a.scripts,
    [],
    exclude_binaries=True,
    name='北斗网格转换工具',
    debug=False,
    bootloader_ignore_signals=False,
    strip=False,
    upx=True,
    console=False,
    disable_windowed_traceback=False,
    argv_emulation=False,
    target_arch=None,
    codesign_identity=None,
    entitlements_file=None,
)

coll = COLLECT(
    exe,
    a.binaries,
    a.datas,
    strip=False,
    upx=True,
    upx_exclude=[],
    name='北斗网格转换工具',
)
'''
    
    with open('beidou_tool_dir.spec', 'w', encoding='utf-8') as f:
        f.write(spec_content)
    
    print("✓ 已创建目录版本spec文件: beidou_tool_dir.spec")

def build_from_spec(spec_file):
    """使用spec文件构建"""
    try:
        cmd = ['pyinstaller', '--clean', spec_file]
        print(f"执行命令: {' '.join(cmd)}")
        
        result = subprocess.run(cmd, check=True, capture_output=True, text=True)
        print("✓ 构建成功!")
        print(result.stdout)
        return True
    except subprocess.CalledProcessError as e:
        print("✗ 构建失败!")
        print("错误输出:", e.stderr)
        print("标准输出:", e.stdout)
        return False
    except Exception as e:
        print(f"✗ 执行失败: {e}")
        return False

def main():
    """主函数"""
    print("=" * 50)
    print("北斗网格转换工具 - 简化打包脚本")
    print("=" * 50)
    
    # 检查PyInstaller
    if not check_pyinstaller():
        return
    
    # 清理构建目录
    clean_build_dirs()
    
    print("\n选择打包方式:")
    print("1. 单文件版本 (便于分发)")
    print("2. 目录版本 (快速启动)")
    print("3. 两种都构建")
    
    choice = input("请选择 (1/2/3): ").strip()
    
    success = False
    
    if choice == '1':
        create_spec_file()
        success = build_from_spec('beidou_tool.spec')
    elif choice == '2':
        create_onedir_spec_file()
        success = build_from_spec('beidou_tool_dir.spec')
    elif choice == '3':
        print("\n构建目录版本...")
        create_onedir_spec_file()
        success1 = build_from_spec('beidou_tool_dir.spec')
        
        print("\n构建单文件版本...")
        create_spec_file()
        success2 = build_from_spec('beidou_tool.spec')
        
        success = success1 or success2
    else:
        print("无效选择")
        return
    
    if success:
        print("\n" + "=" * 50)
        print("构建完成!")
        print("输出文件位于 dist/ 目录中")
        print("=" * 50)
        
        # 显示输出文件信息
        if os.path.exists('dist'):
            print("\n生成的文件:")
            for item in os.listdir('dist'):
                item_path = os.path.join('dist', item)
                if os.path.isfile(item_path):
                    size = os.path.getsize(item_path) / (1024 * 1024)  # MB
                    print(f"  📄 {item} ({size:.1f} MB)")
                elif os.path.isdir(item_path):
                    print(f"  📁 {item}/")
    else:
        print("\n构建失败，请检查错误信息")

if __name__ == '__main__':
    main()
