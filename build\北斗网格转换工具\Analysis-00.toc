(['E:\\YC\\每日任务\\2025\\07\\0731\\北斗网格转换\\main.py'],
 ['E:\\YC\\每日任务\\2025\\07\\0731\\北斗网格转换'],
 ['rasterio',
  'rasterio.drivers',
  'rasterio._shim',
  'rasterio.control',
  'rasterio.crs',
  'rasterio.dtypes',
  'rasterio.env',
  'rasterio.errors',
  'rasterio.features',
  'rasterio.fill',
  'rasterio.mask',
  'rasterio.merge',
  'rasterio.plot',
  'rasterio.profiles',
  'rasterio.rio',
  'rasterio.sample',
  'rasterio.transform',
  'rasterio.warp',
  'rasterio.windows',
  'fiona',
  'fiona.drivers',
  'fiona.crs',
  'fiona.transform',
  'shapely',
  'shapely.geometry',
  'shapely.ops',
  'shapely.prepared',
  'pyproj',
  'pyproj.crs',
  'pyproj.transformer',
  'numpy',
  'scipy',
  'scipy.ndimage',
  'scipy.spatial',
  'scipy.interpolate',
  'tkinter',
  'tkinter.ttk',
  'tkinter.filedialog',
  'tkinter.messagebox',
  'core',
  'core.aggregation',
  'core.coordinate_utils',
  'core.grid_calculator',
  'data',
  'data.data_validator',
  'data.raster_io',
  'data.vector_io',
  'gui',
  'gui.main_window',
  'gui.parameter_panel',
  'gui.progress_dialog',
  'gui.window_utils'],
 [('C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python313\\site-packages\\numpy\\_pyinstaller',
   0),
  ('C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python313\\site-packages\\pypinyin\\__pyinstaller',
   0),
  ('C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python313\\site-packages\\_pyinstaller_hooks_contrib\\stdhooks',
   -1000),
  ('C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python313\\site-packages\\_pyinstaller_hooks_contrib',
   -1000)],
 {},
 ['matplotlib',
  'IPython',
  'jupyter',
  'notebook',
  'pandas',
  'seaborn',
  'plotly',
  '__main__'],
 [],
 False,
 {},
 0,
 [],
 [],
 '3.13.3 (tags/v3.13.3:6280bb5, Apr  8 2025, 14:47:33) [MSC v.1943 64 bit '
 '(AMD64)]',
 [('pyi_rth_inspect',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python313\\site-packages\\PyInstaller\\hooks\\rthooks\\pyi_rth_inspect.py',
   'PYSOURCE'),
  ('pyi_rth__tkinter',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python313\\site-packages\\PyInstaller\\hooks\\rthooks\\pyi_rth__tkinter.py',
   'PYSOURCE'),
  ('pyi_rth_pkgutil',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python313\\site-packages\\PyInstaller\\hooks\\rthooks\\pyi_rth_pkgutil.py',
   'PYSOURCE'),
  ('pyi_rth_multiprocessing',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python313\\site-packages\\PyInstaller\\hooks\\rthooks\\pyi_rth_multiprocessing.py',
   'PYSOURCE'),
  ('pyi_rth_pkgres',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python313\\site-packages\\PyInstaller\\hooks\\rthooks\\pyi_rth_pkgres.py',
   'PYSOURCE'),
  ('pyi_rth_setuptools',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python313\\site-packages\\PyInstaller\\hooks\\rthooks\\pyi_rth_setuptools.py',
   'PYSOURCE'),
  ('pyi_rth_pyproj',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python313\\site-packages\\_pyinstaller_hooks_contrib\\rthooks\\pyi_rth_pyproj.py',
   'PYSOURCE'),
  ('main', 'E:\\YC\\每日任务\\2025\\07\\0731\\北斗网格转换\\main.py', 'PYSOURCE')],
 [('_distutils_hack',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python313\\site-packages\\_distutils_hack\\__init__.py',
   'PYMODULE'),
  ('importlib.util', 'C:\\Python313\\Lib\\importlib\\util.py', 'PYMODULE'),
  ('threading', 'C:\\Python313\\Lib\\threading.py', 'PYMODULE'),
  ('_threading_local', 'C:\\Python313\\Lib\\_threading_local.py', 'PYMODULE'),
  ('contextlib', 'C:\\Python313\\Lib\\contextlib.py', 'PYMODULE'),
  ('_strptime', 'C:\\Python313\\Lib\\_strptime.py', 'PYMODULE'),
  ('datetime', 'C:\\Python313\\Lib\\datetime.py', 'PYMODULE'),
  ('_pydatetime', 'C:\\Python313\\Lib\\_pydatetime.py', 'PYMODULE'),
  ('calendar', 'C:\\Python313\\Lib\\calendar.py', 'PYMODULE'),
  ('importlib._bootstrap_external',
   'C:\\Python313\\Lib\\importlib\\_bootstrap_external.py',
   'PYMODULE'),
  ('importlib.metadata',
   'C:\\Python313\\Lib\\importlib\\metadata\\__init__.py',
   'PYMODULE'),
  ('csv', 'C:\\Python313\\Lib\\csv.py', 'PYMODULE'),
  ('importlib.metadata._adapters',
   'C:\\Python313\\Lib\\importlib\\metadata\\_adapters.py',
   'PYMODULE'),
  ('importlib.metadata._text',
   'C:\\Python313\\Lib\\importlib\\metadata\\_text.py',
   'PYMODULE'),
  ('email.message', 'C:\\Python313\\Lib\\email\\message.py', 'PYMODULE'),
  ('email.policy', 'C:\\Python313\\Lib\\email\\policy.py', 'PYMODULE'),
  ('email.contentmanager',
   'C:\\Python313\\Lib\\email\\contentmanager.py',
   'PYMODULE'),
  ('email.quoprimime', 'C:\\Python313\\Lib\\email\\quoprimime.py', 'PYMODULE'),
  ('string', 'C:\\Python313\\Lib\\string.py', 'PYMODULE'),
  ('email.headerregistry',
   'C:\\Python313\\Lib\\email\\headerregistry.py',
   'PYMODULE'),
  ('email._header_value_parser',
   'C:\\Python313\\Lib\\email\\_header_value_parser.py',
   'PYMODULE'),
  ('urllib', 'C:\\Python313\\Lib\\urllib\\__init__.py', 'PYMODULE'),
  ('email.iterators', 'C:\\Python313\\Lib\\email\\iterators.py', 'PYMODULE'),
  ('email.generator', 'C:\\Python313\\Lib\\email\\generator.py', 'PYMODULE'),
  ('copy', 'C:\\Python313\\Lib\\copy.py', 'PYMODULE'),
  ('random', 'C:\\Python313\\Lib\\random.py', 'PYMODULE'),
  ('statistics', 'C:\\Python313\\Lib\\statistics.py', 'PYMODULE'),
  ('decimal', 'C:\\Python313\\Lib\\decimal.py', 'PYMODULE'),
  ('_pydecimal', 'C:\\Python313\\Lib\\_pydecimal.py', 'PYMODULE'),
  ('contextvars', 'C:\\Python313\\Lib\\contextvars.py', 'PYMODULE'),
  ('fractions', 'C:\\Python313\\Lib\\fractions.py', 'PYMODULE'),
  ('numbers', 'C:\\Python313\\Lib\\numbers.py', 'PYMODULE'),
  ('hashlib', 'C:\\Python313\\Lib\\hashlib.py', 'PYMODULE'),
  ('bisect', 'C:\\Python313\\Lib\\bisect.py', 'PYMODULE'),
  ('email._encoded_words',
   'C:\\Python313\\Lib\\email\\_encoded_words.py',
   'PYMODULE'),
  ('base64', 'C:\\Python313\\Lib\\base64.py', 'PYMODULE'),
  ('getopt', 'C:\\Python313\\Lib\\getopt.py', 'PYMODULE'),
  ('gettext', 'C:\\Python313\\Lib\\gettext.py', 'PYMODULE'),
  ('struct', 'C:\\Python313\\Lib\\struct.py', 'PYMODULE'),
  ('email.charset', 'C:\\Python313\\Lib\\email\\charset.py', 'PYMODULE'),
  ('email.encoders', 'C:\\Python313\\Lib\\email\\encoders.py', 'PYMODULE'),
  ('email.base64mime', 'C:\\Python313\\Lib\\email\\base64mime.py', 'PYMODULE'),
  ('email._policybase',
   'C:\\Python313\\Lib\\email\\_policybase.py',
   'PYMODULE'),
  ('email.header', 'C:\\Python313\\Lib\\email\\header.py', 'PYMODULE'),
  ('email.errors', 'C:\\Python313\\Lib\\email\\errors.py', 'PYMODULE'),
  ('email.utils', 'C:\\Python313\\Lib\\email\\utils.py', 'PYMODULE'),
  ('socket', 'C:\\Python313\\Lib\\socket.py', 'PYMODULE'),
  ('selectors', 'C:\\Python313\\Lib\\selectors.py', 'PYMODULE'),
  ('email._parseaddr', 'C:\\Python313\\Lib\\email\\_parseaddr.py', 'PYMODULE'),
  ('urllib.parse', 'C:\\Python313\\Lib\\urllib\\parse.py', 'PYMODULE'),
  ('ipaddress', 'C:\\Python313\\Lib\\ipaddress.py', 'PYMODULE'),
  ('quopri', 'C:\\Python313\\Lib\\quopri.py', 'PYMODULE'),
  ('importlib.metadata._itertools',
   'C:\\Python313\\Lib\\importlib\\metadata\\_itertools.py',
   'PYMODULE'),
  ('importlib.metadata._functools',
   'C:\\Python313\\Lib\\importlib\\metadata\\_functools.py',
   'PYMODULE'),
  ('importlib.metadata._collections',
   'C:\\Python313\\Lib\\importlib\\metadata\\_collections.py',
   'PYMODULE'),
  ('importlib.metadata._meta',
   'C:\\Python313\\Lib\\importlib\\metadata\\_meta.py',
   'PYMODULE'),
  ('textwrap', 'C:\\Python313\\Lib\\textwrap.py', 'PYMODULE'),
  ('zipfile', 'C:\\Python313\\Lib\\zipfile\\__init__.py', 'PYMODULE'),
  ('zipfile._path',
   'C:\\Python313\\Lib\\zipfile\\_path\\__init__.py',
   'PYMODULE'),
  ('zipfile._path.glob',
   'C:\\Python313\\Lib\\zipfile\\_path\\glob.py',
   'PYMODULE'),
  ('py_compile', 'C:\\Python313\\Lib\\py_compile.py', 'PYMODULE'),
  ('importlib.machinery',
   'C:\\Python313\\Lib\\importlib\\machinery.py',
   'PYMODULE'),
  ('lzma', 'C:\\Python313\\Lib\\lzma.py', 'PYMODULE'),
  ('_compression', 'C:\\Python313\\Lib\\_compression.py', 'PYMODULE'),
  ('bz2', 'C:\\Python313\\Lib\\bz2.py', 'PYMODULE'),
  ('shutil', 'C:\\Python313\\Lib\\shutil.py', 'PYMODULE'),
  ('tarfile', 'C:\\Python313\\Lib\\tarfile.py', 'PYMODULE'),
  ('gzip', 'C:\\Python313\\Lib\\gzip.py', 'PYMODULE'),
  ('fnmatch', 'C:\\Python313\\Lib\\fnmatch.py', 'PYMODULE'),
  ('pathlib', 'C:\\Python313\\Lib\\pathlib\\__init__.py', 'PYMODULE'),
  ('pathlib._local', 'C:\\Python313\\Lib\\pathlib\\_local.py', 'PYMODULE'),
  ('glob', 'C:\\Python313\\Lib\\glob.py', 'PYMODULE'),
  ('pathlib._abc', 'C:\\Python313\\Lib\\pathlib\\_abc.py', 'PYMODULE'),
  ('inspect', 'C:\\Python313\\Lib\\inspect.py', 'PYMODULE'),
  ('token', 'C:\\Python313\\Lib\\token.py', 'PYMODULE'),
  ('dis', 'C:\\Python313\\Lib\\dis.py', 'PYMODULE'),
  ('opcode', 'C:\\Python313\\Lib\\opcode.py', 'PYMODULE'),
  ('_opcode_metadata', 'C:\\Python313\\Lib\\_opcode_metadata.py', 'PYMODULE'),
  ('ast', 'C:\\Python313\\Lib\\ast.py', 'PYMODULE'),
  ('email', 'C:\\Python313\\Lib\\email\\__init__.py', 'PYMODULE'),
  ('email.parser', 'C:\\Python313\\Lib\\email\\parser.py', 'PYMODULE'),
  ('email.feedparser', 'C:\\Python313\\Lib\\email\\feedparser.py', 'PYMODULE'),
  ('json', 'C:\\Python313\\Lib\\json\\__init__.py', 'PYMODULE'),
  ('json.encoder', 'C:\\Python313\\Lib\\json\\encoder.py', 'PYMODULE'),
  ('json.decoder', 'C:\\Python313\\Lib\\json\\decoder.py', 'PYMODULE'),
  ('json.scanner', 'C:\\Python313\\Lib\\json\\scanner.py', 'PYMODULE'),
  ('__future__', 'C:\\Python313\\Lib\\__future__.py', 'PYMODULE'),
  ('importlib.readers',
   'C:\\Python313\\Lib\\importlib\\readers.py',
   'PYMODULE'),
  ('importlib.resources.readers',
   'C:\\Python313\\Lib\\importlib\\resources\\readers.py',
   'PYMODULE'),
  ('importlib.resources._itertools',
   'C:\\Python313\\Lib\\importlib\\resources\\_itertools.py',
   'PYMODULE'),
  ('importlib.resources.abc',
   'C:\\Python313\\Lib\\importlib\\resources\\abc.py',
   'PYMODULE'),
  ('importlib.resources',
   'C:\\Python313\\Lib\\importlib\\resources\\__init__.py',
   'PYMODULE'),
  ('importlib.resources._functional',
   'C:\\Python313\\Lib\\importlib\\resources\\_functional.py',
   'PYMODULE'),
  ('importlib.resources._common',
   'C:\\Python313\\Lib\\importlib\\resources\\_common.py',
   'PYMODULE'),
  ('importlib.resources._adapters',
   'C:\\Python313\\Lib\\importlib\\resources\\_adapters.py',
   'PYMODULE'),
  ('tempfile', 'C:\\Python313\\Lib\\tempfile.py', 'PYMODULE'),
  ('tokenize', 'C:\\Python313\\Lib\\tokenize.py', 'PYMODULE'),
  ('importlib._bootstrap',
   'C:\\Python313\\Lib\\importlib\\_bootstrap.py',
   'PYMODULE'),
  ('importlib._abc', 'C:\\Python313\\Lib\\importlib\\_abc.py', 'PYMODULE'),
  ('importlib.abc', 'C:\\Python313\\Lib\\importlib\\abc.py', 'PYMODULE'),
  ('importlib', 'C:\\Python313\\Lib\\importlib\\__init__.py', 'PYMODULE'),
  ('setuptools',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python313\\site-packages\\setuptools\\__init__.py',
   'PYMODULE'),
  ('setuptools.msvc',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python313\\site-packages\\setuptools\\msvc.py',
   'PYMODULE'),
  ('typing_extensions',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python313\\site-packages\\typing_extensions.py',
   'PYMODULE'),
  ('asyncio.coroutines',
   'C:\\Python313\\Lib\\asyncio\\coroutines.py',
   'PYMODULE'),
  ('asyncio', 'C:\\Python313\\Lib\\asyncio\\__init__.py', 'PYMODULE'),
  ('asyncio.unix_events',
   'C:\\Python313\\Lib\\asyncio\\unix_events.py',
   'PYMODULE'),
  ('asyncio.log', 'C:\\Python313\\Lib\\asyncio\\log.py', 'PYMODULE'),
  ('subprocess', 'C:\\Python313\\Lib\\subprocess.py', 'PYMODULE'),
  ('signal', 'C:\\Python313\\Lib\\signal.py', 'PYMODULE'),
  ('asyncio.windows_events',
   'C:\\Python313\\Lib\\asyncio\\windows_events.py',
   'PYMODULE'),
  ('asyncio.windows_utils',
   'C:\\Python313\\Lib\\asyncio\\windows_utils.py',
   'PYMODULE'),
  ('asyncio.selector_events',
   'C:\\Python313\\Lib\\asyncio\\selector_events.py',
   'PYMODULE'),
  ('ssl', 'C:\\Python313\\Lib\\ssl.py', 'PYMODULE'),
  ('asyncio.proactor_events',
   'C:\\Python313\\Lib\\asyncio\\proactor_events.py',
   'PYMODULE'),
  ('asyncio.base_subprocess',
   'C:\\Python313\\Lib\\asyncio\\base_subprocess.py',
   'PYMODULE'),
  ('asyncio.threads', 'C:\\Python313\\Lib\\asyncio\\threads.py', 'PYMODULE'),
  ('asyncio.taskgroups',
   'C:\\Python313\\Lib\\asyncio\\taskgroups.py',
   'PYMODULE'),
  ('asyncio.subprocess',
   'C:\\Python313\\Lib\\asyncio\\subprocess.py',
   'PYMODULE'),
  ('asyncio.streams', 'C:\\Python313\\Lib\\asyncio\\streams.py', 'PYMODULE'),
  ('asyncio.runners', 'C:\\Python313\\Lib\\asyncio\\runners.py', 'PYMODULE'),
  ('asyncio.base_events',
   'C:\\Python313\\Lib\\asyncio\\base_events.py',
   'PYMODULE'),
  ('concurrent.futures',
   'C:\\Python313\\Lib\\concurrent\\futures\\__init__.py',
   'PYMODULE'),
  ('concurrent.futures.thread',
   'C:\\Python313\\Lib\\concurrent\\futures\\thread.py',
   'PYMODULE'),
  ('queue', 'C:\\Python313\\Lib\\queue.py', 'PYMODULE'),
  ('concurrent.futures.process',
   'C:\\Python313\\Lib\\concurrent\\futures\\process.py',
   'PYMODULE'),
  ('multiprocessing.synchronize',
   'C:\\Python313\\Lib\\multiprocessing\\synchronize.py',
   'PYMODULE'),
  ('multiprocessing.heap',
   'C:\\Python313\\Lib\\multiprocessing\\heap.py',
   'PYMODULE'),
  ('multiprocessing.resource_tracker',
   'C:\\Python313\\Lib\\multiprocessing\\resource_tracker.py',
   'PYMODULE'),
  ('multiprocessing.spawn',
   'C:\\Python313\\Lib\\multiprocessing\\spawn.py',
   'PYMODULE'),
  ('runpy', 'C:\\Python313\\Lib\\runpy.py', 'PYMODULE'),
  ('pkgutil', 'C:\\Python313\\Lib\\pkgutil.py', 'PYMODULE'),
  ('zipimport', 'C:\\Python313\\Lib\\zipimport.py', 'PYMODULE'),
  ('multiprocessing.util',
   'C:\\Python313\\Lib\\multiprocessing\\util.py',
   'PYMODULE'),
  ('multiprocessing.forkserver',
   'C:\\Python313\\Lib\\multiprocessing\\forkserver.py',
   'PYMODULE'),
  ('multiprocessing.process',
   'C:\\Python313\\Lib\\multiprocessing\\process.py',
   'PYMODULE'),
  ('multiprocessing.context',
   'C:\\Python313\\Lib\\multiprocessing\\context.py',
   'PYMODULE'),
  ('multiprocessing.popen_spawn_win32',
   'C:\\Python313\\Lib\\multiprocessing\\popen_spawn_win32.py',
   'PYMODULE'),
  ('multiprocessing.popen_forkserver',
   'C:\\Python313\\Lib\\multiprocessing\\popen_forkserver.py',
   'PYMODULE'),
  ('multiprocessing.popen_spawn_posix',
   'C:\\Python313\\Lib\\multiprocessing\\popen_spawn_posix.py',
   'PYMODULE'),
  ('multiprocessing.popen_fork',
   'C:\\Python313\\Lib\\multiprocessing\\popen_fork.py',
   'PYMODULE'),
  ('multiprocessing.sharedctypes',
   'C:\\Python313\\Lib\\multiprocessing\\sharedctypes.py',
   'PYMODULE'),
  ('ctypes', 'C:\\Python313\\Lib\\ctypes\\__init__.py', 'PYMODULE'),
  ('ctypes.util', 'C:\\Python313\\Lib\\ctypes\\util.py', 'PYMODULE'),
  ('ctypes._aix', 'C:\\Python313\\Lib\\ctypes\\_aix.py', 'PYMODULE'),
  ('ctypes.macholib.dyld',
   'C:\\Python313\\Lib\\ctypes\\macholib\\dyld.py',
   'PYMODULE'),
  ('ctypes.macholib',
   'C:\\Python313\\Lib\\ctypes\\macholib\\__init__.py',
   'PYMODULE'),
  ('ctypes.macholib.dylib',
   'C:\\Python313\\Lib\\ctypes\\macholib\\dylib.py',
   'PYMODULE'),
  ('ctypes.macholib.framework',
   'C:\\Python313\\Lib\\ctypes\\macholib\\framework.py',
   'PYMODULE'),
  ('ctypes._endian', 'C:\\Python313\\Lib\\ctypes\\_endian.py', 'PYMODULE'),
  ('multiprocessing.pool',
   'C:\\Python313\\Lib\\multiprocessing\\pool.py',
   'PYMODULE'),
  ('multiprocessing.dummy',
   'C:\\Python313\\Lib\\multiprocessing\\dummy\\__init__.py',
   'PYMODULE'),
  ('multiprocessing.dummy.connection',
   'C:\\Python313\\Lib\\multiprocessing\\dummy\\connection.py',
   'PYMODULE'),
  ('multiprocessing.managers',
   'C:\\Python313\\Lib\\multiprocessing\\managers.py',
   'PYMODULE'),
  ('multiprocessing.shared_memory',
   'C:\\Python313\\Lib\\multiprocessing\\shared_memory.py',
   'PYMODULE'),
  ('secrets', 'C:\\Python313\\Lib\\secrets.py', 'PYMODULE'),
  ('hmac', 'C:\\Python313\\Lib\\hmac.py', 'PYMODULE'),
  ('multiprocessing.reduction',
   'C:\\Python313\\Lib\\multiprocessing\\reduction.py',
   'PYMODULE'),
  ('multiprocessing.resource_sharer',
   'C:\\Python313\\Lib\\multiprocessing\\resource_sharer.py',
   'PYMODULE'),
  ('pickle', 'C:\\Python313\\Lib\\pickle.py', 'PYMODULE'),
  ('pprint', 'C:\\Python313\\Lib\\pprint.py', 'PYMODULE'),
  ('dataclasses', 'C:\\Python313\\Lib\\dataclasses.py', 'PYMODULE'),
  ('_compat_pickle', 'C:\\Python313\\Lib\\_compat_pickle.py', 'PYMODULE'),
  ('multiprocessing.queues',
   'C:\\Python313\\Lib\\multiprocessing\\queues.py',
   'PYMODULE'),
  ('multiprocessing.connection',
   'C:\\Python313\\Lib\\multiprocessing\\connection.py',
   'PYMODULE'),
  ('xmlrpc.client', 'C:\\Python313\\Lib\\xmlrpc\\client.py', 'PYMODULE'),
  ('xmlrpc', 'C:\\Python313\\Lib\\xmlrpc\\__init__.py', 'PYMODULE'),
  ('xml.parsers.expat',
   'C:\\Python313\\Lib\\xml\\parsers\\expat.py',
   'PYMODULE'),
  ('xml.parsers', 'C:\\Python313\\Lib\\xml\\parsers\\__init__.py', 'PYMODULE'),
  ('xml', 'C:\\Python313\\Lib\\xml\\__init__.py', 'PYMODULE'),
  ('xml.sax.expatreader',
   'C:\\Python313\\Lib\\xml\\sax\\expatreader.py',
   'PYMODULE'),
  ('xml.sax.saxutils', 'C:\\Python313\\Lib\\xml\\sax\\saxutils.py', 'PYMODULE'),
  ('urllib.request', 'C:\\Python313\\Lib\\urllib\\request.py', 'PYMODULE'),
  ('getpass', 'C:\\Python313\\Lib\\getpass.py', 'PYMODULE'),
  ('nturl2path', 'C:\\Python313\\Lib\\nturl2path.py', 'PYMODULE'),
  ('ftplib', 'C:\\Python313\\Lib\\ftplib.py', 'PYMODULE'),
  ('netrc', 'C:\\Python313\\Lib\\netrc.py', 'PYMODULE'),
  ('mimetypes', 'C:\\Python313\\Lib\\mimetypes.py', 'PYMODULE'),
  ('http.cookiejar', 'C:\\Python313\\Lib\\http\\cookiejar.py', 'PYMODULE'),
  ('http', 'C:\\Python313\\Lib\\http\\__init__.py', 'PYMODULE'),
  ('urllib.response', 'C:\\Python313\\Lib\\urllib\\response.py', 'PYMODULE'),
  ('urllib.error', 'C:\\Python313\\Lib\\urllib\\error.py', 'PYMODULE'),
  ('xml.sax', 'C:\\Python313\\Lib\\xml\\sax\\__init__.py', 'PYMODULE'),
  ('xml.sax.handler', 'C:\\Python313\\Lib\\xml\\sax\\handler.py', 'PYMODULE'),
  ('xml.sax._exceptions',
   'C:\\Python313\\Lib\\xml\\sax\\_exceptions.py',
   'PYMODULE'),
  ('xml.sax.xmlreader',
   'C:\\Python313\\Lib\\xml\\sax\\xmlreader.py',
   'PYMODULE'),
  ('http.client', 'C:\\Python313\\Lib\\http\\client.py', 'PYMODULE'),
  ('multiprocessing',
   'C:\\Python313\\Lib\\multiprocessing\\__init__.py',
   'PYMODULE'),
  ('concurrent.futures._base',
   'C:\\Python313\\Lib\\concurrent\\futures\\_base.py',
   'PYMODULE'),
  ('concurrent', 'C:\\Python313\\Lib\\concurrent\\__init__.py', 'PYMODULE'),
  ('asyncio.trsock', 'C:\\Python313\\Lib\\asyncio\\trsock.py', 'PYMODULE'),
  ('asyncio.staggered',
   'C:\\Python313\\Lib\\asyncio\\staggered.py',
   'PYMODULE'),
  ('asyncio.timeouts', 'C:\\Python313\\Lib\\asyncio\\timeouts.py', 'PYMODULE'),
  ('asyncio.tasks', 'C:\\Python313\\Lib\\asyncio\\tasks.py', 'PYMODULE'),
  ('asyncio.queues', 'C:\\Python313\\Lib\\asyncio\\queues.py', 'PYMODULE'),
  ('asyncio.base_tasks',
   'C:\\Python313\\Lib\\asyncio\\base_tasks.py',
   'PYMODULE'),
  ('asyncio.locks', 'C:\\Python313\\Lib\\asyncio\\locks.py', 'PYMODULE'),
  ('asyncio.mixins', 'C:\\Python313\\Lib\\asyncio\\mixins.py', 'PYMODULE'),
  ('asyncio.sslproto', 'C:\\Python313\\Lib\\asyncio\\sslproto.py', 'PYMODULE'),
  ('asyncio.transports',
   'C:\\Python313\\Lib\\asyncio\\transports.py',
   'PYMODULE'),
  ('asyncio.protocols',
   'C:\\Python313\\Lib\\asyncio\\protocols.py',
   'PYMODULE'),
  ('asyncio.futures', 'C:\\Python313\\Lib\\asyncio\\futures.py', 'PYMODULE'),
  ('asyncio.base_futures',
   'C:\\Python313\\Lib\\asyncio\\base_futures.py',
   'PYMODULE'),
  ('asyncio.exceptions',
   'C:\\Python313\\Lib\\asyncio\\exceptions.py',
   'PYMODULE'),
  ('asyncio.events', 'C:\\Python313\\Lib\\asyncio\\events.py', 'PYMODULE'),
  ('asyncio.format_helpers',
   'C:\\Python313\\Lib\\asyncio\\format_helpers.py',
   'PYMODULE'),
  ('asyncio.constants',
   'C:\\Python313\\Lib\\asyncio\\constants.py',
   'PYMODULE'),
  ('setuptools._vendor.more_itertools',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python313\\site-packages\\setuptools\\_vendor\\more_itertools\\__init__.py',
   'PYMODULE'),
  ('setuptools._vendor', '-', 'PYMODULE'),
  ('setuptools._vendor.more_itertools.recipes',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python313\\site-packages\\setuptools\\_vendor\\more_itertools\\recipes.py',
   'PYMODULE'),
  ('setuptools._vendor.more_itertools.more',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python313\\site-packages\\setuptools\\_vendor\\more_itertools\\more.py',
   'PYMODULE'),
  ('platform', 'C:\\Python313\\Lib\\platform.py', 'PYMODULE'),
  ('_ios_support', 'C:\\Python313\\Lib\\_ios_support.py', 'PYMODULE'),
  ('setuptools._distutils.command.build_ext',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python313\\site-packages\\setuptools\\_distutils\\command\\build_ext.py',
   'PYMODULE'),
  ('setuptools._distutils.command',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python313\\site-packages\\setuptools\\_distutils\\command\\__init__.py',
   'PYMODULE'),
  ('setuptools._distutils',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python313\\site-packages\\setuptools\\_distutils\\__init__.py',
   'PYMODULE'),
  ('setuptools._distutils.version',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python313\\site-packages\\setuptools\\_distutils\\version.py',
   'PYMODULE'),
  ('setuptools._distutils.archive_util',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python313\\site-packages\\setuptools\\_distutils\\archive_util.py',
   'PYMODULE'),
  ('setuptools._distutils.spawn',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python313\\site-packages\\setuptools\\_distutils\\spawn.py',
   'PYMODULE'),
  ('setuptools._distutils.debug',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python313\\site-packages\\setuptools\\_distutils\\debug.py',
   'PYMODULE'),
  ('setuptools._distutils.dir_util',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python313\\site-packages\\setuptools\\_distutils\\dir_util.py',
   'PYMODULE'),
  ('setuptools._distutils.file_util',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python313\\site-packages\\setuptools\\_distutils\\file_util.py',
   'PYMODULE'),
  ('setuptools._distutils._msvccompiler',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python313\\site-packages\\setuptools\\_distutils\\_msvccompiler.py',
   'PYMODULE'),
  ('setuptools._distutils.compilers.C.msvc',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python313\\site-packages\\setuptools\\_distutils\\compilers\\C\\msvc.py',
   'PYMODULE'),
  ('setuptools._distutils.compilers.C.errors',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python313\\site-packages\\setuptools\\_distutils\\compilers\\C\\errors.py',
   'PYMODULE'),
  ('setuptools._distutils.compilers.C.base',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python313\\site-packages\\setuptools\\_distutils\\compilers\\C\\base.py',
   'PYMODULE'),
  ('setuptools._distutils.fancy_getopt',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python313\\site-packages\\setuptools\\_distutils\\fancy_getopt.py',
   'PYMODULE'),
  ('unittest.mock', 'C:\\Python313\\Lib\\unittest\\mock.py', 'PYMODULE'),
  ('unittest', 'C:\\Python313\\Lib\\unittest\\__init__.py', 'PYMODULE'),
  ('unittest.async_case',
   'C:\\Python313\\Lib\\unittest\\async_case.py',
   'PYMODULE'),
  ('unittest.signals', 'C:\\Python313\\Lib\\unittest\\signals.py', 'PYMODULE'),
  ('unittest.main', 'C:\\Python313\\Lib\\unittest\\main.py', 'PYMODULE'),
  ('unittest.runner', 'C:\\Python313\\Lib\\unittest\\runner.py', 'PYMODULE'),
  ('unittest.loader', 'C:\\Python313\\Lib\\unittest\\loader.py', 'PYMODULE'),
  ('unittest.suite', 'C:\\Python313\\Lib\\unittest\\suite.py', 'PYMODULE'),
  ('unittest.case', 'C:\\Python313\\Lib\\unittest\\case.py', 'PYMODULE'),
  ('unittest._log', 'C:\\Python313\\Lib\\unittest\\_log.py', 'PYMODULE'),
  ('difflib', 'C:\\Python313\\Lib\\difflib.py', 'PYMODULE'),
  ('unittest.result', 'C:\\Python313\\Lib\\unittest\\result.py', 'PYMODULE'),
  ('unittest.util', 'C:\\Python313\\Lib\\unittest\\util.py', 'PYMODULE'),
  ('setuptools._distutils.compilers.C', '-', 'PYMODULE'),
  ('setuptools._distutils.compilers', '-', 'PYMODULE'),
  ('setuptools._distutils.util',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python313\\site-packages\\setuptools\\_distutils\\util.py',
   'PYMODULE'),
  ('jaraco', '-', 'PYMODULE'),
  ('setuptools._vendor.jaraco.functools',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python313\\site-packages\\setuptools\\_vendor\\jaraco\\functools\\__init__.py',
   'PYMODULE'),
  ('setuptools._vendor.jaraco', '-', 'PYMODULE'),
  ('sysconfig', 'C:\\Python313\\Lib\\sysconfig\\__init__.py', 'PYMODULE'),
  ('_aix_support', 'C:\\Python313\\Lib\\_aix_support.py', 'PYMODULE'),
  ('setuptools._distutils.sysconfig',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python313\\site-packages\\setuptools\\_distutils\\sysconfig.py',
   'PYMODULE'),
  ('setuptools._distutils.text_file',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python313\\site-packages\\setuptools\\_distutils\\text_file.py',
   'PYMODULE'),
  ('setuptools._distutils.compat.py39',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python313\\site-packages\\setuptools\\_distutils\\compat\\py39.py',
   'PYMODULE'),
  ('setuptools._distutils.compat',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python313\\site-packages\\setuptools\\_distutils\\compat\\__init__.py',
   'PYMODULE'),
  ('setuptools._distutils.extension',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python313\\site-packages\\setuptools\\_distutils\\extension.py',
   'PYMODULE'),
  ('setuptools._distutils.ccompiler',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python313\\site-packages\\setuptools\\_distutils\\ccompiler.py',
   'PYMODULE'),
  ('setuptools._distutils.compat.numpy',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python313\\site-packages\\setuptools\\_distutils\\compat\\numpy.py',
   'PYMODULE'),
  ('setuptools._distutils._modified',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python313\\site-packages\\setuptools\\_distutils\\_modified.py',
   'PYMODULE'),
  ('site', 'C:\\Python313\\Lib\\site.py', 'PYMODULE'),
  ('_pyrepl.main', 'C:\\Python313\\Lib\\_pyrepl\\main.py', 'PYMODULE'),
  ('_pyrepl', 'C:\\Python313\\Lib\\_pyrepl\\__init__.py', 'PYMODULE'),
  ('_pyrepl.curses', 'C:\\Python313\\Lib\\_pyrepl\\curses.py', 'PYMODULE'),
  ('curses', 'C:\\Python313\\Lib\\curses\\__init__.py', 'PYMODULE'),
  ('curses.has_key', 'C:\\Python313\\Lib\\curses\\has_key.py', 'PYMODULE'),
  ('_pyrepl._minimal_curses',
   'C:\\Python313\\Lib\\_pyrepl\\_minimal_curses.py',
   'PYMODULE'),
  ('_pyrepl.input', 'C:\\Python313\\Lib\\_pyrepl\\input.py', 'PYMODULE'),
  ('_pyrepl.keymap', 'C:\\Python313\\Lib\\_pyrepl\\keymap.py', 'PYMODULE'),
  ('_pyrepl.types', 'C:\\Python313\\Lib\\_pyrepl\\types.py', 'PYMODULE'),
  ('_pyrepl.commands', 'C:\\Python313\\Lib\\_pyrepl\\commands.py', 'PYMODULE'),
  ('_pyrepl.pager', 'C:\\Python313\\Lib\\_pyrepl\\pager.py', 'PYMODULE'),
  ('tty', 'C:\\Python313\\Lib\\tty.py', 'PYMODULE'),
  ('_pyrepl.historical_reader',
   'C:\\Python313\\Lib\\_pyrepl\\historical_reader.py',
   'PYMODULE'),
  ('_pyrepl.reader', 'C:\\Python313\\Lib\\_pyrepl\\reader.py', 'PYMODULE'),
  ('_pyrepl._threading_handler',
   'C:\\Python313\\Lib\\_pyrepl\\_threading_handler.py',
   'PYMODULE'),
  ('_pyrepl.utils', 'C:\\Python313\\Lib\\_pyrepl\\utils.py', 'PYMODULE'),
  ('_colorize', 'C:\\Python313\\Lib\\_colorize.py', 'PYMODULE'),
  ('_pyrepl.console', 'C:\\Python313\\Lib\\_pyrepl\\console.py', 'PYMODULE'),
  ('code', 'C:\\Python313\\Lib\\code.py', 'PYMODULE'),
  ('codeop', 'C:\\Python313\\Lib\\codeop.py', 'PYMODULE'),
  ('_pyrepl.trace', 'C:\\Python313\\Lib\\_pyrepl\\trace.py', 'PYMODULE'),
  ('_pyrepl.simple_interact',
   'C:\\Python313\\Lib\\_pyrepl\\simple_interact.py',
   'PYMODULE'),
  ('_pyrepl.unix_console',
   'C:\\Python313\\Lib\\_pyrepl\\unix_console.py',
   'PYMODULE'),
  ('_pyrepl.unix_eventqueue',
   'C:\\Python313\\Lib\\_pyrepl\\unix_eventqueue.py',
   'PYMODULE'),
  ('_pyrepl.fancy_termios',
   'C:\\Python313\\Lib\\_pyrepl\\fancy_termios.py',
   'PYMODULE'),
  ('_pyrepl.windows_console',
   'C:\\Python313\\Lib\\_pyrepl\\windows_console.py',
   'PYMODULE'),
  ('ctypes.wintypes', 'C:\\Python313\\Lib\\ctypes\\wintypes.py', 'PYMODULE'),
  ('_pyrepl.readline', 'C:\\Python313\\Lib\\_pyrepl\\readline.py', 'PYMODULE'),
  ('_pyrepl.completing_reader',
   'C:\\Python313\\Lib\\_pyrepl\\completing_reader.py',
   'PYMODULE'),
  ('rlcompleter', 'C:\\Python313\\Lib\\rlcompleter.py', 'PYMODULE'),
  ('_sitebuiltins', 'C:\\Python313\\Lib\\_sitebuiltins.py', 'PYMODULE'),
  ('pydoc', 'C:\\Python313\\Lib\\pydoc.py', 'PYMODULE'),
  ('webbrowser', 'C:\\Python313\\Lib\\webbrowser.py', 'PYMODULE'),
  ('shlex', 'C:\\Python313\\Lib\\shlex.py', 'PYMODULE'),
  ('http.server', 'C:\\Python313\\Lib\\http\\server.py', 'PYMODULE'),
  ('socketserver', 'C:\\Python313\\Lib\\socketserver.py', 'PYMODULE'),
  ('html', 'C:\\Python313\\Lib\\html\\__init__.py', 'PYMODULE'),
  ('html.entities', 'C:\\Python313\\Lib\\html\\entities.py', 'PYMODULE'),
  ('pydoc_data.topics',
   'C:\\Python313\\Lib\\pydoc_data\\topics.py',
   'PYMODULE'),
  ('pydoc_data', 'C:\\Python313\\Lib\\pydoc_data\\__init__.py', 'PYMODULE'),
  ('setuptools._distutils._log',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python313\\site-packages\\setuptools\\_distutils\\_log.py',
   'PYMODULE'),
  ('setuptools._distutils.errors',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python313\\site-packages\\setuptools\\_distutils\\errors.py',
   'PYMODULE'),
  ('setuptools._distutils.core',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python313\\site-packages\\setuptools\\_distutils\\core.py',
   'PYMODULE'),
  ('setuptools._distutils.dist',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python313\\site-packages\\setuptools\\_distutils\\dist.py',
   'PYMODULE'),
  ('setuptools._distutils.versionpredicate',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python313\\site-packages\\setuptools\\_distutils\\versionpredicate.py',
   'PYMODULE'),
  ('configparser', 'C:\\Python313\\Lib\\configparser.py', 'PYMODULE'),
  ('packaging.utils',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python313\\site-packages\\packaging\\utils.py',
   'PYMODULE'),
  ('packaging',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python313\\site-packages\\packaging\\__init__.py',
   'PYMODULE'),
  ('pkg_resources',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python313\\site-packages\\pkg_resources\\__init__.py',
   'PYMODULE'),
  ('packaging.metadata',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python313\\site-packages\\packaging\\metadata.py',
   'PYMODULE'),
  ('packaging.licenses._spdx',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python313\\site-packages\\packaging\\licenses\\_spdx.py',
   'PYMODULE'),
  ('packaging.licenses',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python313\\site-packages\\packaging\\licenses\\__init__.py',
   'PYMODULE'),
  ('packaging._tokenizer',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python313\\site-packages\\packaging\\_tokenizer.py',
   'PYMODULE'),
  ('packaging._structures',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python313\\site-packages\\packaging\\_structures.py',
   'PYMODULE'),
  ('packaging._parser',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python313\\site-packages\\packaging\\_parser.py',
   'PYMODULE'),
  ('packaging._elffile',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python313\\site-packages\\packaging\\_elffile.py',
   'PYMODULE'),
  ('setuptools._vendor.platformdirs',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python313\\site-packages\\setuptools\\_vendor\\platformdirs\\__init__.py',
   'PYMODULE'),
  ('setuptools._vendor.platformdirs.android',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python313\\site-packages\\setuptools\\_vendor\\platformdirs\\android.py',
   'PYMODULE'),
  ('setuptools._vendor.platformdirs.unix',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python313\\site-packages\\setuptools\\_vendor\\platformdirs\\unix.py',
   'PYMODULE'),
  ('setuptools._vendor.platformdirs.macos',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python313\\site-packages\\setuptools\\_vendor\\platformdirs\\macos.py',
   'PYMODULE'),
  ('setuptools._vendor.platformdirs.windows',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python313\\site-packages\\setuptools\\_vendor\\platformdirs\\windows.py',
   'PYMODULE'),
  ('setuptools._vendor.platformdirs.version',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python313\\site-packages\\setuptools\\_vendor\\platformdirs\\version.py',
   'PYMODULE'),
  ('setuptools._vendor.platformdirs.api',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python313\\site-packages\\setuptools\\_vendor\\platformdirs\\api.py',
   'PYMODULE'),
  ('setuptools._vendor.jaraco.text',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python313\\site-packages\\setuptools\\_vendor\\jaraco\\text\\__init__.py',
   'PYMODULE'),
  ('setuptools._vendor.jaraco.context',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python313\\site-packages\\setuptools\\_vendor\\jaraco\\context.py',
   'PYMODULE'),
  ('setuptools._vendor.backports.tarfile',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python313\\site-packages\\setuptools\\_vendor\\backports\\tarfile\\__init__.py',
   'PYMODULE'),
  ('setuptools._vendor.backports',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python313\\site-packages\\setuptools\\_vendor\\backports\\__init__.py',
   'PYMODULE'),
  ('setuptools._vendor.backports.tarfile.compat.py38',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python313\\site-packages\\setuptools\\_vendor\\backports\\tarfile\\compat\\py38.py',
   'PYMODULE'),
  ('setuptools._vendor.backports.tarfile.compat',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python313\\site-packages\\setuptools\\_vendor\\backports\\tarfile\\compat\\__init__.py',
   'PYMODULE'),
  ('backports',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python313\\site-packages\\setuptools\\_vendor\\backports\\__init__.py',
   'PYMODULE'),
  ('packaging.specifiers',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python313\\site-packages\\packaging\\specifiers.py',
   'PYMODULE'),
  ('packaging.requirements',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python313\\site-packages\\packaging\\requirements.py',
   'PYMODULE'),
  ('packaging.markers',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python313\\site-packages\\packaging\\markers.py',
   'PYMODULE'),
  ('plistlib', 'C:\\Python313\\Lib\\plistlib.py', 'PYMODULE'),
  ('packaging._musllinux',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python313\\site-packages\\packaging\\_musllinux.py',
   'PYMODULE'),
  ('packaging._manylinux',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python313\\site-packages\\packaging\\_manylinux.py',
   'PYMODULE'),
  ('packaging.version',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python313\\site-packages\\packaging\\version.py',
   'PYMODULE'),
  ('packaging.tags',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python313\\site-packages\\packaging\\tags.py',
   'PYMODULE'),
  ('setuptools._distutils.cmd',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python313\\site-packages\\setuptools\\_distutils\\cmd.py',
   'PYMODULE'),
  ('setuptools.warnings',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python313\\site-packages\\setuptools\\warnings.py',
   'PYMODULE'),
  ('setuptools.version',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python313\\site-packages\\setuptools\\version.py',
   'PYMODULE'),
  ('setuptools._importlib',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python313\\site-packages\\setuptools\\_importlib.py',
   'PYMODULE'),
  ('setuptools._vendor.importlib_metadata',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python313\\site-packages\\setuptools\\_vendor\\importlib_metadata\\__init__.py',
   'PYMODULE'),
  ('setuptools._vendor.importlib_metadata._adapters',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python313\\site-packages\\setuptools\\_vendor\\importlib_metadata\\_adapters.py',
   'PYMODULE'),
  ('setuptools._vendor.importlib_metadata._text',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python313\\site-packages\\setuptools\\_vendor\\importlib_metadata\\_text.py',
   'PYMODULE'),
  ('setuptools._vendor.importlib_metadata._itertools',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python313\\site-packages\\setuptools\\_vendor\\importlib_metadata\\_itertools.py',
   'PYMODULE'),
  ('setuptools._vendor.importlib_metadata._functools',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python313\\site-packages\\setuptools\\_vendor\\importlib_metadata\\_functools.py',
   'PYMODULE'),
  ('setuptools._vendor.importlib_metadata._compat',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python313\\site-packages\\setuptools\\_vendor\\importlib_metadata\\_compat.py',
   'PYMODULE'),
  ('setuptools._vendor.importlib_metadata._collections',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python313\\site-packages\\setuptools\\_vendor\\importlib_metadata\\_collections.py',
   'PYMODULE'),
  ('setuptools._vendor.importlib_metadata.compat.py311',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python313\\site-packages\\setuptools\\_vendor\\importlib_metadata\\compat\\py311.py',
   'PYMODULE'),
  ('setuptools._vendor.importlib_metadata.compat.py39',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python313\\site-packages\\setuptools\\_vendor\\importlib_metadata\\compat\\py39.py',
   'PYMODULE'),
  ('setuptools._vendor.importlib_metadata.compat',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python313\\site-packages\\setuptools\\_vendor\\importlib_metadata\\compat\\__init__.py',
   'PYMODULE'),
  ('setuptools._vendor.importlib_metadata._meta',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python313\\site-packages\\setuptools\\_vendor\\importlib_metadata\\_meta.py',
   'PYMODULE'),
  ('setuptools._vendor.zipp',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python313\\site-packages\\setuptools\\_vendor\\zipp\\__init__.py',
   'PYMODULE'),
  ('setuptools._vendor.zipp.glob',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python313\\site-packages\\setuptools\\_vendor\\zipp\\glob.py',
   'PYMODULE'),
  ('setuptools._vendor.zipp.compat.py310',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python313\\site-packages\\setuptools\\_vendor\\zipp\\compat\\py310.py',
   'PYMODULE'),
  ('setuptools._vendor.zipp.compat',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python313\\site-packages\\setuptools\\_vendor\\zipp\\compat\\__init__.py',
   'PYMODULE'),
  ('setuptools.extension',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python313\\site-packages\\setuptools\\extension.py',
   'PYMODULE'),
  ('setuptools._path',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python313\\site-packages\\setuptools\\_path.py',
   'PYMODULE'),
  ('setuptools.dist',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python313\\site-packages\\setuptools\\dist.py',
   'PYMODULE'),
  ('setuptools.command.bdist_wheel',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python313\\site-packages\\setuptools\\command\\bdist_wheel.py',
   'PYMODULE'),
  ('setuptools._vendor.wheel.macosx_libfile',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python313\\site-packages\\setuptools\\_vendor\\wheel\\macosx_libfile.py',
   'PYMODULE'),
  ('setuptools._vendor.wheel',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python313\\site-packages\\setuptools\\_vendor\\wheel\\__init__.py',
   'PYMODULE'),
  ('setuptools.command.egg_info',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python313\\site-packages\\setuptools\\command\\egg_info.py',
   'PYMODULE'),
  ('setuptools._distutils.filelist',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python313\\site-packages\\setuptools\\_distutils\\filelist.py',
   'PYMODULE'),
  ('setuptools.command._requirestxt',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python313\\site-packages\\setuptools\\command\\_requirestxt.py',
   'PYMODULE'),
  ('setuptools.glob',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python313\\site-packages\\setuptools\\glob.py',
   'PYMODULE'),
  ('setuptools.command.setopt',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python313\\site-packages\\setuptools\\command\\setopt.py',
   'PYMODULE'),
  ('setuptools.command.sdist',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python313\\site-packages\\setuptools\\command\\sdist.py',
   'PYMODULE'),
  ('setuptools._distutils.command.sdist',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python313\\site-packages\\setuptools\\_distutils\\command\\sdist.py',
   'PYMODULE'),
  ('setuptools.command.build',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python313\\site-packages\\setuptools\\command\\build.py',
   'PYMODULE'),
  ('setuptools._distutils.command.build',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python313\\site-packages\\setuptools\\_distutils\\command\\build.py',
   'PYMODULE'),
  ('setuptools.command.bdist_egg',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python313\\site-packages\\setuptools\\command\\bdist_egg.py',
   'PYMODULE'),
  ('setuptools.unicode_utils',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python313\\site-packages\\setuptools\\unicode_utils.py',
   'PYMODULE'),
  ('setuptools.compat.py39',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python313\\site-packages\\setuptools\\compat\\py39.py',
   'PYMODULE'),
  ('setuptools.compat',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python313\\site-packages\\setuptools\\compat\\__init__.py',
   'PYMODULE'),
  ('setuptools.compat.py311',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python313\\site-packages\\setuptools\\compat\\py311.py',
   'PYMODULE'),
  ('setuptools._vendor.wheel.wheelfile',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python313\\site-packages\\setuptools\\_vendor\\wheel\\wheelfile.py',
   'PYMODULE'),
  ('setuptools._vendor.wheel.util',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python313\\site-packages\\setuptools\\_vendor\\wheel\\util.py',
   'PYMODULE'),
  ('setuptools._vendor.wheel.cli',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python313\\site-packages\\setuptools\\_vendor\\wheel\\cli\\__init__.py',
   'PYMODULE'),
  ('setuptools._vendor.wheel.cli.tags',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python313\\site-packages\\setuptools\\_vendor\\wheel\\cli\\tags.py',
   'PYMODULE'),
  ('setuptools._vendor.wheel.cli.convert',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python313\\site-packages\\setuptools\\_vendor\\wheel\\cli\\convert.py',
   'PYMODULE'),
  ('setuptools._vendor.wheel.vendored.packaging.tags',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python313\\site-packages\\setuptools\\_vendor\\wheel\\vendored\\packaging\\tags.py',
   'PYMODULE'),
  ('setuptools._vendor.wheel.vendored.packaging._musllinux',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python313\\site-packages\\setuptools\\_vendor\\wheel\\vendored\\packaging\\_musllinux.py',
   'PYMODULE'),
  ('setuptools._vendor.wheel.vendored.packaging._elffile',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python313\\site-packages\\setuptools\\_vendor\\wheel\\vendored\\packaging\\_elffile.py',
   'PYMODULE'),
  ('setuptools._vendor.wheel.vendored.packaging._manylinux',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python313\\site-packages\\setuptools\\_vendor\\wheel\\vendored\\packaging\\_manylinux.py',
   'PYMODULE'),
  ('setuptools._vendor.wheel.vendored.packaging',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python313\\site-packages\\setuptools\\_vendor\\wheel\\vendored\\packaging\\__init__.py',
   'PYMODULE'),
  ('setuptools._vendor.wheel.vendored',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python313\\site-packages\\setuptools\\_vendor\\wheel\\vendored\\__init__.py',
   'PYMODULE'),
  ('setuptools._vendor.wheel.metadata',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python313\\site-packages\\setuptools\\_vendor\\wheel\\metadata.py',
   'PYMODULE'),
  ('setuptools._vendor.wheel.vendored.packaging.requirements',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python313\\site-packages\\setuptools\\_vendor\\wheel\\vendored\\packaging\\requirements.py',
   'PYMODULE'),
  ('setuptools._vendor.wheel.vendored.packaging.utils',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python313\\site-packages\\setuptools\\_vendor\\wheel\\vendored\\packaging\\utils.py',
   'PYMODULE'),
  ('setuptools._vendor.wheel.vendored.packaging.version',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python313\\site-packages\\setuptools\\_vendor\\wheel\\vendored\\packaging\\version.py',
   'PYMODULE'),
  ('setuptools._vendor.wheel.vendored.packaging._structures',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python313\\site-packages\\setuptools\\_vendor\\wheel\\vendored\\packaging\\_structures.py',
   'PYMODULE'),
  ('setuptools._vendor.wheel.vendored.packaging.specifiers',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python313\\site-packages\\setuptools\\_vendor\\wheel\\vendored\\packaging\\specifiers.py',
   'PYMODULE'),
  ('setuptools._vendor.wheel.vendored.packaging.markers',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python313\\site-packages\\setuptools\\_vendor\\wheel\\vendored\\packaging\\markers.py',
   'PYMODULE'),
  ('setuptools._vendor.wheel.vendored.packaging._tokenizer',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python313\\site-packages\\setuptools\\_vendor\\wheel\\vendored\\packaging\\_tokenizer.py',
   'PYMODULE'),
  ('setuptools._vendor.wheel.vendored.packaging._parser',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python313\\site-packages\\setuptools\\_vendor\\wheel\\vendored\\packaging\\_parser.py',
   'PYMODULE'),
  ('setuptools._vendor.wheel.cli.pack',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python313\\site-packages\\setuptools\\_vendor\\wheel\\cli\\pack.py',
   'PYMODULE'),
  ('setuptools._vendor.wheel.cli.unpack',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python313\\site-packages\\setuptools\\_vendor\\wheel\\cli\\unpack.py',
   'PYMODULE'),
  ('setuptools.installer',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python313\\site-packages\\setuptools\\installer.py',
   'PYMODULE'),
  ('setuptools.wheel',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python313\\site-packages\\setuptools\\wheel.py',
   'PYMODULE'),
  ('setuptools.archive_util',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python313\\site-packages\\setuptools\\archive_util.py',
   'PYMODULE'),
  ('setuptools._distutils.log',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python313\\site-packages\\setuptools\\_distutils\\log.py',
   'PYMODULE'),
  ('setuptools.errors',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python313\\site-packages\\setuptools\\errors.py',
   'PYMODULE'),
  ('setuptools.config.setupcfg',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python313\\site-packages\\setuptools\\config\\setupcfg.py',
   'PYMODULE'),
  ('setuptools.config.expand',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python313\\site-packages\\setuptools\\config\\expand.py',
   'PYMODULE'),
  ('setuptools.config.pyprojecttoml',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python313\\site-packages\\setuptools\\config\\pyprojecttoml.py',
   'PYMODULE'),
  ('setuptools.config._validate_pyproject',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python313\\site-packages\\setuptools\\config\\_validate_pyproject\\__init__.py',
   'PYMODULE'),
  ('setuptools.config._validate_pyproject.fastjsonschema_validations',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python313\\site-packages\\setuptools\\config\\_validate_pyproject\\fastjsonschema_validations.py',
   'PYMODULE'),
  ('setuptools.config._validate_pyproject.fastjsonschema_exceptions',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python313\\site-packages\\setuptools\\config\\_validate_pyproject\\fastjsonschema_exceptions.py',
   'PYMODULE'),
  ('setuptools.config._validate_pyproject.extra_validations',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python313\\site-packages\\setuptools\\config\\_validate_pyproject\\extra_validations.py',
   'PYMODULE'),
  ('setuptools.config._validate_pyproject.error_reporting',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python313\\site-packages\\setuptools\\config\\_validate_pyproject\\error_reporting.py',
   'PYMODULE'),
  ('setuptools.config._validate_pyproject.formats',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python313\\site-packages\\setuptools\\config\\_validate_pyproject\\formats.py',
   'PYMODULE'),
  ('setuptools._vendor.packaging.requirements',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python313\\site-packages\\setuptools\\_vendor\\packaging\\requirements.py',
   'PYMODULE'),
  ('setuptools._vendor.packaging.utils',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python313\\site-packages\\setuptools\\_vendor\\packaging\\utils.py',
   'PYMODULE'),
  ('setuptools._vendor.packaging.version',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python313\\site-packages\\setuptools\\_vendor\\packaging\\version.py',
   'PYMODULE'),
  ('setuptools._vendor.packaging._structures',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python313\\site-packages\\setuptools\\_vendor\\packaging\\_structures.py',
   'PYMODULE'),
  ('setuptools._vendor.packaging.tags',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python313\\site-packages\\setuptools\\_vendor\\packaging\\tags.py',
   'PYMODULE'),
  ('setuptools._vendor.packaging._musllinux',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python313\\site-packages\\setuptools\\_vendor\\packaging\\_musllinux.py',
   'PYMODULE'),
  ('setuptools._vendor.packaging._elffile',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python313\\site-packages\\setuptools\\_vendor\\packaging\\_elffile.py',
   'PYMODULE'),
  ('setuptools._vendor.packaging._manylinux',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python313\\site-packages\\setuptools\\_vendor\\packaging\\_manylinux.py',
   'PYMODULE'),
  ('setuptools._vendor.packaging.specifiers',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python313\\site-packages\\setuptools\\_vendor\\packaging\\specifiers.py',
   'PYMODULE'),
  ('setuptools._vendor.packaging.markers',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python313\\site-packages\\setuptools\\_vendor\\packaging\\markers.py',
   'PYMODULE'),
  ('setuptools._vendor.packaging._tokenizer',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python313\\site-packages\\setuptools\\_vendor\\packaging\\_tokenizer.py',
   'PYMODULE'),
  ('setuptools._vendor.packaging._parser',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python313\\site-packages\\setuptools\\_vendor\\packaging\\_parser.py',
   'PYMODULE'),
  ('setuptools._vendor.packaging',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python313\\site-packages\\setuptools\\_vendor\\packaging\\__init__.py',
   'PYMODULE'),
  ('setuptools.compat.py310',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python313\\site-packages\\setuptools\\compat\\py310.py',
   'PYMODULE'),
  ('setuptools._vendor.tomli',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python313\\site-packages\\setuptools\\_vendor\\tomli\\__init__.py',
   'PYMODULE'),
  ('setuptools._vendor.tomli._parser',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python313\\site-packages\\setuptools\\_vendor\\tomli\\_parser.py',
   'PYMODULE'),
  ('setuptools._vendor.tomli._types',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python313\\site-packages\\setuptools\\_vendor\\tomli\\_types.py',
   'PYMODULE'),
  ('setuptools._vendor.tomli._re',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python313\\site-packages\\setuptools\\_vendor\\tomli\\_re.py',
   'PYMODULE'),
  ('tomllib', 'C:\\Python313\\Lib\\tomllib\\__init__.py', 'PYMODULE'),
  ('tomllib._parser', 'C:\\Python313\\Lib\\tomllib\\_parser.py', 'PYMODULE'),
  ('tomllib._types', 'C:\\Python313\\Lib\\tomllib\\_types.py', 'PYMODULE'),
  ('tomllib._re', 'C:\\Python313\\Lib\\tomllib\\_re.py', 'PYMODULE'),
  ('setuptools.config._apply_pyprojecttoml',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python313\\site-packages\\setuptools\\config\\_apply_pyprojecttoml.py',
   'PYMODULE'),
  ('setuptools.config',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python313\\site-packages\\setuptools\\config\\__init__.py',
   'PYMODULE'),
  ('setuptools._static',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python313\\site-packages\\setuptools\\_static.py',
   'PYMODULE'),
  ('setuptools._shutil',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python313\\site-packages\\setuptools\\_shutil.py',
   'PYMODULE'),
  ('setuptools.windows_support',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python313\\site-packages\\setuptools\\windows_support.py',
   'PYMODULE'),
  ('setuptools.command',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python313\\site-packages\\setuptools\\command\\__init__.py',
   'PYMODULE'),
  ('setuptools._distutils.command.bdist',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python313\\site-packages\\setuptools\\_distutils\\command\\bdist.py',
   'PYMODULE'),
  ('setuptools._entry_points',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python313\\site-packages\\setuptools\\_entry_points.py',
   'PYMODULE'),
  ('setuptools._itertools',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python313\\site-packages\\setuptools\\_itertools.py',
   'PYMODULE'),
  ('setuptools.discovery',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python313\\site-packages\\setuptools\\discovery.py',
   'PYMODULE'),
  ('setuptools.depends',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python313\\site-packages\\setuptools\\depends.py',
   'PYMODULE'),
  ('setuptools._imp',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python313\\site-packages\\setuptools\\_imp.py',
   'PYMODULE'),
  ('setuptools.logging',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python313\\site-packages\\setuptools\\logging.py',
   'PYMODULE'),
  ('setuptools.monkey',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python313\\site-packages\\setuptools\\monkey.py',
   'PYMODULE'),
  ('setuptools._core_metadata',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python313\\site-packages\\setuptools\\_core_metadata.py',
   'PYMODULE'),
  ('setuptools._reqs',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python313\\site-packages\\setuptools\\_reqs.py',
   'PYMODULE'),
  ('setuptools._normalization',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python313\\site-packages\\setuptools\\_normalization.py',
   'PYMODULE'),
  ('_distutils_hack.override',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python313\\site-packages\\_distutils_hack\\override.py',
   'PYMODULE'),
  ('gui.window_utils',
   'E:\\YC\\每日任务\\2025\\07\\0731\\北斗网格转换\\gui\\window_utils.py',
   'PYMODULE'),
  ('gui.progress_dialog',
   'E:\\YC\\每日任务\\2025\\07\\0731\\北斗网格转换\\gui\\progress_dialog.py',
   'PYMODULE'),
  ('gui.parameter_panel',
   'E:\\YC\\每日任务\\2025\\07\\0731\\北斗网格转换\\gui\\parameter_panel.py',
   'PYMODULE'),
  ('gui', 'E:\\YC\\每日任务\\2025\\07\\0731\\北斗网格转换\\gui\\__init__.py', 'PYMODULE'),
  ('data.vector_io',
   'E:\\YC\\每日任务\\2025\\07\\0731\\北斗网格转换\\data\\vector_io.py',
   'PYMODULE'),
  ('data.raster_io',
   'E:\\YC\\每日任务\\2025\\07\\0731\\北斗网格转换\\data\\raster_io.py',
   'PYMODULE'),
  ('rasterio.enums',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python313\\site-packages\\rasterio\\enums.py',
   'PYMODULE'),
  ('data.data_validator',
   'E:\\YC\\每日任务\\2025\\07\\0731\\北斗网格转换\\data\\data_validator.py',
   'PYMODULE'),
  ('data',
   'E:\\YC\\每日任务\\2025\\07\\0731\\北斗网格转换\\data\\__init__.py',
   'PYMODULE'),
  ('core.grid_calculator',
   'E:\\YC\\每日任务\\2025\\07\\0731\\北斗网格转换\\core\\grid_calculator.py',
   'PYMODULE'),
  ('core.coordinate_utils',
   'E:\\YC\\每日任务\\2025\\07\\0731\\北斗网格转换\\core\\coordinate_utils.py',
   'PYMODULE'),
  ('core',
   'E:\\YC\\每日任务\\2025\\07\\0731\\北斗网格转换\\core\\__init__.py',
   'PYMODULE'),
  ('tkinter.messagebox',
   'C:\\Python313\\Lib\\tkinter\\messagebox.py',
   'PYMODULE'),
  ('tkinter.commondialog',
   'C:\\Python313\\Lib\\tkinter\\commondialog.py',
   'PYMODULE'),
  ('tkinter.filedialog',
   'C:\\Python313\\Lib\\tkinter\\filedialog.py',
   'PYMODULE'),
  ('tkinter.simpledialog',
   'C:\\Python313\\Lib\\tkinter\\simpledialog.py',
   'PYMODULE'),
  ('tkinter.dialog', 'C:\\Python313\\Lib\\tkinter\\dialog.py', 'PYMODULE'),
  ('tkinter.ttk', 'C:\\Python313\\Lib\\tkinter\\ttk.py', 'PYMODULE'),
  ('tkinter', 'C:\\Python313\\Lib\\tkinter\\__init__.py', 'PYMODULE'),
  ('tkinter.constants',
   'C:\\Python313\\Lib\\tkinter\\constants.py',
   'PYMODULE'),
  ('scipy.interpolate',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python313\\site-packages\\scipy\\interpolate\\__init__.py',
   'PYMODULE'),
  ('scipy._lib._testutils',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python313\\site-packages\\scipy\\_lib\\_testutils.py',
   'PYMODULE'),
  ('psutil',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python313\\site-packages\\psutil\\__init__.py',
   'PYMODULE'),
  ('psutil._pswindows',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python313\\site-packages\\psutil\\_pswindows.py',
   'PYMODULE'),
  ('psutil._compat',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python313\\site-packages\\psutil\\_compat.py',
   'PYMODULE'),
  ('psutil._common',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python313\\site-packages\\psutil\\_common.py',
   'PYMODULE'),
  ('scipy._lib._pep440',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python313\\site-packages\\scipy\\_lib\\_pep440.py',
   'PYMODULE'),
  ('scipy._lib',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python313\\site-packages\\scipy\\_lib\\__init__.py',
   'PYMODULE'),
  ('scipy._lib.doccer',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python313\\site-packages\\scipy\\_lib\\doccer.py',
   'PYMODULE'),
  ('scipy._lib.array_api_compat',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python313\\site-packages\\scipy\\_lib\\array_api_compat\\__init__.py',
   'PYMODULE'),
  ('scipy._lib.array_api_compat.common',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python313\\site-packages\\scipy\\_lib\\array_api_compat\\common\\__init__.py',
   'PYMODULE'),
  ('scipy._lib.array_api_compat.common._fft',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python313\\site-packages\\scipy\\_lib\\array_api_compat\\common\\_fft.py',
   'PYMODULE'),
  ('scipy._lib.array_api_compat.common._typing',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python313\\site-packages\\scipy\\_lib\\array_api_compat\\common\\_typing.py',
   'PYMODULE'),
  ('scipy._lib.array_api_compat.common._helpers',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python313\\site-packages\\scipy\\_lib\\array_api_compat\\common\\_helpers.py',
   'PYMODULE'),
  ('scipy._lib.array_api_compat.dask.array',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python313\\site-packages\\scipy\\_lib\\array_api_compat\\dask\\array\\__init__.py',
   'PYMODULE'),
  ('scipy._lib.array_api_compat.dask.array._aliases',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python313\\site-packages\\scipy\\_lib\\array_api_compat\\dask\\array\\_aliases.py',
   'PYMODULE'),
  ('scipy._lib.array_api_compat.dask.array._info',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python313\\site-packages\\scipy\\_lib\\array_api_compat\\dask\\array\\_info.py',
   'PYMODULE'),
  ('scipy._lib.array_api_compat._internal',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python313\\site-packages\\scipy\\_lib\\array_api_compat\\_internal.py',
   'PYMODULE'),
  ('scipy._lib.array_api_compat.dask',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python313\\site-packages\\scipy\\_lib\\array_api_compat\\dask\\__init__.py',
   'PYMODULE'),
  ('numpy.typing',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python313\\site-packages\\numpy\\typing\\__init__.py',
   'PYMODULE'),
  ('numpy._pytesttester',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python313\\site-packages\\numpy\\_pytesttester.py',
   'PYMODULE'),
  ('numpy.testing',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python313\\site-packages\\numpy\\testing\\__init__.py',
   'PYMODULE'),
  ('numpy.testing.overrides',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python313\\site-packages\\numpy\\testing\\overrides.py',
   'PYMODULE'),
  ('numpy.lib.recfunctions',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python313\\site-packages\\numpy\\lib\\recfunctions.py',
   'PYMODULE'),
  ('numpy.lib._iotools',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python313\\site-packages\\numpy\\lib\\_iotools.py',
   'PYMODULE'),
  ('numpy._utils',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python313\\site-packages\\numpy\\_utils\\__init__.py',
   'PYMODULE'),
  ('numpy._utils._convertions',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python313\\site-packages\\numpy\\_utils\\_convertions.py',
   'PYMODULE'),
  ('numpy._core.numeric',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python313\\site-packages\\numpy\\_core\\numeric.py',
   'PYMODULE'),
  ('numpy._core._asarray',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python313\\site-packages\\numpy\\_core\\_asarray.py',
   'PYMODULE'),
  ('numpy._core.arrayprint',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python313\\site-packages\\numpy\\_core\\arrayprint.py',
   'PYMODULE'),
  ('numpy._core.printoptions',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python313\\site-packages\\numpy\\_core\\printoptions.py',
   'PYMODULE'),
  ('numpy._core.fromnumeric',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python313\\site-packages\\numpy\\_core\\fromnumeric.py',
   'PYMODULE'),
  ('numpy._core._methods',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python313\\site-packages\\numpy\\_core\\_methods.py',
   'PYMODULE'),
  ('numpy.lib._stride_tricks_impl',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python313\\site-packages\\numpy\\lib\\_stride_tricks_impl.py',
   'PYMODULE'),
  ('numpy._globals',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python313\\site-packages\\numpy\\_globals.py',
   'PYMODULE'),
  ('numpy._core._exceptions',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python313\\site-packages\\numpy\\_core\\_exceptions.py',
   'PYMODULE'),
  ('numpy._core._ufunc_config',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python313\\site-packages\\numpy\\_core\\_ufunc_config.py',
   'PYMODULE'),
  ('numpy.exceptions',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python313\\site-packages\\numpy\\exceptions.py',
   'PYMODULE'),
  ('numpy._core.shape_base',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python313\\site-packages\\numpy\\_core\\shape_base.py',
   'PYMODULE'),
  ('numpy._core.numerictypes',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python313\\site-packages\\numpy\\_core\\numerictypes.py',
   'PYMODULE'),
  ('numpy._core._dtype',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python313\\site-packages\\numpy\\_core\\_dtype.py',
   'PYMODULE'),
  ('numpy._core._type_aliases',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python313\\site-packages\\numpy\\_core\\_type_aliases.py',
   'PYMODULE'),
  ('numpy._core._string_helpers',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python313\\site-packages\\numpy\\_core\\_string_helpers.py',
   'PYMODULE'),
  ('numpy._core.multiarray',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python313\\site-packages\\numpy\\_core\\multiarray.py',
   'PYMODULE'),
  ('numpy._core',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python313\\site-packages\\numpy\\_core\\__init__.py',
   'PYMODULE'),
  ('numpy._core._internal',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python313\\site-packages\\numpy\\_core\\_internal.py',
   'PYMODULE'),
  ('numpy._core._dtype_ctypes',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python313\\site-packages\\numpy\\_core\\_dtype_ctypes.py',
   'PYMODULE'),
  ('numpy._core._add_newdocs_scalars',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python313\\site-packages\\numpy\\_core\\_add_newdocs_scalars.py',
   'PYMODULE'),
  ('numpy._core._add_newdocs',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python313\\site-packages\\numpy\\_core\\_add_newdocs.py',
   'PYMODULE'),
  ('numpy._core.einsumfunc',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python313\\site-packages\\numpy\\_core\\einsumfunc.py',
   'PYMODULE'),
  ('numpy._core._machar',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python313\\site-packages\\numpy\\_core\\_machar.py',
   'PYMODULE'),
  ('numpy._core.function_base',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python313\\site-packages\\numpy\\_core\\function_base.py',
   'PYMODULE'),
  ('numpy._core.memmap',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python313\\site-packages\\numpy\\_core\\memmap.py',
   'PYMODULE'),
  ('numpy._core.records',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python313\\site-packages\\numpy\\_core\\records.py',
   'PYMODULE'),
  ('numpy._core.getlimits',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python313\\site-packages\\numpy\\_core\\getlimits.py',
   'PYMODULE'),
  ('numpy.version',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python313\\site-packages\\numpy\\version.py',
   'PYMODULE'),
  ('numpy.ma.mrecords',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python313\\site-packages\\numpy\\ma\\mrecords.py',
   'PYMODULE'),
  ('numpy.ma',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python313\\site-packages\\numpy\\ma\\__init__.py',
   'PYMODULE'),
  ('numpy.ma.extras',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python313\\site-packages\\numpy\\ma\\extras.py',
   'PYMODULE'),
  ('numpy.lib._index_tricks_impl',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python313\\site-packages\\numpy\\lib\\_index_tricks_impl.py',
   'PYMODULE'),
  ('numpy.lib.stride_tricks',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python313\\site-packages\\numpy\\lib\\stride_tricks.py',
   'PYMODULE'),
  ('numpy.matrixlib',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python313\\site-packages\\numpy\\matrixlib\\__init__.py',
   'PYMODULE'),
  ('numpy.matrixlib.defmatrix',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python313\\site-packages\\numpy\\matrixlib\\defmatrix.py',
   'PYMODULE'),
  ('numpy.linalg',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python313\\site-packages\\numpy\\linalg\\__init__.py',
   'PYMODULE'),
  ('numpy.linalg.linalg',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python313\\site-packages\\numpy\\linalg\\linalg.py',
   'PYMODULE'),
  ('numpy.linalg._linalg',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python313\\site-packages\\numpy\\linalg\\_linalg.py',
   'PYMODULE'),
  ('numpy.lib._twodim_base_impl',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python313\\site-packages\\numpy\\lib\\_twodim_base_impl.py',
   'PYMODULE'),
  ('numpy._typing._ufunc',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python313\\site-packages\\numpy\\_typing\\_ufunc.py',
   'PYMODULE'),
  ('numpy.lib._function_base_impl',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python313\\site-packages\\numpy\\lib\\_function_base_impl.py',
   'PYMODULE'),
  ('numpy.lib._histograms_impl',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python313\\site-packages\\numpy\\lib\\_histograms_impl.py',
   'PYMODULE'),
  ('numpy.lib.array_utils',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python313\\site-packages\\numpy\\lib\\array_utils.py',
   'PYMODULE'),
  ('numpy.lib._array_utils_impl',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python313\\site-packages\\numpy\\lib\\_array_utils_impl.py',
   'PYMODULE'),
  ('numpy.ma.core',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python313\\site-packages\\numpy\\ma\\core.py',
   'PYMODULE'),
  ('numpy._utils._inspect',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python313\\site-packages\\numpy\\_utils\\_inspect.py',
   'PYMODULE'),
  ('numpy.lib',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python313\\site-packages\\numpy\\lib\\__init__.py',
   'PYMODULE'),
  ('numpy.lib._version',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python313\\site-packages\\numpy\\lib\\_version.py',
   'PYMODULE'),
  ('numpy.lib._arraypad_impl',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python313\\site-packages\\numpy\\lib\\_arraypad_impl.py',
   'PYMODULE'),
  ('numpy.lib._arrayterator_impl',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python313\\site-packages\\numpy\\lib\\_arrayterator_impl.py',
   'PYMODULE'),
  ('numpy.lib._npyio_impl',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python313\\site-packages\\numpy\\lib\\_npyio_impl.py',
   'PYMODULE'),
  ('numpy.lib._datasource',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python313\\site-packages\\numpy\\lib\\_datasource.py',
   'PYMODULE'),
  ('numpy.lib._polynomial_impl',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python313\\site-packages\\numpy\\lib\\_polynomial_impl.py',
   'PYMODULE'),
  ('numpy.lib._arraysetops_impl',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python313\\site-packages\\numpy\\lib\\_arraysetops_impl.py',
   'PYMODULE'),
  ('numpy.lib._utils_impl',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python313\\site-packages\\numpy\\lib\\_utils_impl.py',
   'PYMODULE'),
  ('numpy.lib._ufunclike_impl',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python313\\site-packages\\numpy\\lib\\_ufunclike_impl.py',
   'PYMODULE'),
  ('numpy.lib._shape_base_impl',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python313\\site-packages\\numpy\\lib\\_shape_base_impl.py',
   'PYMODULE'),
  ('numpy.lib._nanfunctions_impl',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python313\\site-packages\\numpy\\lib\\_nanfunctions_impl.py',
   'PYMODULE'),
  ('numpy.lib._type_check_impl',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python313\\site-packages\\numpy\\lib\\_type_check_impl.py',
   'PYMODULE'),
  ('numpy.lib.scimath',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python313\\site-packages\\numpy\\lib\\scimath.py',
   'PYMODULE'),
  ('numpy.lib._scimath_impl',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python313\\site-packages\\numpy\\lib\\_scimath_impl.py',
   'PYMODULE'),
  ('numpy.lib.npyio',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python313\\site-packages\\numpy\\lib\\npyio.py',
   'PYMODULE'),
  ('numpy.lib.format',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python313\\site-packages\\numpy\\lib\\format.py',
   'PYMODULE'),
  ('numpy.lib.mixins',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python313\\site-packages\\numpy\\lib\\mixins.py',
   'PYMODULE'),
  ('numpy.lib.introspect',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python313\\site-packages\\numpy\\lib\\introspect.py',
   'PYMODULE'),
  ('numpy._core.umath',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python313\\site-packages\\numpy\\_core\\umath.py',
   'PYMODULE'),
  ('numpy._core.overrides',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python313\\site-packages\\numpy\\_core\\overrides.py',
   'PYMODULE'),
  ('numpy.testing._private.extbuild',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python313\\site-packages\\numpy\\testing\\_private\\extbuild.py',
   'PYMODULE'),
  ('numpy.testing._private.utils',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python313\\site-packages\\numpy\\testing\\_private\\utils.py',
   'PYMODULE'),
  ('doctest', 'C:\\Python313\\Lib\\doctest.py', 'PYMODULE'),
  ('pdb', 'C:\\Python313\\Lib\\pdb.py', 'PYMODULE'),
  ('bdb', 'C:\\Python313\\Lib\\bdb.py', 'PYMODULE'),
  ('cmd', 'C:\\Python313\\Lib\\cmd.py', 'PYMODULE'),
  ('numpy._core.tests._natype',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python313\\site-packages\\numpy\\_core\\tests\\_natype.py',
   'PYMODULE'),
  ('numpy._core.tests', '-', 'PYMODULE'),
  ('numpy.testing._private',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python313\\site-packages\\numpy\\testing\\_private\\__init__.py',
   'PYMODULE'),
  ('numpy._typing._add_docstring',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python313\\site-packages\\numpy\\_typing\\_add_docstring.py',
   'PYMODULE'),
  ('numpy._typing._array_like',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python313\\site-packages\\numpy\\_typing\\_array_like.py',
   'PYMODULE'),
  ('numpy._typing._shape',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python313\\site-packages\\numpy\\_typing\\_shape.py',
   'PYMODULE'),
  ('numpy._typing._nested_sequence',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python313\\site-packages\\numpy\\_typing\\_nested_sequence.py',
   'PYMODULE'),
  ('numpy._typing._nbit_base',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python313\\site-packages\\numpy\\_typing\\_nbit_base.py',
   'PYMODULE'),
  ('numpy._typing',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python313\\site-packages\\numpy\\_typing\\__init__.py',
   'PYMODULE'),
  ('numpy._typing._dtype_like',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python313\\site-packages\\numpy\\_typing\\_dtype_like.py',
   'PYMODULE'),
  ('numpy._typing._scalars',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python313\\site-packages\\numpy\\_typing\\_scalars.py',
   'PYMODULE'),
  ('numpy._typing._char_codes',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python313\\site-packages\\numpy\\_typing\\_char_codes.py',
   'PYMODULE'),
  ('numpy._typing._nbit',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python313\\site-packages\\numpy\\_typing\\_nbit.py',
   'PYMODULE'),
  ('scipy._lib.array_api_compat.common._linalg',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python313\\site-packages\\scipy\\_lib\\array_api_compat\\common\\_linalg.py',
   'PYMODULE'),
  ('numpy.core.numeric',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python313\\site-packages\\numpy\\core\\numeric.py',
   'PYMODULE'),
  ('numpy.core',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python313\\site-packages\\numpy\\core\\__init__.py',
   'PYMODULE'),
  ('numpy.core._utils',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python313\\site-packages\\numpy\\core\\_utils.py',
   'PYMODULE'),
  ('scipy._lib.array_api_compat.common._aliases',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python313\\site-packages\\scipy\\_lib\\array_api_compat\\common\\_aliases.py',
   'PYMODULE'),
  ('scipy._lib.array_api_compat.torch',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python313\\site-packages\\scipy\\_lib\\array_api_compat\\torch\\__init__.py',
   'PYMODULE'),
  ('scipy._lib.array_api_compat.torch._aliases',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python313\\site-packages\\scipy\\_lib\\array_api_compat\\torch\\_aliases.py',
   'PYMODULE'),
  ('scipy._lib.array_api_compat.torch._typing',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python313\\site-packages\\scipy\\_lib\\array_api_compat\\torch\\_typing.py',
   'PYMODULE'),
  ('scipy._lib.array_api_compat.torch._info',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python313\\site-packages\\scipy\\_lib\\array_api_compat\\torch\\_info.py',
   'PYMODULE'),
  ('scipy._lib.array_api_compat.cupy',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python313\\site-packages\\scipy\\_lib\\array_api_compat\\cupy\\__init__.py',
   'PYMODULE'),
  ('scipy._lib.array_api_compat.cupy._aliases',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python313\\site-packages\\scipy\\_lib\\array_api_compat\\cupy\\_aliases.py',
   'PYMODULE'),
  ('scipy._lib.array_api_compat.cupy._typing',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python313\\site-packages\\scipy\\_lib\\array_api_compat\\cupy\\_typing.py',
   'PYMODULE'),
  ('scipy._lib.array_api_compat.cupy._info',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python313\\site-packages\\scipy\\_lib\\array_api_compat\\cupy\\_info.py',
   'PYMODULE'),
  ('scipy._lib.array_api_compat.numpy',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python313\\site-packages\\scipy\\_lib\\array_api_compat\\numpy\\__init__.py',
   'PYMODULE'),
  ('scipy._lib.array_api_compat.numpy.linalg',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python313\\site-packages\\scipy\\_lib\\array_api_compat\\numpy\\linalg.py',
   'PYMODULE'),
  ('scipy._lib.array_api_compat.numpy._typing',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python313\\site-packages\\scipy\\_lib\\array_api_compat\\numpy\\_typing.py',
   'PYMODULE'),
  ('scipy._lib.array_api_compat.numpy._aliases',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python313\\site-packages\\scipy\\_lib\\array_api_compat\\numpy\\_aliases.py',
   'PYMODULE'),
  ('scipy._lib.array_api_compat.numpy._info',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python313\\site-packages\\scipy\\_lib\\array_api_compat\\numpy\\_info.py',
   'PYMODULE'),
  ('scipy.interpolate.interpnd',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python313\\site-packages\\scipy\\interpolate\\interpnd.py',
   'PYMODULE'),
  ('scipy._lib.deprecation',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python313\\site-packages\\scipy\\_lib\\deprecation.py',
   'PYMODULE'),
  ('scipy._lib._docscrape',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python313\\site-packages\\scipy\\_lib\\_docscrape.py',
   'PYMODULE'),
  ('scipy.interpolate.rbf',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python313\\site-packages\\scipy\\interpolate\\rbf.py',
   'PYMODULE'),
  ('scipy.interpolate.polyint',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python313\\site-packages\\scipy\\interpolate\\polyint.py',
   'PYMODULE'),
  ('scipy.interpolate.ndgriddata',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python313\\site-packages\\scipy\\interpolate\\ndgriddata.py',
   'PYMODULE'),
  ('scipy.interpolate.interpolate',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python313\\site-packages\\scipy\\interpolate\\interpolate.py',
   'PYMODULE'),
  ('scipy.interpolate.fitpack2',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python313\\site-packages\\scipy\\interpolate\\fitpack2.py',
   'PYMODULE'),
  ('scipy.interpolate.fitpack',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python313\\site-packages\\scipy\\interpolate\\fitpack.py',
   'PYMODULE'),
  ('scipy.interpolate._bary_rational',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python313\\site-packages\\scipy\\interpolate\\_bary_rational.py',
   'PYMODULE'),
  ('scipy.interpolate._ndbspline',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python313\\site-packages\\scipy\\interpolate\\_ndbspline.py',
   'PYMODULE'),
  ('scipy.sparse',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python313\\site-packages\\scipy\\sparse\\__init__.py',
   'PYMODULE'),
  ('scipy.sparse.sputils',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python313\\site-packages\\scipy\\sparse\\sputils.py',
   'PYMODULE'),
  ('scipy.sparse.sparsetools',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python313\\site-packages\\scipy\\sparse\\sparsetools.py',
   'PYMODULE'),
  ('scipy.sparse.lil',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python313\\site-packages\\scipy\\sparse\\lil.py',
   'PYMODULE'),
  ('scipy.sparse.extract',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python313\\site-packages\\scipy\\sparse\\extract.py',
   'PYMODULE'),
  ('scipy.sparse.dok',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python313\\site-packages\\scipy\\sparse\\dok.py',
   'PYMODULE'),
  ('scipy.sparse.dia',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python313\\site-packages\\scipy\\sparse\\dia.py',
   'PYMODULE'),
  ('scipy.sparse.data',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python313\\site-packages\\scipy\\sparse\\data.py',
   'PYMODULE'),
  ('scipy.sparse.csr',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python313\\site-packages\\scipy\\sparse\\csr.py',
   'PYMODULE'),
  ('scipy.sparse.csc',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python313\\site-packages\\scipy\\sparse\\csc.py',
   'PYMODULE'),
  ('scipy.sparse.coo',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python313\\site-packages\\scipy\\sparse\\coo.py',
   'PYMODULE'),
  ('scipy.sparse.construct',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python313\\site-packages\\scipy\\sparse\\construct.py',
   'PYMODULE'),
  ('scipy.sparse.compressed',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python313\\site-packages\\scipy\\sparse\\compressed.py',
   'PYMODULE'),
  ('scipy.sparse.bsr',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python313\\site-packages\\scipy\\sparse\\bsr.py',
   'PYMODULE'),
  ('scipy.sparse.base',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python313\\site-packages\\scipy\\sparse\\base.py',
   'PYMODULE'),
  ('scipy.sparse._sputils',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python313\\site-packages\\scipy\\sparse\\_sputils.py',
   'PYMODULE'),
  ('scipy._lib._util',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python313\\site-packages\\scipy\\_lib\\_util.py',
   'PYMODULE'),
  ('numpy.random',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python313\\site-packages\\numpy\\random\\__init__.py',
   'PYMODULE'),
  ('numpy.random._pickle',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python313\\site-packages\\numpy\\random\\_pickle.py',
   'PYMODULE'),
  ('scipy._lib._sparse',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python313\\site-packages\\scipy\\_lib\\_sparse.py',
   'PYMODULE'),
  ('scipy._lib._array_api',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python313\\site-packages\\scipy\\_lib\\_array_api.py',
   'PYMODULE'),
  ('scipy._lib.array_api_extra.testing',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python313\\site-packages\\scipy\\_lib\\array_api_extra\\testing.py',
   'PYMODULE'),
  ('scipy._lib.array_api_extra',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python313\\site-packages\\scipy\\_lib\\array_api_extra\\__init__.py',
   'PYMODULE'),
  ('scipy._lib.array_api_extra._lib._lazy',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python313\\site-packages\\scipy\\_lib\\array_api_extra\\_lib\\_lazy.py',
   'PYMODULE'),
  ('scipy._lib.array_api_extra._lib',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python313\\site-packages\\scipy\\_lib\\array_api_extra\\_lib\\__init__.py',
   'PYMODULE'),
  ('scipy._lib.array_api_extra._lib._utils._typing',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python313\\site-packages\\scipy\\_lib\\array_api_extra\\_lib\\_utils\\_typing.py',
   'PYMODULE'),
  ('scipy._lib.array_api_extra._lib._utils',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python313\\site-packages\\scipy\\_lib\\array_api_extra\\_lib\\_utils\\__init__.py',
   'PYMODULE'),
  ('scipy._lib.array_api_extra._lib._funcs',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python313\\site-packages\\scipy\\_lib\\array_api_extra\\_lib\\_funcs.py',
   'PYMODULE'),
  ('scipy._lib.array_api_extra._lib._at',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python313\\site-packages\\scipy\\_lib\\array_api_extra\\_lib\\_at.py',
   'PYMODULE'),
  ('scipy._lib.array_api_extra._delegation',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python313\\site-packages\\scipy\\_lib\\array_api_extra\\_delegation.py',
   'PYMODULE'),
  ('scipy._lib.array_api_extra._lib._utils._helpers',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python313\\site-packages\\scipy\\_lib\\array_api_extra\\_lib\\_utils\\_helpers.py',
   'PYMODULE'),
  ('scipy._lib.array_api_extra._lib._utils._compat',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python313\\site-packages\\scipy\\_lib\\array_api_extra\\_lib\\_utils\\_compat.py',
   'PYMODULE'),
  ('scipy._lib._array_api_compat_vendor',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python313\\site-packages\\scipy\\_lib\\_array_api_compat_vendor.py',
   'PYMODULE'),
  ('scipy.sparse._matrix_io',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python313\\site-packages\\scipy\\sparse\\_matrix_io.py',
   'PYMODULE'),
  ('scipy.sparse._matrix',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python313\\site-packages\\scipy\\sparse\\_matrix.py',
   'PYMODULE'),
  ('scipy.sparse._extract',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python313\\site-packages\\scipy\\sparse\\_extract.py',
   'PYMODULE'),
  ('scipy.sparse._construct',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python313\\site-packages\\scipy\\sparse\\_construct.py',
   'PYMODULE'),
  ('scipy.sparse._bsr',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python313\\site-packages\\scipy\\sparse\\_bsr.py',
   'PYMODULE'),
  ('scipy.sparse._compressed',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python313\\site-packages\\scipy\\sparse\\_compressed.py',
   'PYMODULE'),
  ('scipy.sparse._index',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python313\\site-packages\\scipy\\sparse\\_index.py',
   'PYMODULE'),
  ('scipy.sparse._data',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python313\\site-packages\\scipy\\sparse\\_data.py',
   'PYMODULE'),
  ('scipy.sparse._dia',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python313\\site-packages\\scipy\\sparse\\_dia.py',
   'PYMODULE'),
  ('scipy.sparse._coo',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python313\\site-packages\\scipy\\sparse\\_coo.py',
   'PYMODULE'),
  ('scipy.sparse._dok',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python313\\site-packages\\scipy\\sparse\\_dok.py',
   'PYMODULE'),
  ('scipy.sparse._lil',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python313\\site-packages\\scipy\\sparse\\_lil.py',
   'PYMODULE'),
  ('scipy.sparse._csc',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python313\\site-packages\\scipy\\sparse\\_csc.py',
   'PYMODULE'),
  ('scipy.sparse._csr',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python313\\site-packages\\scipy\\sparse\\_csr.py',
   'PYMODULE'),
  ('scipy.sparse._spfuncs',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python313\\site-packages\\scipy\\sparse\\_spfuncs.py',
   'PYMODULE'),
  ('scipy.sparse._base',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python313\\site-packages\\scipy\\sparse\\_base.py',
   'PYMODULE'),
  ('scipy.sparse.linalg',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python313\\site-packages\\scipy\\sparse\\linalg\\__init__.py',
   'PYMODULE'),
  ('scipy.sparse.linalg.matfuncs',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python313\\site-packages\\scipy\\sparse\\linalg\\matfuncs.py',
   'PYMODULE'),
  ('scipy.sparse.linalg.eigen',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python313\\site-packages\\scipy\\sparse\\linalg\\eigen.py',
   'PYMODULE'),
  ('scipy.sparse.linalg.interface',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python313\\site-packages\\scipy\\sparse\\linalg\\interface.py',
   'PYMODULE'),
  ('scipy.sparse.linalg.dsolve',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python313\\site-packages\\scipy\\sparse\\linalg\\dsolve.py',
   'PYMODULE'),
  ('scipy.sparse.linalg.isolve',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python313\\site-packages\\scipy\\sparse\\linalg\\isolve.py',
   'PYMODULE'),
  ('scipy.sparse.linalg._special_sparse_arrays',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python313\\site-packages\\scipy\\sparse\\linalg\\_special_sparse_arrays.py',
   'PYMODULE'),
  ('scipy.sparse.linalg._expm_multiply',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python313\\site-packages\\scipy\\sparse\\linalg\\_expm_multiply.py',
   'PYMODULE'),
  ('scipy.linalg._decomp_qr',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python313\\site-packages\\scipy\\linalg\\_decomp_qr.py',
   'PYMODULE'),
  ('scipy.linalg._misc',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python313\\site-packages\\scipy\\linalg\\_misc.py',
   'PYMODULE'),
  ('scipy.linalg.blas',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python313\\site-packages\\scipy\\linalg\\blas.py',
   'PYMODULE'),
  ('scipy.linalg.lapack',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python313\\site-packages\\scipy\\linalg\\lapack.py',
   'PYMODULE'),
  ('scipy.linalg',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python313\\site-packages\\scipy\\linalg\\__init__.py',
   'PYMODULE'),
  ('scipy.linalg.matfuncs',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python313\\site-packages\\scipy\\linalg\\matfuncs.py',
   'PYMODULE'),
  ('scipy.linalg.special_matrices',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python313\\site-packages\\scipy\\linalg\\special_matrices.py',
   'PYMODULE'),
  ('scipy.linalg.misc',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python313\\site-packages\\scipy\\linalg\\misc.py',
   'PYMODULE'),
  ('scipy.linalg.basic',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python313\\site-packages\\scipy\\linalg\\basic.py',
   'PYMODULE'),
  ('scipy.linalg.decomp_schur',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python313\\site-packages\\scipy\\linalg\\decomp_schur.py',
   'PYMODULE'),
  ('scipy.linalg.decomp_svd',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python313\\site-packages\\scipy\\linalg\\decomp_svd.py',
   'PYMODULE'),
  ('scipy.linalg.decomp_qr',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python313\\site-packages\\scipy\\linalg\\decomp_qr.py',
   'PYMODULE'),
  ('scipy.linalg.decomp_lu',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python313\\site-packages\\scipy\\linalg\\decomp_lu.py',
   'PYMODULE'),
  ('scipy.linalg.decomp_cholesky',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python313\\site-packages\\scipy\\linalg\\decomp_cholesky.py',
   'PYMODULE'),
  ('scipy.linalg.decomp',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python313\\site-packages\\scipy\\linalg\\decomp.py',
   'PYMODULE'),
  ('scipy.linalg._decomp_cossin',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python313\\site-packages\\scipy\\linalg\\_decomp_cossin.py',
   'PYMODULE'),
  ('scipy.linalg._sketches',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python313\\site-packages\\scipy\\linalg\\_sketches.py',
   'PYMODULE'),
  ('scipy.linalg._procrustes',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python313\\site-packages\\scipy\\linalg\\_procrustes.py',
   'PYMODULE'),
  ('scipy.linalg._solvers',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python313\\site-packages\\scipy\\linalg\\_solvers.py',
   'PYMODULE'),
  ('scipy.linalg._special_matrices',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python313\\site-packages\\scipy\\linalg\\_special_matrices.py',
   'PYMODULE'),
  ('scipy.special',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python313\\site-packages\\scipy\\special\\__init__.py',
   'PYMODULE'),
  ('scipy.special.spfun_stats',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python313\\site-packages\\scipy\\special\\spfun_stats.py',
   'PYMODULE'),
  ('scipy.special.sf_error',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python313\\site-packages\\scipy\\special\\sf_error.py',
   'PYMODULE'),
  ('scipy.special.specfun',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python313\\site-packages\\scipy\\special\\specfun.py',
   'PYMODULE'),
  ('scipy.special.orthogonal',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python313\\site-packages\\scipy\\special\\orthogonal.py',
   'PYMODULE'),
  ('scipy.special.basic',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python313\\site-packages\\scipy\\special\\basic.py',
   'PYMODULE'),
  ('scipy.special.add_newdocs',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python313\\site-packages\\scipy\\special\\add_newdocs.py',
   'PYMODULE'),
  ('scipy.special._spherical_bessel',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python313\\site-packages\\scipy\\special\\_spherical_bessel.py',
   'PYMODULE'),
  ('scipy.special._lambertw',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python313\\site-packages\\scipy\\special\\_lambertw.py',
   'PYMODULE'),
  ('scipy.special._ellip_harm',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python313\\site-packages\\scipy\\special\\_ellip_harm.py',
   'PYMODULE'),
  ('scipy.integrate',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python313\\site-packages\\scipy\\integrate\\__init__.py',
   'PYMODULE'),
  ('scipy.integrate.quadpack',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python313\\site-packages\\scipy\\integrate\\quadpack.py',
   'PYMODULE'),
  ('scipy.integrate.odepack',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python313\\site-packages\\scipy\\integrate\\odepack.py',
   'PYMODULE'),
  ('scipy.integrate.vode',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python313\\site-packages\\scipy\\integrate\\vode.py',
   'PYMODULE'),
  ('scipy.integrate.lsoda',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python313\\site-packages\\scipy\\integrate\\lsoda.py',
   'PYMODULE'),
  ('scipy.integrate.dop',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python313\\site-packages\\scipy\\integrate\\dop.py',
   'PYMODULE'),
  ('scipy.integrate._lebedev',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python313\\site-packages\\scipy\\integrate\\_lebedev.py',
   'PYMODULE'),
  ('scipy.integrate._cubature',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python313\\site-packages\\scipy\\integrate\\_cubature.py',
   'PYMODULE'),
  ('scipy.integrate._rules._base',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python313\\site-packages\\scipy\\integrate\\_rules\\_base.py',
   'PYMODULE'),
  ('scipy.integrate._rules',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python313\\site-packages\\scipy\\integrate\\_rules\\__init__.py',
   'PYMODULE'),
  ('scipy.integrate._rules._gauss_legendre',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python313\\site-packages\\scipy\\integrate\\_rules\\_gauss_legendre.py',
   'PYMODULE'),
  ('scipy.integrate._rules._gauss_kronrod',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python313\\site-packages\\scipy\\integrate\\_rules\\_gauss_kronrod.py',
   'PYMODULE'),
  ('scipy.integrate._rules._genz_malik',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python313\\site-packages\\scipy\\integrate\\_rules\\_genz_malik.py',
   'PYMODULE'),
  ('scipy.integrate._tanhsinh',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python313\\site-packages\\scipy\\integrate\\_tanhsinh.py',
   'PYMODULE'),
  ('scipy._lib._elementwise_iterative_method',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python313\\site-packages\\scipy\\_lib\\_elementwise_iterative_method.py',
   'PYMODULE'),
  ('scipy.integrate._quad_vec',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python313\\site-packages\\scipy\\integrate\\_quad_vec.py',
   'PYMODULE'),
  ('scipy.integrate._ivp',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python313\\site-packages\\scipy\\integrate\\_ivp\\__init__.py',
   'PYMODULE'),
  ('scipy.integrate._ivp.base',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python313\\site-packages\\scipy\\integrate\\_ivp\\base.py',
   'PYMODULE'),
  ('scipy.integrate._ivp.common',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python313\\site-packages\\scipy\\integrate\\_ivp\\common.py',
   'PYMODULE'),
  ('scipy.integrate._ivp.lsoda',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python313\\site-packages\\scipy\\integrate\\_ivp\\lsoda.py',
   'PYMODULE'),
  ('scipy.integrate._ivp.bdf',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python313\\site-packages\\scipy\\integrate\\_ivp\\bdf.py',
   'PYMODULE'),
  ('scipy.optimize._numdiff',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python313\\site-packages\\scipy\\optimize\\_numdiff.py',
   'PYMODULE'),
  ('scipy.optimize',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python313\\site-packages\\scipy\\optimize\\__init__.py',
   'PYMODULE'),
  ('scipy.optimize.zeros',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python313\\site-packages\\scipy\\optimize\\zeros.py',
   'PYMODULE'),
  ('scipy.optimize.tnc',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python313\\site-packages\\scipy\\optimize\\tnc.py',
   'PYMODULE'),
  ('scipy.optimize.slsqp',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python313\\site-packages\\scipy\\optimize\\slsqp.py',
   'PYMODULE'),
  ('scipy.optimize.optimize',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python313\\site-packages\\scipy\\optimize\\optimize.py',
   'PYMODULE'),
  ('scipy.optimize.nonlin',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python313\\site-packages\\scipy\\optimize\\nonlin.py',
   'PYMODULE'),
  ('scipy.optimize.moduleTNC',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python313\\site-packages\\scipy\\optimize\\moduleTNC.py',
   'PYMODULE'),
  ('scipy.optimize.minpack2',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python313\\site-packages\\scipy\\optimize\\minpack2.py',
   'PYMODULE'),
  ('scipy.optimize.minpack',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python313\\site-packages\\scipy\\optimize\\minpack.py',
   'PYMODULE'),
  ('scipy.optimize.linesearch',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python313\\site-packages\\scipy\\optimize\\linesearch.py',
   'PYMODULE'),
  ('scipy.optimize.lbfgsb',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python313\\site-packages\\scipy\\optimize\\lbfgsb.py',
   'PYMODULE'),
  ('scipy.optimize.cobyla',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python313\\site-packages\\scipy\\optimize\\cobyla.py',
   'PYMODULE'),
  ('scipy.optimize._milp',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python313\\site-packages\\scipy\\optimize\\_milp.py',
   'PYMODULE'),
  ('scipy.optimize._linprog_highs',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python313\\site-packages\\scipy\\optimize\\_linprog_highs.py',
   'PYMODULE'),
  ('scipy.optimize._highspy',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python313\\site-packages\\scipy\\optimize\\_highspy\\__init__.py',
   'PYMODULE'),
  ('scipy.optimize._highspy._highs_wrapper',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python313\\site-packages\\scipy\\optimize\\_highspy\\_highs_wrapper.py',
   'PYMODULE'),
  ('scipy.optimize._direct_py',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python313\\site-packages\\scipy\\optimize\\_direct_py.py',
   'PYMODULE'),
  ('scipy.optimize._qap',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python313\\site-packages\\scipy\\optimize\\_qap.py',
   'PYMODULE'),
  ('scipy.optimize._dual_annealing',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python313\\site-packages\\scipy\\optimize\\_dual_annealing.py',
   'PYMODULE'),
  ('scipy.optimize._shgo',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python313\\site-packages\\scipy\\optimize\\_shgo.py',
   'PYMODULE'),
  ('scipy.stats.qmc',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python313\\site-packages\\scipy\\stats\\qmc.py',
   'PYMODULE'),
  ('scipy.stats._qmc',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python313\\site-packages\\scipy\\stats\\_qmc.py',
   'PYMODULE'),
  ('scipy.spatial.distance',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python313\\site-packages\\scipy\\spatial\\distance.py',
   'PYMODULE'),
  ('scipy.sparse.csgraph',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python313\\site-packages\\scipy\\sparse\\csgraph\\__init__.py',
   'PYMODULE'),
  ('scipy.sparse.csgraph._validation',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python313\\site-packages\\scipy\\sparse\\csgraph\\_validation.py',
   'PYMODULE'),
  ('scipy.sparse.csgraph._laplacian',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python313\\site-packages\\scipy\\sparse\\csgraph\\_laplacian.py',
   'PYMODULE'),
  ('scipy.stats',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python313\\site-packages\\scipy\\stats\\__init__.py',
   'PYMODULE'),
  ('scipy.stats.stats',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python313\\site-packages\\scipy\\stats\\stats.py',
   'PYMODULE'),
  ('scipy.stats.mvn',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python313\\site-packages\\scipy\\stats\\mvn.py',
   'PYMODULE'),
  ('scipy.stats.mstats_extras',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python313\\site-packages\\scipy\\stats\\mstats_extras.py',
   'PYMODULE'),
  ('scipy.stats.mstats_basic',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python313\\site-packages\\scipy\\stats\\mstats_basic.py',
   'PYMODULE'),
  ('scipy.stats.morestats',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python313\\site-packages\\scipy\\stats\\morestats.py',
   'PYMODULE'),
  ('scipy.stats.kde',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python313\\site-packages\\scipy\\stats\\kde.py',
   'PYMODULE'),
  ('scipy.stats.biasedurn',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python313\\site-packages\\scipy\\stats\\biasedurn.py',
   'PYMODULE'),
  ('scipy.stats._quantile',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python313\\site-packages\\scipy\\stats\\_quantile.py',
   'PYMODULE'),
  ('scipy.stats._axis_nan_policy',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python313\\site-packages\\scipy\\stats\\_axis_nan_policy.py',
   'PYMODULE'),
  ('scipy.stats._correlation',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python313\\site-packages\\scipy\\stats\\_correlation.py',
   'PYMODULE'),
  ('scipy.stats._mgc',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python313\\site-packages\\scipy\\stats\\_mgc.py',
   'PYMODULE'),
  ('scipy.ndimage._measurements',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python313\\site-packages\\scipy\\ndimage\\_measurements.py',
   'PYMODULE'),
  ('scipy.ndimage._morphology',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python313\\site-packages\\scipy\\ndimage\\_morphology.py',
   'PYMODULE'),
  ('scipy.ndimage._filters',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python313\\site-packages\\scipy\\ndimage\\_filters.py',
   'PYMODULE'),
  ('scipy.ndimage._ni_docstrings',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python313\\site-packages\\scipy\\ndimage\\_ni_docstrings.py',
   'PYMODULE'),
  ('scipy.ndimage._ni_support',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python313\\site-packages\\scipy\\ndimage\\_ni_support.py',
   'PYMODULE'),
  ('scipy._lib._bunch',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python313\\site-packages\\scipy\\_lib\\_bunch.py',
   'PYMODULE'),
  ('scipy.stats._new_distributions',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python313\\site-packages\\scipy\\stats\\_new_distributions.py',
   'PYMODULE'),
  ('scipy.stats._distribution_infrastructure',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python313\\site-packages\\scipy\\stats\\_distribution_infrastructure.py',
   'PYMODULE'),
  ('scipy.stats._probability_distribution',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python313\\site-packages\\scipy\\stats\\_probability_distribution.py',
   'PYMODULE'),
  ('scipy.optimize._chandrupatla',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python313\\site-packages\\scipy\\optimize\\_chandrupatla.py',
   'PYMODULE'),
  ('scipy.optimize._bracket',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python313\\site-packages\\scipy\\optimize\\_bracket.py',
   'PYMODULE'),
  ('scipy.stats._survival',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python313\\site-packages\\scipy\\stats\\_survival.py',
   'PYMODULE'),
  ('scipy.stats._common',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python313\\site-packages\\scipy\\stats\\_common.py',
   'PYMODULE'),
  ('scipy.stats._sensitivity_analysis',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python313\\site-packages\\scipy\\stats\\_sensitivity_analysis.py',
   'PYMODULE'),
  ('scipy.stats._fit',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python313\\site-packages\\scipy\\stats\\_fit.py',
   'PYMODULE'),
  ('scipy.stats._bws_test',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python313\\site-packages\\scipy\\stats\\_bws_test.py',
   'PYMODULE'),
  ('scipy.stats._mannwhitneyu',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python313\\site-packages\\scipy\\stats\\_mannwhitneyu.py',
   'PYMODULE'),
  ('scipy.stats._page_trend_test',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python313\\site-packages\\scipy\\stats\\_page_trend_test.py',
   'PYMODULE'),
  ('scipy.stats._hypotests',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python313\\site-packages\\scipy\\stats\\_hypotests.py',
   'PYMODULE'),
  ('scipy.fft',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python313\\site-packages\\scipy\\fft\\__init__.py',
   'PYMODULE'),
  ('scipy.fft._pocketfft.helper',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python313\\site-packages\\scipy\\fft\\_pocketfft\\helper.py',
   'PYMODULE'),
  ('scipy.fft._pocketfft',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python313\\site-packages\\scipy\\fft\\_pocketfft\\__init__.py',
   'PYMODULE'),
  ('scipy.fft._pocketfft.realtransforms',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python313\\site-packages\\scipy\\fft\\_pocketfft\\realtransforms.py',
   'PYMODULE'),
  ('scipy.fft._pocketfft.basic',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python313\\site-packages\\scipy\\fft\\_pocketfft\\basic.py',
   'PYMODULE'),
  ('scipy.fft._backend',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python313\\site-packages\\scipy\\fft\\_backend.py',
   'PYMODULE'),
  ('scipy.fft._fftlog_backend',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python313\\site-packages\\scipy\\fft\\_fftlog_backend.py',
   'PYMODULE'),
  ('scipy._lib.uarray',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python313\\site-packages\\scipy\\_lib\\uarray.py',
   'PYMODULE'),
  ('scipy._lib._uarray',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python313\\site-packages\\scipy\\_lib\\_uarray\\__init__.py',
   'PYMODULE'),
  ('scipy._lib._uarray._backend',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python313\\site-packages\\scipy\\_lib\\_uarray\\_backend.py',
   'PYMODULE'),
  ('scipy.fft._realtransforms_backend',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python313\\site-packages\\scipy\\fft\\_realtransforms_backend.py',
   'PYMODULE'),
  ('scipy.fft._basic_backend',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python313\\site-packages\\scipy\\fft\\_basic_backend.py',
   'PYMODULE'),
  ('scipy.fft._helper',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python313\\site-packages\\scipy\\fft\\_helper.py',
   'PYMODULE'),
  ('scipy.fft._fftlog',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python313\\site-packages\\scipy\\fft\\_fftlog.py',
   'PYMODULE'),
  ('scipy.fft._realtransforms',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python313\\site-packages\\scipy\\fft\\_realtransforms.py',
   'PYMODULE'),
  ('scipy.fft._basic',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python313\\site-packages\\scipy\\fft\\_basic.py',
   'PYMODULE'),
  ('scipy.stats._entropy',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python313\\site-packages\\scipy\\stats\\_entropy.py',
   'PYMODULE'),
  ('scipy.stats._resampling',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python313\\site-packages\\scipy\\stats\\_resampling.py',
   'PYMODULE'),
  ('scipy.stats._censored_data',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python313\\site-packages\\scipy\\stats\\_censored_data.py',
   'PYMODULE'),
  ('scipy.stats.contingency',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python313\\site-packages\\scipy\\stats\\contingency.py',
   'PYMODULE'),
  ('scipy.stats._odds_ratio',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python313\\site-packages\\scipy\\stats\\_odds_ratio.py',
   'PYMODULE'),
  ('scipy.stats._crosstab',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python313\\site-packages\\scipy\\stats\\_crosstab.py',
   'PYMODULE'),
  ('scipy.stats._relative_risk',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python313\\site-packages\\scipy\\stats\\_relative_risk.py',
   'PYMODULE'),
  ('scipy.stats.mstats',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python313\\site-packages\\scipy\\stats\\mstats.py',
   'PYMODULE'),
  ('scipy.stats._mstats_extras',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python313\\site-packages\\scipy\\stats\\_mstats_extras.py',
   'PYMODULE'),
  ('scipy.stats._kde',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python313\\site-packages\\scipy\\stats\\_kde.py',
   'PYMODULE'),
  ('scipy.stats._rcont',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python313\\site-packages\\scipy\\stats\\_rcont\\__init__.py',
   'PYMODULE'),
  ('scipy.stats._covariance',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python313\\site-packages\\scipy\\stats\\_covariance.py',
   'PYMODULE'),
  ('scipy.stats._multivariate',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python313\\site-packages\\scipy\\stats\\_multivariate.py',
   'PYMODULE'),
  ('scipy.stats._qmvnt',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python313\\site-packages\\scipy\\stats\\_qmvnt.py',
   'PYMODULE'),
  ('scipy.stats._binned_statistic',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python313\\site-packages\\scipy\\stats\\_binned_statistic.py',
   'PYMODULE'),
  ('scipy.stats._binomtest',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python313\\site-packages\\scipy\\stats\\_binomtest.py',
   'PYMODULE'),
  ('scipy.stats._multicomp',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python313\\site-packages\\scipy\\stats\\_multicomp.py',
   'PYMODULE'),
  ('scipy.stats._wilcoxon',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python313\\site-packages\\scipy\\stats\\_wilcoxon.py',
   'PYMODULE'),
  ('scipy.stats._morestats',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python313\\site-packages\\scipy\\stats\\_morestats.py',
   'PYMODULE'),
  ('scipy.stats._distn_infrastructure',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python313\\site-packages\\scipy\\stats\\_distn_infrastructure.py',
   'PYMODULE'),
  ('scipy.stats._constants',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python313\\site-packages\\scipy\\stats\\_constants.py',
   'PYMODULE'),
  ('scipy.stats._finite_differences',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python313\\site-packages\\scipy\\stats\\_finite_differences.py',
   'PYMODULE'),
  ('scipy.stats._distr_params',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python313\\site-packages\\scipy\\stats\\_distr_params.py',
   'PYMODULE'),
  ('scipy.stats._variation',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python313\\site-packages\\scipy\\stats\\_variation.py',
   'PYMODULE'),
  ('scipy.stats._stats_py',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python313\\site-packages\\scipy\\stats\\_stats_py.py',
   'PYMODULE'),
  ('scipy.stats._stats_mstats_common',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python313\\site-packages\\scipy\\stats\\_stats_mstats_common.py',
   'PYMODULE'),
  ('scipy.stats._mstats_basic',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python313\\site-packages\\scipy\\stats\\_mstats_basic.py',
   'PYMODULE'),
  ('scipy.stats.distributions',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python313\\site-packages\\scipy\\stats\\distributions.py',
   'PYMODULE'),
  ('scipy.stats._levy_stable',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python313\\site-packages\\scipy\\stats\\_levy_stable\\__init__.py',
   'PYMODULE'),
  ('scipy.stats._discrete_distns',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python313\\site-packages\\scipy\\stats\\_discrete_distns.py',
   'PYMODULE'),
  ('scipy.stats._continuous_distns',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python313\\site-packages\\scipy\\stats\\_continuous_distns.py',
   'PYMODULE'),
  ('scipy.stats._ksstats',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python313\\site-packages\\scipy\\stats\\_ksstats.py',
   'PYMODULE'),
  ('scipy.stats._tukeylambda_stats',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python313\\site-packages\\scipy\\stats\\_tukeylambda_stats.py',
   'PYMODULE'),
  ('scipy._lib._ccallback',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python313\\site-packages\\scipy\\_lib\\_ccallback.py',
   'PYMODULE'),
  ('cffi',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python313\\site-packages\\cffi\\__init__.py',
   'PYMODULE'),
  ('cffi.error',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python313\\site-packages\\cffi\\error.py',
   'PYMODULE'),
  ('cffi.api',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python313\\site-packages\\cffi\\api.py',
   'PYMODULE'),
  ('cffi.recompiler',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python313\\site-packages\\cffi\\recompiler.py',
   'PYMODULE'),
  ('cffi.cffi_opcode',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python313\\site-packages\\cffi\\cffi_opcode.py',
   'PYMODULE'),
  ('cffi._shimmed_dist_utils',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python313\\site-packages\\cffi\\_shimmed_dist_utils.py',
   'PYMODULE'),
  ('cffi.verifier',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python313\\site-packages\\cffi\\verifier.py',
   'PYMODULE'),
  ('cffi.lock',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python313\\site-packages\\cffi\\lock.py',
   'PYMODULE'),
  ('cffi.pkgconfig',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python313\\site-packages\\cffi\\pkgconfig.py',
   'PYMODULE'),
  ('cffi.vengine_cpy',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python313\\site-packages\\cffi\\vengine_cpy.py',
   'PYMODULE'),
  ('cffi._imp_emulation',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python313\\site-packages\\cffi\\_imp_emulation.py',
   'PYMODULE'),
  ('cffi.vengine_gen',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python313\\site-packages\\cffi\\vengine_gen.py',
   'PYMODULE'),
  ('cffi.ffiplatform',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python313\\site-packages\\cffi\\ffiplatform.py',
   'PYMODULE'),
  ('cffi.cparser',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python313\\site-packages\\cffi\\cparser.py',
   'PYMODULE'),
  ('pycparser.lextab',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python313\\site-packages\\pycparser\\lextab.py',
   'PYMODULE'),
  ('pycparser.yacctab',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python313\\site-packages\\pycparser\\yacctab.py',
   'PYMODULE'),
  ('pycparser',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python313\\site-packages\\pycparser\\__init__.py',
   'PYMODULE'),
  ('pycparser.c_parser',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python313\\site-packages\\pycparser\\c_parser.py',
   'PYMODULE'),
  ('pycparser.ast_transforms',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python313\\site-packages\\pycparser\\ast_transforms.py',
   'PYMODULE'),
  ('pycparser.plyparser',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python313\\site-packages\\pycparser\\plyparser.py',
   'PYMODULE'),
  ('pycparser.c_lexer',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python313\\site-packages\\pycparser\\c_lexer.py',
   'PYMODULE'),
  ('pycparser.ply.lex',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python313\\site-packages\\pycparser\\ply\\lex.py',
   'PYMODULE'),
  ('pycparser.ply.yacc',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python313\\site-packages\\pycparser\\ply\\yacc.py',
   'PYMODULE'),
  ('pycparser.ply',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python313\\site-packages\\pycparser\\ply\\__init__.py',
   'PYMODULE'),
  ('pycparser.c_ast',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python313\\site-packages\\pycparser\\c_ast.py',
   'PYMODULE'),
  ('cffi.commontypes',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python313\\site-packages\\cffi\\commontypes.py',
   'PYMODULE'),
  ('cffi.model',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python313\\site-packages\\cffi\\model.py',
   'PYMODULE'),
  ('numpy.polynomial',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python313\\site-packages\\numpy\\polynomial\\__init__.py',
   'PYMODULE'),
  ('numpy.polynomial._polybase',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python313\\site-packages\\numpy\\polynomial\\_polybase.py',
   'PYMODULE'),
  ('numpy.polynomial.laguerre',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python313\\site-packages\\numpy\\polynomial\\laguerre.py',
   'PYMODULE'),
  ('numpy.polynomial.hermite_e',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python313\\site-packages\\numpy\\polynomial\\hermite_e.py',
   'PYMODULE'),
  ('numpy.polynomial.hermite',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python313\\site-packages\\numpy\\polynomial\\hermite.py',
   'PYMODULE'),
  ('numpy.polynomial.legendre',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python313\\site-packages\\numpy\\polynomial\\legendre.py',
   'PYMODULE'),
  ('numpy.polynomial.chebyshev',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python313\\site-packages\\numpy\\polynomial\\chebyshev.py',
   'PYMODULE'),
  ('numpy.polynomial.polynomial',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python313\\site-packages\\numpy\\polynomial\\polynomial.py',
   'PYMODULE'),
  ('numpy.polynomial.polyutils',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python313\\site-packages\\numpy\\polynomial\\polyutils.py',
   'PYMODULE'),
  ('scipy.stats._warnings_errors',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python313\\site-packages\\scipy\\stats\\_warnings_errors.py',
   'PYMODULE'),
  ('scipy.optimize._shgo_lib._complex',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python313\\site-packages\\scipy\\optimize\\_shgo_lib\\_complex.py',
   'PYMODULE'),
  ('scipy.optimize._shgo_lib',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python313\\site-packages\\scipy\\optimize\\_shgo_lib\\__init__.py',
   'PYMODULE'),
  ('scipy.optimize._shgo_lib._vertex',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python313\\site-packages\\scipy\\optimize\\_shgo_lib\\_vertex.py',
   'PYMODULE'),
  ('scipy.optimize._hessian_update_strategy',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python313\\site-packages\\scipy\\optimize\\_hessian_update_strategy.py',
   'PYMODULE'),
  ('scipy.optimize._constraints',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python313\\site-packages\\scipy\\optimize\\_constraints.py',
   'PYMODULE'),
  ('scipy.optimize._differentiable_functions',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python313\\site-packages\\scipy\\optimize\\_differentiable_functions.py',
   'PYMODULE'),
  ('scipy.optimize._isotonic',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python313\\site-packages\\scipy\\optimize\\_isotonic.py',
   'PYMODULE'),
  ('scipy.optimize._lsq.lsq_linear',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python313\\site-packages\\scipy\\optimize\\_lsq\\lsq_linear.py',
   'PYMODULE'),
  ('scipy.optimize._lsq.bvls',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python313\\site-packages\\scipy\\optimize\\_lsq\\bvls.py',
   'PYMODULE'),
  ('scipy.optimize._lsq.trf_linear',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python313\\site-packages\\scipy\\optimize\\_lsq\\trf_linear.py',
   'PYMODULE'),
  ('scipy.optimize._lsq.common',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python313\\site-packages\\scipy\\optimize\\_lsq\\common.py',
   'PYMODULE'),
  ('scipy.optimize._lsq.least_squares',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python313\\site-packages\\scipy\\optimize\\_lsq\\least_squares.py',
   'PYMODULE'),
  ('scipy.optimize._lsq.dogbox',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python313\\site-packages\\scipy\\optimize\\_lsq\\dogbox.py',
   'PYMODULE'),
  ('scipy.optimize._lsq.trf',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python313\\site-packages\\scipy\\optimize\\_lsq\\trf.py',
   'PYMODULE'),
  ('scipy.optimize._lsq',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python313\\site-packages\\scipy\\optimize\\_lsq\\__init__.py',
   'PYMODULE'),
  ('scipy.optimize._differentialevolution',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python313\\site-packages\\scipy\\optimize\\_differentialevolution.py',
   'PYMODULE'),
  ('scipy.optimize._linprog',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python313\\site-packages\\scipy\\optimize\\_linprog.py',
   'PYMODULE'),
  ('scipy.optimize._linprog_util',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python313\\site-packages\\scipy\\optimize\\_linprog_util.py',
   'PYMODULE'),
  ('scipy.optimize._remove_redundancy',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python313\\site-packages\\scipy\\optimize\\_remove_redundancy.py',
   'PYMODULE'),
  ('scipy.linalg.interpolative',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python313\\site-packages\\scipy\\linalg\\interpolative.py',
   'PYMODULE'),
  ('scipy.optimize._linprog_doc',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python313\\site-packages\\scipy\\optimize\\_linprog_doc.py',
   'PYMODULE'),
  ('scipy.optimize._linprog_rs',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python313\\site-packages\\scipy\\optimize\\_linprog_rs.py',
   'PYMODULE'),
  ('scipy.optimize._linprog_simplex',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python313\\site-packages\\scipy\\optimize\\_linprog_simplex.py',
   'PYMODULE'),
  ('scipy.optimize._linprog_ip',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python313\\site-packages\\scipy\\optimize\\_linprog_ip.py',
   'PYMODULE'),
  ('scipy.optimize._basinhopping',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python313\\site-packages\\scipy\\optimize\\_basinhopping.py',
   'PYMODULE'),
  ('scipy.optimize._nnls',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python313\\site-packages\\scipy\\optimize\\_nnls.py',
   'PYMODULE'),
  ('scipy.optimize._slsqp_py',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python313\\site-packages\\scipy\\optimize\\_slsqp_py.py',
   'PYMODULE'),
  ('scipy.optimize._cobyla_py',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python313\\site-packages\\scipy\\optimize\\_cobyla_py.py',
   'PYMODULE'),
  ('scipy._lib.pyprima.common.message',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python313\\site-packages\\scipy\\_lib\\pyprima\\common\\message.py',
   'PYMODULE'),
  ('scipy._lib.pyprima.common',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python313\\site-packages\\scipy\\_lib\\pyprima\\common\\__init__.py',
   'PYMODULE'),
  ('scipy._lib.pyprima.common.present',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python313\\site-packages\\scipy\\_lib\\pyprima\\common\\present.py',
   'PYMODULE'),
  ('scipy._lib.pyprima.common.consts',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python313\\site-packages\\scipy\\_lib\\pyprima\\common\\consts.py',
   'PYMODULE'),
  ('scipy._lib.pyprima.common.infos',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python313\\site-packages\\scipy\\_lib\\pyprima\\common\\infos.py',
   'PYMODULE'),
  ('scipy._lib.pyprima',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python313\\site-packages\\scipy\\_lib\\pyprima\\__init__.py',
   'PYMODULE'),
  ('scipy._lib.pyprima.cobyla.cobyla',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python313\\site-packages\\scipy\\_lib\\pyprima\\cobyla\\cobyla.py',
   'PYMODULE'),
  ('scipy._lib.pyprima.cobyla',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python313\\site-packages\\scipy\\_lib\\pyprima\\cobyla\\__init__.py',
   'PYMODULE'),
  ('scipy._lib.pyprima.cobyla.cobylb',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python313\\site-packages\\scipy\\_lib\\pyprima\\cobyla\\cobylb.py',
   'PYMODULE'),
  ('scipy._lib.pyprima.cobyla.initialize',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python313\\site-packages\\scipy\\_lib\\pyprima\\cobyla\\initialize.py',
   'PYMODULE'),
  ('scipy._lib.pyprima.cobyla.trustregion',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python313\\site-packages\\scipy\\_lib\\pyprima\\cobyla\\trustregion.py',
   'PYMODULE'),
  ('scipy._lib.pyprima.common.powalg',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python313\\site-packages\\scipy\\_lib\\pyprima\\common\\powalg.py',
   'PYMODULE'),
  ('scipy._lib.pyprima.cobyla.geometry',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python313\\site-packages\\scipy\\_lib\\pyprima\\cobyla\\geometry.py',
   'PYMODULE'),
  ('scipy._lib.pyprima.cobyla.update',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python313\\site-packages\\scipy\\_lib\\pyprima\\cobyla\\update.py',
   'PYMODULE'),
  ('scipy._lib.pyprima.common.selectx',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python313\\site-packages\\scipy\\_lib\\pyprima\\common\\selectx.py',
   'PYMODULE'),
  ('scipy._lib.pyprima.common.redrho',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python313\\site-packages\\scipy\\_lib\\pyprima\\common\\redrho.py',
   'PYMODULE'),
  ('scipy._lib.pyprima.common.ratio',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python313\\site-packages\\scipy\\_lib\\pyprima\\common\\ratio.py',
   'PYMODULE'),
  ('scipy._lib.pyprima.common.history',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python313\\site-packages\\scipy\\_lib\\pyprima\\common\\history.py',
   'PYMODULE'),
  ('scipy._lib.pyprima.common.checkbreak',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python313\\site-packages\\scipy\\_lib\\pyprima\\common\\checkbreak.py',
   'PYMODULE'),
  ('scipy._lib.pyprima.common.preproc',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python313\\site-packages\\scipy\\_lib\\pyprima\\common\\preproc.py',
   'PYMODULE'),
  ('scipy._lib.pyprima.common.evaluate',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python313\\site-packages\\scipy\\_lib\\pyprima\\common\\evaluate.py',
   'PYMODULE'),
  ('scipy._lib.pyprima.common.linalg',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python313\\site-packages\\scipy\\_lib\\pyprima\\common\\linalg.py',
   'PYMODULE'),
  ('scipy._lib.pyprima.common._project',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python313\\site-packages\\scipy\\_lib\\pyprima\\common\\_project.py',
   'PYMODULE'),
  ('scipy._lib.pyprima.common._bounds',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python313\\site-packages\\scipy\\_lib\\pyprima\\common\\_bounds.py',
   'PYMODULE'),
  ('scipy._lib.pyprima.common._linear_constraints',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python313\\site-packages\\scipy\\_lib\\pyprima\\common\\_linear_constraints.py',
   'PYMODULE'),
  ('scipy._lib.pyprima.common._nonlinear_constraints',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python313\\site-packages\\scipy\\_lib\\pyprima\\common\\_nonlinear_constraints.py',
   'PYMODULE'),
  ('scipy.optimize._tnc',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python313\\site-packages\\scipy\\optimize\\_tnc.py',
   'PYMODULE'),
  ('scipy.optimize._lbfgsb_py',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python313\\site-packages\\scipy\\optimize\\_lbfgsb_py.py',
   'PYMODULE'),
  ('scipy.optimize._minpack_py',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python313\\site-packages\\scipy\\optimize\\_minpack_py.py',
   'PYMODULE'),
  ('scipy.optimize._root_scalar',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python313\\site-packages\\scipy\\optimize\\_root_scalar.py',
   'PYMODULE'),
  ('scipy.optimize._zeros_py',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python313\\site-packages\\scipy\\optimize\\_zeros_py.py',
   'PYMODULE'),
  ('scipy.optimize._root',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python313\\site-packages\\scipy\\optimize\\_root.py',
   'PYMODULE'),
  ('scipy.optimize._spectral',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python313\\site-packages\\scipy\\optimize\\_spectral.py',
   'PYMODULE'),
  ('scipy.optimize._linesearch',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python313\\site-packages\\scipy\\optimize\\_linesearch.py',
   'PYMODULE'),
  ('scipy.optimize._dcsrch',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python313\\site-packages\\scipy\\optimize\\_dcsrch.py',
   'PYMODULE'),
  ('scipy.optimize._nonlin',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python313\\site-packages\\scipy\\optimize\\_nonlin.py',
   'PYMODULE'),
  ('scipy.optimize._minimize',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python313\\site-packages\\scipy\\optimize\\_minimize.py',
   'PYMODULE'),
  ('scipy.optimize._cobyqa_py',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python313\\site-packages\\scipy\\optimize\\_cobyqa_py.py',
   'PYMODULE'),
  ('scipy._lib.cobyqa',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python313\\site-packages\\scipy\\_lib\\cobyqa\\__init__.py',
   'PYMODULE'),
  ('scipy._lib.cobyqa.utils',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python313\\site-packages\\scipy\\_lib\\cobyqa\\utils\\__init__.py',
   'PYMODULE'),
  ('scipy._lib.cobyqa.utils.versions',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python313\\site-packages\\scipy\\_lib\\cobyqa\\utils\\versions.py',
   'PYMODULE'),
  ('scipy._lib.cobyqa.utils.math',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python313\\site-packages\\scipy\\_lib\\cobyqa\\utils\\math.py',
   'PYMODULE'),
  ('scipy._lib.cobyqa.utils.exceptions',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python313\\site-packages\\scipy\\_lib\\cobyqa\\utils\\exceptions.py',
   'PYMODULE'),
  ('scipy._lib.cobyqa.main',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python313\\site-packages\\scipy\\_lib\\cobyqa\\main.py',
   'PYMODULE'),
  ('scipy._lib.cobyqa.settings',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python313\\site-packages\\scipy\\_lib\\cobyqa\\settings.py',
   'PYMODULE'),
  ('scipy._lib.cobyqa.problem',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python313\\site-packages\\scipy\\_lib\\cobyqa\\problem.py',
   'PYMODULE'),
  ('scipy._lib.cobyqa.framework',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python313\\site-packages\\scipy\\_lib\\cobyqa\\framework.py',
   'PYMODULE'),
  ('scipy._lib.cobyqa.subsolvers.optim',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python313\\site-packages\\scipy\\_lib\\cobyqa\\subsolvers\\optim.py',
   'PYMODULE'),
  ('scipy._lib.cobyqa.subsolvers',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python313\\site-packages\\scipy\\_lib\\cobyqa\\subsolvers\\__init__.py',
   'PYMODULE'),
  ('scipy._lib.cobyqa.subsolvers.geometry',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python313\\site-packages\\scipy\\_lib\\cobyqa\\subsolvers\\geometry.py',
   'PYMODULE'),
  ('scipy._lib.cobyqa.models',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python313\\site-packages\\scipy\\_lib\\cobyqa\\models.py',
   'PYMODULE'),
  ('scipy.optimize._trustregion_constr',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python313\\site-packages\\scipy\\optimize\\_trustregion_constr\\__init__.py',
   'PYMODULE'),
  ('scipy.optimize._trustregion_constr.minimize_trustregion_constr',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python313\\site-packages\\scipy\\optimize\\_trustregion_constr\\minimize_trustregion_constr.py',
   'PYMODULE'),
  ('scipy.optimize._trustregion_constr.report',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python313\\site-packages\\scipy\\optimize\\_trustregion_constr\\report.py',
   'PYMODULE'),
  ('scipy.optimize._trustregion_constr.tr_interior_point',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python313\\site-packages\\scipy\\optimize\\_trustregion_constr\\tr_interior_point.py',
   'PYMODULE'),
  ('scipy.optimize._trustregion_constr.canonical_constraint',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python313\\site-packages\\scipy\\optimize\\_trustregion_constr\\canonical_constraint.py',
   'PYMODULE'),
  ('scipy.optimize._trustregion_constr.equality_constrained_sqp',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python313\\site-packages\\scipy\\optimize\\_trustregion_constr\\equality_constrained_sqp.py',
   'PYMODULE'),
  ('scipy.optimize._trustregion_constr.qp_subproblem',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python313\\site-packages\\scipy\\optimize\\_trustregion_constr\\qp_subproblem.py',
   'PYMODULE'),
  ('scipy.optimize._trustregion_constr.projections',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python313\\site-packages\\scipy\\optimize\\_trustregion_constr\\projections.py',
   'PYMODULE'),
  ('scipy.optimize._trustregion_exact',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python313\\site-packages\\scipy\\optimize\\_trustregion_exact.py',
   'PYMODULE'),
  ('scipy.optimize._trustregion',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python313\\site-packages\\scipy\\optimize\\_trustregion.py',
   'PYMODULE'),
  ('scipy.optimize._trustregion_krylov',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python313\\site-packages\\scipy\\optimize\\_trustregion_krylov.py',
   'PYMODULE'),
  ('scipy.optimize._trlib',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python313\\site-packages\\scipy\\optimize\\_trlib\\__init__.py',
   'PYMODULE'),
  ('scipy.optimize._trustregion_ncg',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python313\\site-packages\\scipy\\optimize\\_trustregion_ncg.py',
   'PYMODULE'),
  ('scipy.optimize._trustregion_dogleg',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python313\\site-packages\\scipy\\optimize\\_trustregion_dogleg.py',
   'PYMODULE'),
  ('scipy.optimize._optimize',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python313\\site-packages\\scipy\\optimize\\_optimize.py',
   'PYMODULE'),
  ('scipy.integrate._ivp.radau',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python313\\site-packages\\scipy\\integrate\\_ivp\\radau.py',
   'PYMODULE'),
  ('scipy.integrate._ivp.rk',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python313\\site-packages\\scipy\\integrate\\_ivp\\rk.py',
   'PYMODULE'),
  ('scipy.integrate._ivp.ivp',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python313\\site-packages\\scipy\\integrate\\_ivp\\ivp.py',
   'PYMODULE'),
  ('scipy.integrate._ivp.dop853_coefficients',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python313\\site-packages\\scipy\\integrate\\_ivp\\dop853_coefficients.py',
   'PYMODULE'),
  ('scipy.integrate._bvp',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python313\\site-packages\\scipy\\integrate\\_bvp.py',
   'PYMODULE'),
  ('scipy.integrate._ode',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python313\\site-packages\\scipy\\integrate\\_ode.py',
   'PYMODULE'),
  ('scipy.integrate._quadpack_py',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python313\\site-packages\\scipy\\integrate\\_quadpack_py.py',
   'PYMODULE'),
  ('scipy.integrate._odepack_py',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python313\\site-packages\\scipy\\integrate\\_odepack_py.py',
   'PYMODULE'),
  ('scipy.integrate._quadrature',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python313\\site-packages\\scipy\\integrate\\_quadrature.py',
   'PYMODULE'),
  ('scipy.special._spfun_stats',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python313\\site-packages\\scipy\\special\\_spfun_stats.py',
   'PYMODULE'),
  ('scipy.special._orthogonal',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python313\\site-packages\\scipy\\special\\_orthogonal.py',
   'PYMODULE'),
  ('scipy.special._multiufuncs',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python313\\site-packages\\scipy\\special\\_multiufuncs.py',
   'PYMODULE'),
  ('scipy.special._input_validation',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python313\\site-packages\\scipy\\special\\_input_validation.py',
   'PYMODULE'),
  ('scipy.special._logsumexp',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python313\\site-packages\\scipy\\special\\_logsumexp.py',
   'PYMODULE'),
  ('scipy.special._basic',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python313\\site-packages\\scipy\\special\\_basic.py',
   'PYMODULE'),
  ('scipy.special._support_alternative_backends',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python313\\site-packages\\scipy\\special\\_support_alternative_backends.py',
   'PYMODULE'),
  ('scipy.optimize.elementwise',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python313\\site-packages\\scipy\\optimize\\elementwise.py',
   'PYMODULE'),
  ('scipy.optimize._elementwise',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python313\\site-packages\\scipy\\optimize\\_elementwise.py',
   'PYMODULE'),
  ('scipy.special._sf_error',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python313\\site-packages\\scipy\\special\\_sf_error.py',
   'PYMODULE'),
  ('scipy.linalg._matfuncs',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python313\\site-packages\\scipy\\linalg\\_matfuncs.py',
   'PYMODULE'),
  ('scipy.linalg._matfuncs_inv_ssq',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python313\\site-packages\\scipy\\linalg\\_matfuncs_inv_ssq.py',
   'PYMODULE'),
  ('scipy.linalg._matfuncs_sqrtm',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python313\\site-packages\\scipy\\linalg\\_matfuncs_sqrtm.py',
   'PYMODULE'),
  ('scipy.linalg._expm_frechet',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python313\\site-packages\\scipy\\linalg\\_expm_frechet.py',
   'PYMODULE'),
  ('scipy.linalg._decomp_polar',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python313\\site-packages\\scipy\\linalg\\_decomp_polar.py',
   'PYMODULE'),
  ('scipy.linalg._decomp_schur',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python313\\site-packages\\scipy\\linalg\\_decomp_schur.py',
   'PYMODULE'),
  ('scipy.linalg._decomp_qz',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python313\\site-packages\\scipy\\linalg\\_decomp_qz.py',
   'PYMODULE'),
  ('scipy.linalg._decomp_cholesky',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python313\\site-packages\\scipy\\linalg\\_decomp_cholesky.py',
   'PYMODULE'),
  ('scipy.linalg._decomp_ldl',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python313\\site-packages\\scipy\\linalg\\_decomp_ldl.py',
   'PYMODULE'),
  ('scipy.linalg._decomp_lu',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python313\\site-packages\\scipy\\linalg\\_decomp_lu.py',
   'PYMODULE'),
  ('scipy.linalg._decomp',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python313\\site-packages\\scipy\\linalg\\_decomp.py',
   'PYMODULE'),
  ('scipy.linalg._basic',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python313\\site-packages\\scipy\\linalg\\_basic.py',
   'PYMODULE'),
  ('scipy.linalg._decomp_svd',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python313\\site-packages\\scipy\\linalg\\_decomp_svd.py',
   'PYMODULE'),
  ('scipy.sparse.linalg._norm',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python313\\site-packages\\scipy\\sparse\\linalg\\_norm.py',
   'PYMODULE'),
  ('scipy.sparse.linalg._onenormest',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python313\\site-packages\\scipy\\sparse\\linalg\\_onenormest.py',
   'PYMODULE'),
  ('scipy.sparse.linalg._matfuncs',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python313\\site-packages\\scipy\\sparse\\linalg\\_matfuncs.py',
   'PYMODULE'),
  ('scipy.sparse.linalg._eigen',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python313\\site-packages\\scipy\\sparse\\linalg\\_eigen\\__init__.py',
   'PYMODULE'),
  ('scipy.sparse.linalg._eigen._svds',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python313\\site-packages\\scipy\\sparse\\linalg\\_eigen\\_svds.py',
   'PYMODULE'),
  ('scipy.sparse.linalg._svdp',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python313\\site-packages\\scipy\\sparse\\linalg\\_svdp.py',
   'PYMODULE'),
  ('scipy.sparse.linalg._propack', '-', 'PYMODULE'),
  ('scipy.sparse.linalg._eigen.lobpcg.lobpcg',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python313\\site-packages\\scipy\\sparse\\linalg\\_eigen\\lobpcg\\lobpcg.py',
   'PYMODULE'),
  ('scipy.sparse.linalg._eigen.lobpcg',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python313\\site-packages\\scipy\\sparse\\linalg\\_eigen\\lobpcg\\__init__.py',
   'PYMODULE'),
  ('scipy.sparse.linalg._eigen.arpack',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python313\\site-packages\\scipy\\sparse\\linalg\\_eigen\\arpack\\__init__.py',
   'PYMODULE'),
  ('scipy.sparse.linalg._eigen.arpack.arpack',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python313\\site-packages\\scipy\\sparse\\linalg\\_eigen\\arpack\\arpack.py',
   'PYMODULE'),
  ('scipy._lib._threadsafety',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python313\\site-packages\\scipy\\_lib\\_threadsafety.py',
   'PYMODULE'),
  ('scipy._lib.decorator',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python313\\site-packages\\scipy\\_lib\\decorator.py',
   'PYMODULE'),
  ('scipy.sparse.linalg._interface',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python313\\site-packages\\scipy\\sparse\\linalg\\_interface.py',
   'PYMODULE'),
  ('scipy.sparse.linalg._dsolve',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python313\\site-packages\\scipy\\sparse\\linalg\\_dsolve\\__init__.py',
   'PYMODULE'),
  ('scipy.sparse.linalg._dsolve._add_newdocs',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python313\\site-packages\\scipy\\sparse\\linalg\\_dsolve\\_add_newdocs.py',
   'PYMODULE'),
  ('scipy.sparse.linalg._dsolve.linsolve',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python313\\site-packages\\scipy\\sparse\\linalg\\_dsolve\\linsolve.py',
   'PYMODULE'),
  ('scipy.sparse.linalg._isolve',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python313\\site-packages\\scipy\\sparse\\linalg\\_isolve\\__init__.py',
   'PYMODULE'),
  ('scipy.sparse.linalg._isolve.tfqmr',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python313\\site-packages\\scipy\\sparse\\linalg\\_isolve\\tfqmr.py',
   'PYMODULE'),
  ('scipy.sparse.linalg._isolve.utils',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python313\\site-packages\\scipy\\sparse\\linalg\\_isolve\\utils.py',
   'PYMODULE'),
  ('scipy.sparse.linalg._isolve._gcrotmk',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python313\\site-packages\\scipy\\sparse\\linalg\\_isolve\\_gcrotmk.py',
   'PYMODULE'),
  ('scipy.sparse.linalg._isolve.lsmr',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python313\\site-packages\\scipy\\sparse\\linalg\\_isolve\\lsmr.py',
   'PYMODULE'),
  ('scipy.sparse.linalg._isolve.lsqr',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python313\\site-packages\\scipy\\sparse\\linalg\\_isolve\\lsqr.py',
   'PYMODULE'),
  ('scipy.sparse.linalg._isolve.lgmres',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python313\\site-packages\\scipy\\sparse\\linalg\\_isolve\\lgmres.py',
   'PYMODULE'),
  ('scipy.sparse.linalg._isolve.minres',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python313\\site-packages\\scipy\\sparse\\linalg\\_isolve\\minres.py',
   'PYMODULE'),
  ('scipy.sparse.linalg._isolve.iterative',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python313\\site-packages\\scipy\\sparse\\linalg\\_isolve\\iterative.py',
   'PYMODULE'),
  ('scipy.interpolate._rgi',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python313\\site-packages\\scipy\\interpolate\\_rgi.py',
   'PYMODULE'),
  ('scipy.interpolate._pade',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python313\\site-packages\\scipy\\interpolate\\_pade.py',
   'PYMODULE'),
  ('scipy.interpolate._fitpack_repro',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python313\\site-packages\\scipy\\interpolate\\_fitpack_repro.py',
   'PYMODULE'),
  ('scipy.interpolate._bsplines',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python313\\site-packages\\scipy\\interpolate\\_bsplines.py',
   'PYMODULE'),
  ('scipy.interpolate._fitpack_impl',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python313\\site-packages\\scipy\\interpolate\\_fitpack_impl.py',
   'PYMODULE'),
  ('scipy.interpolate._ndgriddata',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python313\\site-packages\\scipy\\interpolate\\_ndgriddata.py',
   'PYMODULE'),
  ('scipy.interpolate._cubic',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python313\\site-packages\\scipy\\interpolate\\_cubic.py',
   'PYMODULE'),
  ('scipy.interpolate._polyint',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python313\\site-packages\\scipy\\interpolate\\_polyint.py',
   'PYMODULE'),
  ('scipy.interpolate._rbfinterp',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python313\\site-packages\\scipy\\interpolate\\_rbfinterp.py',
   'PYMODULE'),
  ('scipy.interpolate._rbf',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python313\\site-packages\\scipy\\interpolate\\_rbf.py',
   'PYMODULE'),
  ('scipy.interpolate._fitpack2',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python313\\site-packages\\scipy\\interpolate\\_fitpack2.py',
   'PYMODULE'),
  ('scipy.interpolate._interpolate',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python313\\site-packages\\scipy\\interpolate\\_interpolate.py',
   'PYMODULE'),
  ('scipy.interpolate._fitpack_py',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python313\\site-packages\\scipy\\interpolate\\_fitpack_py.py',
   'PYMODULE'),
  ('scipy.spatial',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python313\\site-packages\\scipy\\spatial\\__init__.py',
   'PYMODULE'),
  ('scipy.spatial.transform',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python313\\site-packages\\scipy\\spatial\\transform\\__init__.py',
   'PYMODULE'),
  ('scipy.spatial.transform.rotation',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python313\\site-packages\\scipy\\spatial\\transform\\rotation.py',
   'PYMODULE'),
  ('scipy.spatial.transform._rotation_groups',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python313\\site-packages\\scipy\\spatial\\transform\\_rotation_groups.py',
   'PYMODULE'),
  ('scipy.constants',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python313\\site-packages\\scipy\\constants\\__init__.py',
   'PYMODULE'),
  ('scipy.constants.constants',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python313\\site-packages\\scipy\\constants\\constants.py',
   'PYMODULE'),
  ('scipy.constants.codata',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python313\\site-packages\\scipy\\constants\\codata.py',
   'PYMODULE'),
  ('scipy.constants._constants',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python313\\site-packages\\scipy\\constants\\_constants.py',
   'PYMODULE'),
  ('scipy.constants._codata',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python313\\site-packages\\scipy\\constants\\_codata.py',
   'PYMODULE'),
  ('scipy.spatial.transform._rotation_spline',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python313\\site-packages\\scipy\\spatial\\transform\\_rotation_spline.py',
   'PYMODULE'),
  ('scipy.spatial.qhull',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python313\\site-packages\\scipy\\spatial\\qhull.py',
   'PYMODULE'),
  ('scipy.spatial.kdtree',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python313\\site-packages\\scipy\\spatial\\kdtree.py',
   'PYMODULE'),
  ('scipy.spatial.ckdtree',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python313\\site-packages\\scipy\\spatial\\ckdtree.py',
   'PYMODULE'),
  ('scipy.spatial._geometric_slerp',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python313\\site-packages\\scipy\\spatial\\_geometric_slerp.py',
   'PYMODULE'),
  ('scipy.spatial._procrustes',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python313\\site-packages\\scipy\\spatial\\_procrustes.py',
   'PYMODULE'),
  ('scipy.spatial._plotutils',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python313\\site-packages\\scipy\\spatial\\_plotutils.py',
   'PYMODULE'),
  ('scipy.spatial._spherical_voronoi',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python313\\site-packages\\scipy\\spatial\\_spherical_voronoi.py',
   'PYMODULE'),
  ('scipy.spatial._kdtree',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python313\\site-packages\\scipy\\spatial\\_kdtree.py',
   'PYMODULE'),
  ('scipy.ndimage',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python313\\site-packages\\scipy\\ndimage\\__init__.py',
   'PYMODULE'),
  ('scipy.ndimage.morphology',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python313\\site-packages\\scipy\\ndimage\\morphology.py',
   'PYMODULE'),
  ('scipy.ndimage.measurements',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python313\\site-packages\\scipy\\ndimage\\measurements.py',
   'PYMODULE'),
  ('scipy.ndimage.interpolation',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python313\\site-packages\\scipy\\ndimage\\interpolation.py',
   'PYMODULE'),
  ('scipy.ndimage.fourier',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python313\\site-packages\\scipy\\ndimage\\fourier.py',
   'PYMODULE'),
  ('scipy.ndimage.filters',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python313\\site-packages\\scipy\\ndimage\\filters.py',
   'PYMODULE'),
  ('scipy.ndimage._support_alternative_backends',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python313\\site-packages\\scipy\\ndimage\\_support_alternative_backends.py',
   'PYMODULE'),
  ('scipy.ndimage._ndimage_api',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python313\\site-packages\\scipy\\ndimage\\_ndimage_api.py',
   'PYMODULE'),
  ('scipy.ndimage._interpolation',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python313\\site-packages\\scipy\\ndimage\\_interpolation.py',
   'PYMODULE'),
  ('scipy.ndimage._fourier',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python313\\site-packages\\scipy\\ndimage\\_fourier.py',
   'PYMODULE'),
  ('scipy.ndimage._delegators',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python313\\site-packages\\scipy\\ndimage\\_delegators.py',
   'PYMODULE'),
  ('scipy',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python313\\site-packages\\scipy\\__init__.py',
   'PYMODULE'),
  ('numpy.f2py.use_rules',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python313\\site-packages\\numpy\\f2py\\use_rules.py',
   'PYMODULE'),
  ('numpy.f2py.symbolic',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python313\\site-packages\\numpy\\f2py\\symbolic.py',
   'PYMODULE'),
  ('numpy.f2py.rules',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python313\\site-packages\\numpy\\f2py\\rules.py',
   'PYMODULE'),
  ('numpy.f2py.func2subr',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python313\\site-packages\\numpy\\f2py\\func2subr.py',
   'PYMODULE'),
  ('numpy.f2py.f90mod_rules',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python313\\site-packages\\numpy\\f2py\\f90mod_rules.py',
   'PYMODULE'),
  ('numpy.f2py.f2py2e',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python313\\site-packages\\numpy\\f2py\\f2py2e.py',
   'PYMODULE'),
  ('numpy.f2py.diagnose',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python313\\site-packages\\numpy\\f2py\\diagnose.py',
   'PYMODULE'),
  ('numpy.f2py.crackfortran',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python313\\site-packages\\numpy\\f2py\\crackfortran.py',
   'PYMODULE'),
  ('charset_normalizer',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python313\\site-packages\\charset_normalizer\\__init__.py',
   'PYMODULE'),
  ('charset_normalizer.version',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python313\\site-packages\\charset_normalizer\\version.py',
   'PYMODULE'),
  ('charset_normalizer.utils',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python313\\site-packages\\charset_normalizer\\utils.py',
   'PYMODULE'),
  ('charset_normalizer.constant',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python313\\site-packages\\charset_normalizer\\constant.py',
   'PYMODULE'),
  ('charset_normalizer.models',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python313\\site-packages\\charset_normalizer\\models.py',
   'PYMODULE'),
  ('charset_normalizer.cd',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python313\\site-packages\\charset_normalizer\\cd.py',
   'PYMODULE'),
  ('charset_normalizer.legacy',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python313\\site-packages\\charset_normalizer\\legacy.py',
   'PYMODULE'),
  ('charset_normalizer.api',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python313\\site-packages\\charset_normalizer\\api.py',
   'PYMODULE'),
  ('fileinput', 'C:\\Python313\\Lib\\fileinput.py', 'PYMODULE'),
  ('numpy.f2py.common_rules',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python313\\site-packages\\numpy\\f2py\\common_rules.py',
   'PYMODULE'),
  ('numpy.f2py.cfuncs',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python313\\site-packages\\numpy\\f2py\\cfuncs.py',
   'PYMODULE'),
  ('numpy.f2py.cb_rules',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python313\\site-packages\\numpy\\f2py\\cb_rules.py',
   'PYMODULE'),
  ('numpy.f2py.capi_maps',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python313\\site-packages\\numpy\\f2py\\capi_maps.py',
   'PYMODULE'),
  ('numpy.f2py.auxfuncs',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python313\\site-packages\\numpy\\f2py\\auxfuncs.py',
   'PYMODULE'),
  ('numpy.f2py._src_pyf',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python313\\site-packages\\numpy\\f2py\\_src_pyf.py',
   'PYMODULE'),
  ('numpy.f2py._isocbind',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python313\\site-packages\\numpy\\f2py\\_isocbind.py',
   'PYMODULE'),
  ('numpy.f2py._backends._meson',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python313\\site-packages\\numpy\\f2py\\_backends\\_meson.py',
   'PYMODULE'),
  ('numpy.f2py._backends._distutils',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python313\\site-packages\\numpy\\f2py\\_backends\\_distutils.py',
   'PYMODULE'),
  ('numpy.f2py._backends._backend',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python313\\site-packages\\numpy\\f2py\\_backends\\_backend.py',
   'PYMODULE'),
  ('numpy.f2py._backends',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python313\\site-packages\\numpy\\f2py\\_backends\\__init__.py',
   'PYMODULE'),
  ('numpy.f2py.__version__',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python313\\site-packages\\numpy\\f2py\\__version__.py',
   'PYMODULE'),
  ('numpy.f2py.__main__',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python313\\site-packages\\numpy\\f2py\\__main__.py',
   'PYMODULE'),
  ('numpy.f2py',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python313\\site-packages\\numpy\\f2py\\__init__.py',
   'PYMODULE'),
  ('scipy._lib.array_api_compat.numpy.fft',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python313\\site-packages\\scipy\\_lib\\array_api_compat\\numpy\\fft.py',
   'PYMODULE'),
  ('numpy.fft',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python313\\site-packages\\numpy\\fft\\__init__.py',
   'PYMODULE'),
  ('numpy.fft.helper',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python313\\site-packages\\numpy\\fft\\helper.py',
   'PYMODULE'),
  ('numpy.fft._helper',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python313\\site-packages\\numpy\\fft\\_helper.py',
   'PYMODULE'),
  ('numpy.fft._pocketfft',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python313\\site-packages\\numpy\\fft\\_pocketfft.py',
   'PYMODULE'),
  ('scipy._distributor_init',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python313\\site-packages\\scipy\\_distributor_init.py',
   'PYMODULE'),
  ('scipy.version',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python313\\site-packages\\scipy\\version.py',
   'PYMODULE'),
  ('scipy.__config__',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python313\\site-packages\\scipy\\__config__.py',
   'PYMODULE'),
  ('yaml',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python313\\site-packages\\yaml\\__init__.py',
   'PYMODULE'),
  ('yaml.cyaml',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python313\\site-packages\\yaml\\cyaml.py',
   'PYMODULE'),
  ('yaml.resolver',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python313\\site-packages\\yaml\\resolver.py',
   'PYMODULE'),
  ('yaml.representer',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python313\\site-packages\\yaml\\representer.py',
   'PYMODULE'),
  ('yaml.serializer',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python313\\site-packages\\yaml\\serializer.py',
   'PYMODULE'),
  ('yaml.constructor',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python313\\site-packages\\yaml\\constructor.py',
   'PYMODULE'),
  ('yaml.dumper',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python313\\site-packages\\yaml\\dumper.py',
   'PYMODULE'),
  ('yaml.emitter',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python313\\site-packages\\yaml\\emitter.py',
   'PYMODULE'),
  ('yaml.loader',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python313\\site-packages\\yaml\\loader.py',
   'PYMODULE'),
  ('yaml.composer',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python313\\site-packages\\yaml\\composer.py',
   'PYMODULE'),
  ('yaml.parser',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python313\\site-packages\\yaml\\parser.py',
   'PYMODULE'),
  ('yaml.scanner',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python313\\site-packages\\yaml\\scanner.py',
   'PYMODULE'),
  ('yaml.reader',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python313\\site-packages\\yaml\\reader.py',
   'PYMODULE'),
  ('yaml.nodes',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python313\\site-packages\\yaml\\nodes.py',
   'PYMODULE'),
  ('yaml.events',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python313\\site-packages\\yaml\\events.py',
   'PYMODULE'),
  ('yaml.tokens',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python313\\site-packages\\yaml\\tokens.py',
   'PYMODULE'),
  ('yaml.error',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python313\\site-packages\\yaml\\error.py',
   'PYMODULE'),
  ('numpy',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python313\\site-packages\\numpy\\__init__.py',
   'PYMODULE'),
  ('numpy.strings',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python313\\site-packages\\numpy\\strings\\__init__.py',
   'PYMODULE'),
  ('numpy._core.strings',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python313\\site-packages\\numpy\\_core\\strings.py',
   'PYMODULE'),
  ('numpy.char',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python313\\site-packages\\numpy\\char\\__init__.py',
   'PYMODULE'),
  ('numpy._core.defchararray',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python313\\site-packages\\numpy\\_core\\defchararray.py',
   'PYMODULE'),
  ('numpy.rec',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python313\\site-packages\\numpy\\rec\\__init__.py',
   'PYMODULE'),
  ('numpy.matlib',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python313\\site-packages\\numpy\\matlib.py',
   'PYMODULE'),
  ('numpy.ctypeslib',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python313\\site-packages\\numpy\\ctypeslib.py',
   'PYMODULE'),
  ('numpy.dtypes',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python313\\site-packages\\numpy\\dtypes.py',
   'PYMODULE'),
  ('numpy._array_api_info',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python313\\site-packages\\numpy\\_array_api_info.py',
   'PYMODULE'),
  ('numpy.__config__',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python313\\site-packages\\numpy\\__config__.py',
   'PYMODULE'),
  ('numpy._distributor_init',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python313\\site-packages\\numpy\\_distributor_init.py',
   'PYMODULE'),
  ('numpy._expired_attrs_2_0',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python313\\site-packages\\numpy\\_expired_attrs_2_0.py',
   'PYMODULE'),
  ('pyproj.transformer',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python313\\site-packages\\pyproj\\transformer.py',
   'PYMODULE'),
  ('pyproj.utils',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python313\\site-packages\\pyproj\\utils.py',
   'PYMODULE'),
  ('pyproj.sync',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python313\\site-packages\\pyproj\\sync.py',
   'PYMODULE'),
  ('pyproj.aoi',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python313\\site-packages\\pyproj\\aoi.py',
   'PYMODULE'),
  ('pyproj.exceptions',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python313\\site-packages\\pyproj\\exceptions.py',
   'PYMODULE'),
  ('pyproj.enums',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python313\\site-packages\\pyproj\\enums.py',
   'PYMODULE'),
  ('pyproj.datadir',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python313\\site-packages\\pyproj\\datadir.py',
   'PYMODULE'),
  ('pyproj.crs.enums',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python313\\site-packages\\pyproj\\crs\\enums.py',
   'PYMODULE'),
  ('pyproj.crs',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python313\\site-packages\\pyproj\\crs\\__init__.py',
   'PYMODULE'),
  ('pyproj.crs.crs',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python313\\site-packages\\pyproj\\crs\\crs.py',
   'PYMODULE'),
  ('pyproj.geod',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python313\\site-packages\\pyproj\\geod.py',
   'PYMODULE'),
  ('pyproj.crs.coordinate_system',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python313\\site-packages\\pyproj\\crs\\coordinate_system.py',
   'PYMODULE'),
  ('pyproj.crs.coordinate_operation',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python313\\site-packages\\pyproj\\crs\\coordinate_operation.py',
   'PYMODULE'),
  ('pyproj.crs._cf1x8',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python313\\site-packages\\pyproj\\crs\\_cf1x8.py',
   'PYMODULE'),
  ('pyproj.crs.datum',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python313\\site-packages\\pyproj\\crs\\datum.py',
   'PYMODULE'),
  ('pyproj',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python313\\site-packages\\pyproj\\__init__.py',
   'PYMODULE'),
  ('pyproj.proj',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python313\\site-packages\\pyproj\\proj.py',
   'PYMODULE'),
  ('pyproj._show_versions',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python313\\site-packages\\pyproj\\_show_versions.py',
   'PYMODULE'),
  ('pyproj.network',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python313\\site-packages\\pyproj\\network.py',
   'PYMODULE'),
  ('certifi',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python313\\site-packages\\certifi\\__init__.py',
   'PYMODULE'),
  ('certifi.core',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python313\\site-packages\\certifi\\core.py',
   'PYMODULE'),
  ('shapely.prepared',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python313\\site-packages\\shapely\\prepared.py',
   'PYMODULE'),
  ('shapely.ops',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python313\\site-packages\\shapely\\ops.py',
   'PYMODULE'),
  ('shapely.geometry.base',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python313\\site-packages\\shapely\\geometry\\base.py',
   'PYMODULE'),
  ('shapely.decorators',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python313\\site-packages\\shapely\\decorators.py',
   'PYMODULE'),
  ('shapely.coords',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python313\\site-packages\\shapely\\coords.py',
   'PYMODULE'),
  ('shapely.constructive',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python313\\site-packages\\shapely\\constructive.py',
   'PYMODULE'),
  ('shapely.algorithms.cga',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python313\\site-packages\\shapely\\algorithms\\cga.py',
   'PYMODULE'),
  ('shapely.algorithms',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python313\\site-packages\\shapely\\algorithms\\__init__.py',
   'PYMODULE'),
  ('shapely.algorithms._oriented_envelope',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python313\\site-packages\\shapely\\algorithms\\_oriented_envelope.py',
   'PYMODULE'),
  ('shapely.affinity',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python313\\site-packages\\shapely\\affinity.py',
   'PYMODULE'),
  ('shapely._enum',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python313\\site-packages\\shapely\\_enum.py',
   'PYMODULE'),
  ('shapely.errors',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python313\\site-packages\\shapely\\errors.py',
   'PYMODULE'),
  ('shapely.algorithms.polylabel',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python313\\site-packages\\shapely\\algorithms\\polylabel.py',
   'PYMODULE'),
  ('shapely._geometry',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python313\\site-packages\\shapely\\_geometry.py',
   'PYMODULE'),
  ('shapely.geometry',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python313\\site-packages\\shapely\\geometry\\__init__.py',
   'PYMODULE'),
  ('shapely.geometry.point',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python313\\site-packages\\shapely\\geometry\\point.py',
   'PYMODULE'),
  ('shapely.geometry.multipolygon',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python313\\site-packages\\shapely\\geometry\\multipolygon.py',
   'PYMODULE'),
  ('shapely.geometry.multipoint',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python313\\site-packages\\shapely\\geometry\\multipoint.py',
   'PYMODULE'),
  ('shapely.geometry.multilinestring',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python313\\site-packages\\shapely\\geometry\\multilinestring.py',
   'PYMODULE'),
  ('shapely.geometry.linestring',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python313\\site-packages\\shapely\\geometry\\linestring.py',
   'PYMODULE'),
  ('shapely.geometry.geo',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python313\\site-packages\\shapely\\geometry\\geo.py',
   'PYMODULE'),
  ('shapely.geometry.polygon',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python313\\site-packages\\shapely\\geometry\\polygon.py',
   'PYMODULE'),
  ('shapely.geometry.collection',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python313\\site-packages\\shapely\\geometry\\collection.py',
   'PYMODULE'),
  ('shapely',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python313\\site-packages\\shapely\\__init__.py',
   'PYMODULE'),
  ('shapely._version',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python313\\site-packages\\shapely\\_version.py',
   'PYMODULE'),
  ('shapely._coverage',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python313\\site-packages\\shapely\\_coverage.py',
   'PYMODULE'),
  ('shapely.io',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python313\\site-packages\\shapely\\io.py',
   'PYMODULE'),
  ('shapely._ragged_array',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python313\\site-packages\\shapely\\_ragged_array.py',
   'PYMODULE'),
  ('shapely.strtree',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python313\\site-packages\\shapely\\strtree.py',
   'PYMODULE'),
  ('shapely.coordinates',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python313\\site-packages\\shapely\\coordinates.py',
   'PYMODULE'),
  ('shapely.linear',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python313\\site-packages\\shapely\\linear.py',
   'PYMODULE'),
  ('shapely.set_operations',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python313\\site-packages\\shapely\\set_operations.py',
   'PYMODULE'),
  ('shapely.measurement',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python313\\site-packages\\shapely\\measurement.py',
   'PYMODULE'),
  ('shapely.predicates',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python313\\site-packages\\shapely\\predicates.py',
   'PYMODULE'),
  ('shapely.creation',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python313\\site-packages\\shapely\\creation.py',
   'PYMODULE'),
  ('fiona.transform',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python313\\site-packages\\fiona\\transform.py',
   'PYMODULE'),
  ('fiona.model',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python313\\site-packages\\fiona\\model.py',
   'PYMODULE'),
  ('fiona.errors',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python313\\site-packages\\fiona\\errors.py',
   'PYMODULE'),
  ('fiona.compat',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python313\\site-packages\\fiona\\compat.py',
   'PYMODULE'),
  ('fiona',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python313\\site-packages\\fiona\\__init__.py',
   'PYMODULE'),
  ('fiona.enums',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python313\\site-packages\\fiona\\enums.py',
   'PYMODULE'),
  ('uuid', 'C:\\Python313\\Lib\\uuid.py', 'PYMODULE'),
  ('fiona.rfc3339',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python313\\site-packages\\fiona\\rfc3339.py',
   'PYMODULE'),
  ('fiona.io',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python313\\site-packages\\fiona\\io.py',
   'PYMODULE'),
  ('fiona.meta',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python313\\site-packages\\fiona\\meta.py',
   'PYMODULE'),
  ('xml.etree.ElementTree',
   'C:\\Python313\\Lib\\xml\\etree\\ElementTree.py',
   'PYMODULE'),
  ('xml.etree.cElementTree',
   'C:\\Python313\\Lib\\xml\\etree\\cElementTree.py',
   'PYMODULE'),
  ('xml.etree.ElementInclude',
   'C:\\Python313\\Lib\\xml\\etree\\ElementInclude.py',
   'PYMODULE'),
  ('xml.etree.ElementPath',
   'C:\\Python313\\Lib\\xml\\etree\\ElementPath.py',
   'PYMODULE'),
  ('xml.etree', 'C:\\Python313\\Lib\\xml\\etree\\__init__.py', 'PYMODULE'),
  ('fiona.env',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python313\\site-packages\\fiona\\env.py',
   'PYMODULE'),
  ('fiona.session',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python313\\site-packages\\fiona\\session.py',
   'PYMODULE'),
  ('attr',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python313\\site-packages\\attr\\__init__.py',
   'PYMODULE'),
  ('attr._version_info',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python313\\site-packages\\attr\\_version_info.py',
   'PYMODULE'),
  ('attr._next_gen',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python313\\site-packages\\attr\\_next_gen.py',
   'PYMODULE'),
  ('attr._make',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python313\\site-packages\\attr\\_make.py',
   'PYMODULE'),
  ('attr._compat',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python313\\site-packages\\attr\\_compat.py',
   'PYMODULE'),
  ('attr._funcs',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python313\\site-packages\\attr\\_funcs.py',
   'PYMODULE'),
  ('attr._cmp',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python313\\site-packages\\attr\\_cmp.py',
   'PYMODULE'),
  ('attr.validators',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python313\\site-packages\\attr\\validators.py',
   'PYMODULE'),
  ('attr.filters',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python313\\site-packages\\attr\\filters.py',
   'PYMODULE'),
  ('attr.exceptions',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python313\\site-packages\\attr\\exceptions.py',
   'PYMODULE'),
  ('attr.converters',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python313\\site-packages\\attr\\converters.py',
   'PYMODULE'),
  ('attr.setters',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python313\\site-packages\\attr\\setters.py',
   'PYMODULE'),
  ('attr._config',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python313\\site-packages\\attr\\_config.py',
   'PYMODULE'),
  ('fiona.drvsupport',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python313\\site-packages\\fiona\\drvsupport.py',
   'PYMODULE'),
  ('fiona.collection',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python313\\site-packages\\fiona\\collection.py',
   'PYMODULE'),
  ('fiona.logutils',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python313\\site-packages\\fiona\\logutils.py',
   'PYMODULE'),
  ('fiona.vfs',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python313\\site-packages\\fiona\\vfs.py',
   'PYMODULE'),
  ('fiona._show_versions',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python313\\site-packages\\fiona\\_show_versions.py',
   'PYMODULE'),
  ('fiona._path',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python313\\site-packages\\fiona\\_path.py',
   'PYMODULE'),
  ('rasterio.windows',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python313\\site-packages\\rasterio\\windows.py',
   'PYMODULE'),
  ('affine',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python313\\site-packages\\affine\\__init__.py',
   'PYMODULE'),
  ('rasterio.warp',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python313\\site-packages\\rasterio\\warp.py',
   'PYMODULE'),
  ('rasterio.transform',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python313\\site-packages\\rasterio\\transform.py',
   'PYMODULE'),
  ('rasterio.rpc',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python313\\site-packages\\rasterio\\rpc.py',
   'PYMODULE'),
  ('rasterio.sample',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python313\\site-packages\\rasterio\\sample.py',
   'PYMODULE'),
  ('rasterio.rio',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python313\\site-packages\\rasterio\\rio\\__init__.py',
   'PYMODULE'),
  ('rasterio.profiles',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python313\\site-packages\\rasterio\\profiles.py',
   'PYMODULE'),
  ('rasterio.plot',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python313\\site-packages\\rasterio\\plot.py',
   'PYMODULE'),
  ('rasterio.io',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python313\\site-packages\\rasterio\\io.py',
   'PYMODULE'),
  ('rasterio._path',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python313\\site-packages\\rasterio\\_path.py',
   'PYMODULE'),
  ('rasterio.merge',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python313\\site-packages\\rasterio\\merge.py',
   'PYMODULE'),
  ('rasterio.mask',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python313\\site-packages\\rasterio\\mask.py',
   'PYMODULE'),
  ('rasterio.fill',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python313\\site-packages\\rasterio\\fill.py',
   'PYMODULE'),
  ('rasterio.features',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python313\\site-packages\\rasterio\\features.py',
   'PYMODULE'),
  ('rasterio.rio.helpers',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python313\\site-packages\\rasterio\\rio\\helpers.py',
   'PYMODULE'),
  ('click',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python313\\site-packages\\click\\__init__.py',
   'PYMODULE'),
  ('click.utils',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python313\\site-packages\\click\\utils.py',
   'PYMODULE'),
  ('click._compat',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python313\\site-packages\\click\\_compat.py',
   'PYMODULE'),
  ('colorama',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python313\\site-packages\\colorama\\__init__.py',
   'PYMODULE'),
  ('colorama.ansitowin32',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python313\\site-packages\\colorama\\ansitowin32.py',
   'PYMODULE'),
  ('colorama.winterm',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python313\\site-packages\\colorama\\winterm.py',
   'PYMODULE'),
  ('colorama.ansi',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python313\\site-packages\\colorama\\ansi.py',
   'PYMODULE'),
  ('colorama.initialise',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python313\\site-packages\\colorama\\initialise.py',
   'PYMODULE'),
  ('colorama.win32',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python313\\site-packages\\colorama\\win32.py',
   'PYMODULE'),
  ('click._winconsole',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python313\\site-packages\\click\\_winconsole.py',
   'PYMODULE'),
  ('click.termui',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python313\\site-packages\\click\\termui.py',
   'PYMODULE'),
  ('click._termui_impl',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python313\\site-packages\\click\\_termui_impl.py',
   'PYMODULE'),
  ('click.parser',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python313\\site-packages\\click\\parser.py',
   'PYMODULE'),
  ('click.globals',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python313\\site-packages\\click\\globals.py',
   'PYMODULE'),
  ('click.formatting',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python313\\site-packages\\click\\formatting.py',
   'PYMODULE'),
  ('click._textwrap',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python313\\site-packages\\click\\_textwrap.py',
   'PYMODULE'),
  ('click.exceptions',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python313\\site-packages\\click\\exceptions.py',
   'PYMODULE'),
  ('click.decorators',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python313\\site-packages\\click\\decorators.py',
   'PYMODULE'),
  ('click.core',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python313\\site-packages\\click\\core.py',
   'PYMODULE'),
  ('click.shell_completion',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python313\\site-packages\\click\\shell_completion.py',
   'PYMODULE'),
  ('click.types',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python313\\site-packages\\click\\types.py',
   'PYMODULE'),
  ('rasterio.errors',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python313\\site-packages\\rasterio\\errors.py',
   'PYMODULE'),
  ('rasterio.env',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python313\\site-packages\\rasterio\\env.py',
   'PYMODULE'),
  ('rasterio.session',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python313\\site-packages\\rasterio\\session.py',
   'PYMODULE'),
  ('rasterio.dtypes',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python313\\site-packages\\rasterio\\dtypes.py',
   'PYMODULE'),
  ('rasterio.control',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python313\\site-packages\\rasterio\\control.py',
   'PYMODULE'),
  ('rasterio.drivers',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python313\\site-packages\\rasterio\\drivers.py',
   'PYMODULE'),
  ('rasterio',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python313\\site-packages\\rasterio\\__init__.py',
   'PYMODULE'),
  ('rasterio.coords',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python313\\site-packages\\rasterio\\coords.py',
   'PYMODULE'),
  ('rasterio._show_versions',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python313\\site-packages\\rasterio\\_show_versions.py',
   'PYMODULE'),
  ('tracemalloc', 'C:\\Python313\\Lib\\tracemalloc.py', 'PYMODULE'),
  ('_py_abc', 'C:\\Python313\\Lib\\_py_abc.py', 'PYMODULE'),
  ('stringprep', 'C:\\Python313\\Lib\\stringprep.py', 'PYMODULE'),
  ('gui.main_window',
   'E:\\YC\\每日任务\\2025\\07\\0731\\北斗网格转换\\gui\\main_window.py',
   'PYMODULE'),
  ('core.aggregation',
   'E:\\YC\\每日任务\\2025\\07\\0731\\北斗网格转换\\core\\aggregation.py',
   'PYMODULE'),
  ('beidou_grid_processor',
   'E:\\YC\\每日任务\\2025\\07\\0731\\北斗网格转换\\beidou_grid_processor.py',
   'PYMODULE'),
  ('typing', 'C:\\Python313\\Lib\\typing.py', 'PYMODULE'),
  ('logging', 'C:\\Python313\\Lib\\logging\\__init__.py', 'PYMODULE'),
  ('argparse', 'C:\\Python313\\Lib\\argparse.py', 'PYMODULE')],
 [('Shapely.libs\\geos-a5c01bdebd805679c9540218609cd4b8.dll',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python313\\site-packages\\Shapely.libs\\geos-a5c01bdebd805679c9540218609cd4b8.dll',
   'BINARY'),
  ('Shapely.libs\\geos_c-f79418bf8e5cda2d1933e2121ee44e49.dll',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python313\\site-packages\\Shapely.libs\\geos_c-f79418bf8e5cda2d1933e2121ee44e49.dll',
   'BINARY'),
  ('Shapely.libs\\msvcp140-8a79f4687fc453279df1092923244d9e.dll',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python313\\site-packages\\Shapely.libs\\msvcp140-8a79f4687fc453279df1092923244d9e.dll',
   'BINARY'),
  ('python313.dll', 'C:\\Python313\\python313.dll', 'BINARY'),
  ('scipy.libs\\libscipy_openblas-6b2103f2ae4d8547998b5d188e9801fb.dll',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python313\\site-packages\\scipy.libs\\libscipy_openblas-6b2103f2ae4d8547998b5d188e9801fb.dll',
   'BINARY'),
  ('numpy.libs\\msvcp140-263139962577ecda4cd9469ca360a746.dll',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python313\\site-packages\\numpy.libs\\msvcp140-263139962577ecda4cd9469ca360a746.dll',
   'BINARY'),
  ('numpy.libs\\libscipy_openblas64_-43e11ff0749b8cbe0a615c9cf6737e0e.dll',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python313\\site-packages\\numpy.libs\\libscipy_openblas64_-43e11ff0749b8cbe0a615c9cf6737e0e.dll',
   'BINARY'),
  ('pyproj.libs\\proj_9-ece80b3ce99919cf7dd31637c074a1bb.dll',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python313\\site-packages\\pyproj.libs\\proj_9-ece80b3ce99919cf7dd31637c074a1bb.dll',
   'BINARY'),
  ('pyproj.libs\\msvcp140-d76d4b45e040cbc263297f5a5893a46c.dll',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python313\\site-packages\\pyproj.libs\\msvcp140-d76d4b45e040cbc263297f5a5893a46c.dll',
   'BINARY'),
  ('pyproj.libs\\jpeg62-b8ab92f4fbc92367f3262d8a1a71f90b.dll',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python313\\site-packages\\pyproj.libs\\jpeg62-b8ab92f4fbc92367f3262d8a1a71f90b.dll',
   'BINARY'),
  ('pyproj.libs\\tiff-5e90040fe2ccc57bb6a51e477dfe595e.dll',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python313\\site-packages\\pyproj.libs\\tiff-5e90040fe2ccc57bb6a51e477dfe595e.dll',
   'BINARY'),
  ('pyproj.libs\\libcurl-a29cc9317505e6997addcef4c84fa626.dll',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python313\\site-packages\\pyproj.libs\\libcurl-a29cc9317505e6997addcef4c84fa626.dll',
   'BINARY'),
  ('pyproj.libs\\sqlite3-4d959957f8a1ac1287d0dfb03e0e5353.dll',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python313\\site-packages\\pyproj.libs\\sqlite3-4d959957f8a1ac1287d0dfb03e0e5353.dll',
   'BINARY'),
  ('pyproj.libs\\liblzma-559e6637e59731119da5f77161d49851.dll',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python313\\site-packages\\pyproj.libs\\liblzma-559e6637e59731119da5f77161d49851.dll',
   'BINARY'),
  ('pyproj.libs\\zlib1-20140a261acd3e37526970333bcd3057.dll',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python313\\site-packages\\pyproj.libs\\zlib1-20140a261acd3e37526970333bcd3057.dll',
   'BINARY'),
  ('_decimal.pyd', 'C:\\Python313\\DLLs\\_decimal.pyd', 'EXTENSION'),
  ('_hashlib.pyd', 'C:\\Python313\\DLLs\\_hashlib.pyd', 'EXTENSION'),
  ('select.pyd', 'C:\\Python313\\DLLs\\select.pyd', 'EXTENSION'),
  ('_socket.pyd', 'C:\\Python313\\DLLs\\_socket.pyd', 'EXTENSION'),
  ('unicodedata.pyd', 'C:\\Python313\\DLLs\\unicodedata.pyd', 'EXTENSION'),
  ('_lzma.pyd', 'C:\\Python313\\DLLs\\_lzma.pyd', 'EXTENSION'),
  ('_bz2.pyd', 'C:\\Python313\\DLLs\\_bz2.pyd', 'EXTENSION'),
  ('_overlapped.pyd', 'C:\\Python313\\DLLs\\_overlapped.pyd', 'EXTENSION'),
  ('_ssl.pyd', 'C:\\Python313\\DLLs\\_ssl.pyd', 'EXTENSION'),
  ('_queue.pyd', 'C:\\Python313\\DLLs\\_queue.pyd', 'EXTENSION'),
  ('_ctypes.pyd', 'C:\\Python313\\DLLs\\_ctypes.pyd', 'EXTENSION'),
  ('_multiprocessing.pyd',
   'C:\\Python313\\DLLs\\_multiprocessing.pyd',
   'EXTENSION'),
  ('pyexpat.pyd', 'C:\\Python313\\DLLs\\pyexpat.pyd', 'EXTENSION'),
  ('_asyncio.pyd', 'C:\\Python313\\DLLs\\_asyncio.pyd', 'EXTENSION'),
  ('_wmi.pyd', 'C:\\Python313\\DLLs\\_wmi.pyd', 'EXTENSION'),
  ('_tkinter.pyd', 'C:\\Python313\\DLLs\\_tkinter.pyd', 'EXTENSION'),
  ('psutil\\_psutil_windows.pyd',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python313\\site-packages\\psutil\\_psutil_windows.pyd',
   'EXTENSION'),
  ('numpy\\_core\\_multiarray_umath.cp313-win_amd64.pyd',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python313\\site-packages\\numpy\\_core\\_multiarray_umath.cp313-win_amd64.pyd',
   'EXTENSION'),
  ('numpy\\linalg\\_umath_linalg.cp313-win_amd64.pyd',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python313\\site-packages\\numpy\\linalg\\_umath_linalg.cp313-win_amd64.pyd',
   'EXTENSION'),
  ('win32\\win32pdh.pyd',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python313\\site-packages\\win32\\win32pdh.pyd',
   'EXTENSION'),
  ('scipy\\_lib\\_ccallback_c.cp313-win_amd64.pyd',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python313\\site-packages\\scipy\\_lib\\_ccallback_c.cp313-win_amd64.pyd',
   'EXTENSION'),
  ('numpy\\random\\mtrand.cp313-win_amd64.pyd',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python313\\site-packages\\numpy\\random\\mtrand.cp313-win_amd64.pyd',
   'EXTENSION'),
  ('numpy\\random\\_sfc64.cp313-win_amd64.pyd',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python313\\site-packages\\numpy\\random\\_sfc64.cp313-win_amd64.pyd',
   'EXTENSION'),
  ('numpy\\random\\_philox.cp313-win_amd64.pyd',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python313\\site-packages\\numpy\\random\\_philox.cp313-win_amd64.pyd',
   'EXTENSION'),
  ('numpy\\random\\_pcg64.cp313-win_amd64.pyd',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python313\\site-packages\\numpy\\random\\_pcg64.cp313-win_amd64.pyd',
   'EXTENSION'),
  ('numpy\\random\\_mt19937.cp313-win_amd64.pyd',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python313\\site-packages\\numpy\\random\\_mt19937.cp313-win_amd64.pyd',
   'EXTENSION'),
  ('numpy\\random\\bit_generator.cp313-win_amd64.pyd',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python313\\site-packages\\numpy\\random\\bit_generator.cp313-win_amd64.pyd',
   'EXTENSION'),
  ('numpy\\random\\_generator.cp313-win_amd64.pyd',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python313\\site-packages\\numpy\\random\\_generator.cp313-win_amd64.pyd',
   'EXTENSION'),
  ('numpy\\random\\_bounded_integers.cp313-win_amd64.pyd',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python313\\site-packages\\numpy\\random\\_bounded_integers.cp313-win_amd64.pyd',
   'EXTENSION'),
  ('numpy\\random\\_common.cp313-win_amd64.pyd',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python313\\site-packages\\numpy\\random\\_common.cp313-win_amd64.pyd',
   'EXTENSION'),
  ('scipy\\sparse\\_csparsetools.cp313-win_amd64.pyd',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python313\\site-packages\\scipy\\sparse\\_csparsetools.cp313-win_amd64.pyd',
   'EXTENSION'),
  ('scipy\\sparse\\_sparsetools.cp313-win_amd64.pyd',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python313\\site-packages\\scipy\\sparse\\_sparsetools.cp313-win_amd64.pyd',
   'EXTENSION'),
  ('scipy\\linalg\\_fblas.cp313-win_amd64.pyd',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python313\\site-packages\\scipy\\linalg\\_fblas.cp313-win_amd64.pyd',
   'EXTENSION'),
  ('scipy\\linalg\\_flapack.cp313-win_amd64.pyd',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python313\\site-packages\\scipy\\linalg\\_flapack.cp313-win_amd64.pyd',
   'EXTENSION'),
  ('scipy\\linalg\\cython_lapack.cp313-win_amd64.pyd',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python313\\site-packages\\scipy\\linalg\\cython_lapack.cp313-win_amd64.pyd',
   'EXTENSION'),
  ('scipy\\linalg\\cython_blas.cp313-win_amd64.pyd',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python313\\site-packages\\scipy\\linalg\\cython_blas.cp313-win_amd64.pyd',
   'EXTENSION'),
  ('scipy\\linalg\\_decomp_update.cp313-win_amd64.pyd',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python313\\site-packages\\scipy\\linalg\\_decomp_update.cp313-win_amd64.pyd',
   'EXTENSION'),
  ('scipy\\special\\_ellip_harm_2.cp313-win_amd64.pyd',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python313\\site-packages\\scipy\\special\\_ellip_harm_2.cp313-win_amd64.pyd',
   'EXTENSION'),
  ('scipy\\optimize\\_highspy\\_core.cp313-win_amd64.pyd',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python313\\site-packages\\scipy\\optimize\\_highspy\\_core.cp313-win_amd64.pyd',
   'EXTENSION'),
  ('scipy\\optimize\\_highspy\\_highs_options.cp313-win_amd64.pyd',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python313\\site-packages\\scipy\\optimize\\_highspy\\_highs_options.cp313-win_amd64.pyd',
   'EXTENSION'),
  ('scipy\\optimize\\_direct.cp313-win_amd64.pyd',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python313\\site-packages\\scipy\\optimize\\_direct.cp313-win_amd64.pyd',
   'EXTENSION'),
  ('scipy\\stats\\_qmc_cy.cp313-win_amd64.pyd',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python313\\site-packages\\scipy\\stats\\_qmc_cy.cp313-win_amd64.pyd',
   'EXTENSION'),
  ('scipy\\stats\\_sobol.cp313-win_amd64.pyd',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python313\\site-packages\\scipy\\stats\\_sobol.cp313-win_amd64.pyd',
   'EXTENSION'),
  ('scipy\\spatial\\_distance_wrap.cp313-win_amd64.pyd',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python313\\site-packages\\scipy\\spatial\\_distance_wrap.cp313-win_amd64.pyd',
   'EXTENSION'),
  ('scipy\\spatial\\_distance_pybind.cp313-win_amd64.pyd',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python313\\site-packages\\scipy\\spatial\\_distance_pybind.cp313-win_amd64.pyd',
   'EXTENSION'),
  ('scipy\\spatial\\_hausdorff.cp313-win_amd64.pyd',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python313\\site-packages\\scipy\\spatial\\_hausdorff.cp313-win_amd64.pyd',
   'EXTENSION'),
  ('scipy\\sparse\\csgraph\\_tools.cp313-win_amd64.pyd',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python313\\site-packages\\scipy\\sparse\\csgraph\\_tools.cp313-win_amd64.pyd',
   'EXTENSION'),
  ('scipy\\sparse\\csgraph\\_reordering.cp313-win_amd64.pyd',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python313\\site-packages\\scipy\\sparse\\csgraph\\_reordering.cp313-win_amd64.pyd',
   'EXTENSION'),
  ('scipy\\sparse\\csgraph\\_matching.cp313-win_amd64.pyd',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python313\\site-packages\\scipy\\sparse\\csgraph\\_matching.cp313-win_amd64.pyd',
   'EXTENSION'),
  ('scipy\\sparse\\csgraph\\_flow.cp313-win_amd64.pyd',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python313\\site-packages\\scipy\\sparse\\csgraph\\_flow.cp313-win_amd64.pyd',
   'EXTENSION'),
  ('scipy\\sparse\\csgraph\\_min_spanning_tree.cp313-win_amd64.pyd',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python313\\site-packages\\scipy\\sparse\\csgraph\\_min_spanning_tree.cp313-win_amd64.pyd',
   'EXTENSION'),
  ('scipy\\sparse\\csgraph\\_traversal.cp313-win_amd64.pyd',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python313\\site-packages\\scipy\\sparse\\csgraph\\_traversal.cp313-win_amd64.pyd',
   'EXTENSION'),
  ('scipy\\sparse\\csgraph\\_shortest_path.cp313-win_amd64.pyd',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python313\\site-packages\\scipy\\sparse\\csgraph\\_shortest_path.cp313-win_amd64.pyd',
   'EXTENSION'),
  ('scipy\\ndimage\\_rank_filter_1d.cp313-win_amd64.pyd',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python313\\site-packages\\scipy\\ndimage\\_rank_filter_1d.cp313-win_amd64.pyd',
   'EXTENSION'),
  ('scipy\\ndimage\\_nd_image.cp313-win_amd64.pyd',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python313\\site-packages\\scipy\\ndimage\\_nd_image.cp313-win_amd64.pyd',
   'EXTENSION'),
  ('scipy\\ndimage\\_ni_label.cp313-win_amd64.pyd',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python313\\site-packages\\scipy\\ndimage\\_ni_label.cp313-win_amd64.pyd',
   'EXTENSION'),
  ('scipy\\stats\\_stats_pythran.cp313-win_amd64.pyd',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python313\\site-packages\\scipy\\stats\\_stats_pythran.cp313-win_amd64.pyd',
   'EXTENSION'),
  ('scipy\\fft\\_pocketfft\\pypocketfft.cp313-win_amd64.pyd',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python313\\site-packages\\scipy\\fft\\_pocketfft\\pypocketfft.cp313-win_amd64.pyd',
   'EXTENSION'),
  ('scipy\\_lib\\_uarray\\_uarray.cp313-win_amd64.pyd',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python313\\site-packages\\scipy\\_lib\\_uarray\\_uarray.cp313-win_amd64.pyd',
   'EXTENSION'),
  ('scipy\\stats\\_rcont\\rcont.cp313-win_amd64.pyd',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python313\\site-packages\\scipy\\stats\\_rcont\\rcont.cp313-win_amd64.pyd',
   'EXTENSION'),
  ('scipy\\stats\\_qmvnt_cy.cp313-win_amd64.pyd',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python313\\site-packages\\scipy\\stats\\_qmvnt_cy.cp313-win_amd64.pyd',
   'EXTENSION'),
  ('scipy\\stats\\_ansari_swilk_statistics.cp313-win_amd64.pyd',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python313\\site-packages\\scipy\\stats\\_ansari_swilk_statistics.cp313-win_amd64.pyd',
   'EXTENSION'),
  ('scipy\\stats\\_levy_stable\\levyst.cp313-win_amd64.pyd',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python313\\site-packages\\scipy\\stats\\_levy_stable\\levyst.cp313-win_amd64.pyd',
   'EXTENSION'),
  ('scipy\\stats\\_biasedurn.cp313-win_amd64.pyd',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python313\\site-packages\\scipy\\stats\\_biasedurn.cp313-win_amd64.pyd',
   'EXTENSION'),
  ('_cffi_backend.cp313-win_amd64.pyd',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python313\\site-packages\\_cffi_backend.cp313-win_amd64.pyd',
   'EXTENSION'),
  ('scipy\\stats\\_stats.cp313-win_amd64.pyd',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python313\\site-packages\\scipy\\stats\\_stats.cp313-win_amd64.pyd',
   'EXTENSION'),
  ('scipy\\special\\cython_special.cp313-win_amd64.pyd',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python313\\site-packages\\scipy\\special\\cython_special.cp313-win_amd64.pyd',
   'EXTENSION'),
  ('scipy\\optimize\\_pava_pybind.cp313-win_amd64.pyd',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python313\\site-packages\\scipy\\optimize\\_pava_pybind.cp313-win_amd64.pyd',
   'EXTENSION'),
  ('scipy\\optimize\\_lsq\\givens_elimination.cp313-win_amd64.pyd',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python313\\site-packages\\scipy\\optimize\\_lsq\\givens_elimination.cp313-win_amd64.pyd',
   'EXTENSION'),
  ('scipy\\optimize\\_lsap.cp313-win_amd64.pyd',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python313\\site-packages\\scipy\\optimize\\_lsap.cp313-win_amd64.pyd',
   'EXTENSION'),
  ('scipy\\linalg\\_decomp_interpolative.cp313-win_amd64.pyd',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python313\\site-packages\\scipy\\linalg\\_decomp_interpolative.cp313-win_amd64.pyd',
   'EXTENSION'),
  ('scipy\\optimize\\_bglu_dense.cp313-win_amd64.pyd',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python313\\site-packages\\scipy\\optimize\\_bglu_dense.cp313-win_amd64.pyd',
   'EXTENSION'),
  ('scipy\\optimize\\_slsqplib.cp313-win_amd64.pyd',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python313\\site-packages\\scipy\\optimize\\_slsqplib.cp313-win_amd64.pyd',
   'EXTENSION'),
  ('scipy\\optimize\\_zeros.cp313-win_amd64.pyd',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python313\\site-packages\\scipy\\optimize\\_zeros.cp313-win_amd64.pyd',
   'EXTENSION'),
  ('scipy\\optimize\\_minpack.cp313-win_amd64.pyd',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python313\\site-packages\\scipy\\optimize\\_minpack.cp313-win_amd64.pyd',
   'EXTENSION'),
  ('scipy\\optimize\\_trlib\\_trlib.cp313-win_amd64.pyd',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python313\\site-packages\\scipy\\optimize\\_trlib\\_trlib.cp313-win_amd64.pyd',
   'EXTENSION'),
  ('scipy\\optimize\\_moduleTNC.cp313-win_amd64.pyd',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python313\\site-packages\\scipy\\optimize\\_moduleTNC.cp313-win_amd64.pyd',
   'EXTENSION'),
  ('scipy\\optimize\\_lbfgsb.cp313-win_amd64.pyd',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python313\\site-packages\\scipy\\optimize\\_lbfgsb.cp313-win_amd64.pyd',
   'EXTENSION'),
  ('scipy\\optimize\\_group_columns.cp313-win_amd64.pyd',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python313\\site-packages\\scipy\\optimize\\_group_columns.cp313-win_amd64.pyd',
   'EXTENSION'),
  ('scipy\\integrate\\_lsoda.cp313-win_amd64.pyd',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python313\\site-packages\\scipy\\integrate\\_lsoda.cp313-win_amd64.pyd',
   'EXTENSION'),
  ('scipy\\integrate\\_dop.cp313-win_amd64.pyd',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python313\\site-packages\\scipy\\integrate\\_dop.cp313-win_amd64.pyd',
   'EXTENSION'),
  ('scipy\\integrate\\_vode.cp313-win_amd64.pyd',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python313\\site-packages\\scipy\\integrate\\_vode.cp313-win_amd64.pyd',
   'EXTENSION'),
  ('scipy\\integrate\\_quadpack.cp313-win_amd64.pyd',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python313\\site-packages\\scipy\\integrate\\_quadpack.cp313-win_amd64.pyd',
   'EXTENSION'),
  ('scipy\\integrate\\_odepack.cp313-win_amd64.pyd',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python313\\site-packages\\scipy\\integrate\\_odepack.cp313-win_amd64.pyd',
   'EXTENSION'),
  ('scipy\\special\\_gufuncs.cp313-win_amd64.pyd',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python313\\site-packages\\scipy\\special\\_gufuncs.cp313-win_amd64.pyd',
   'EXTENSION'),
  ('scipy\\special\\_special_ufuncs.cp313-win_amd64.pyd',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python313\\site-packages\\scipy\\special\\_special_ufuncs.cp313-win_amd64.pyd',
   'EXTENSION'),
  ('scipy\\special\\_comb.cp313-win_amd64.pyd',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python313\\site-packages\\scipy\\special\\_comb.cp313-win_amd64.pyd',
   'EXTENSION'),
  ('scipy\\special\\_specfun.cp313-win_amd64.pyd',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python313\\site-packages\\scipy\\special\\_specfun.cp313-win_amd64.pyd',
   'EXTENSION'),
  ('scipy\\special\\_ufuncs.cp313-win_amd64.pyd',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python313\\site-packages\\scipy\\special\\_ufuncs.cp313-win_amd64.pyd',
   'EXTENSION'),
  ('scipy\\special\\_ufuncs_cxx.cp313-win_amd64.pyd',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python313\\site-packages\\scipy\\special\\_ufuncs_cxx.cp313-win_amd64.pyd',
   'EXTENSION'),
  ('scipy\\linalg\\_matfuncs_sqrtm_triu.cp313-win_amd64.pyd',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python313\\site-packages\\scipy\\linalg\\_matfuncs_sqrtm_triu.cp313-win_amd64.pyd',
   'EXTENSION'),
  ('scipy\\linalg\\_linalg_pythran.cp313-win_amd64.pyd',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python313\\site-packages\\scipy\\linalg\\_linalg_pythran.cp313-win_amd64.pyd',
   'EXTENSION'),
  ('scipy\\linalg\\_matfuncs_expm.cp313-win_amd64.pyd',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python313\\site-packages\\scipy\\linalg\\_matfuncs_expm.cp313-win_amd64.pyd',
   'EXTENSION'),
  ('scipy\\linalg\\_matfuncs_schur_sqrtm.cp313-win_amd64.pyd',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python313\\site-packages\\scipy\\linalg\\_matfuncs_schur_sqrtm.cp313-win_amd64.pyd',
   'EXTENSION'),
  ('scipy\\linalg\\_decomp_lu_cython.cp313-win_amd64.pyd',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python313\\site-packages\\scipy\\linalg\\_decomp_lu_cython.cp313-win_amd64.pyd',
   'EXTENSION'),
  ('scipy\\linalg\\_solve_toeplitz.cp313-win_amd64.pyd',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python313\\site-packages\\scipy\\linalg\\_solve_toeplitz.cp313-win_amd64.pyd',
   'EXTENSION'),
  ('scipy\\linalg\\_cythonized_array_utils.cp313-win_amd64.pyd',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python313\\site-packages\\scipy\\linalg\\_cythonized_array_utils.cp313-win_amd64.pyd',
   'EXTENSION'),
  ('scipy\\sparse\\linalg\\_propack\\_zpropack.cp313-win_amd64.pyd',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python313\\site-packages\\scipy\\sparse\\linalg\\_propack\\_zpropack.cp313-win_amd64.pyd',
   'EXTENSION'),
  ('scipy\\sparse\\linalg\\_propack\\_cpropack.cp313-win_amd64.pyd',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python313\\site-packages\\scipy\\sparse\\linalg\\_propack\\_cpropack.cp313-win_amd64.pyd',
   'EXTENSION'),
  ('scipy\\sparse\\linalg\\_propack\\_dpropack.cp313-win_amd64.pyd',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python313\\site-packages\\scipy\\sparse\\linalg\\_propack\\_dpropack.cp313-win_amd64.pyd',
   'EXTENSION'),
  ('scipy\\sparse\\linalg\\_propack\\_spropack.cp313-win_amd64.pyd',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python313\\site-packages\\scipy\\sparse\\linalg\\_propack\\_spropack.cp313-win_amd64.pyd',
   'EXTENSION'),
  ('scipy\\sparse\\linalg\\_eigen\\arpack\\_arpack.cp313-win_amd64.pyd',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python313\\site-packages\\scipy\\sparse\\linalg\\_eigen\\arpack\\_arpack.cp313-win_amd64.pyd',
   'EXTENSION'),
  ('scipy\\sparse\\linalg\\_dsolve\\_superlu.cp313-win_amd64.pyd',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python313\\site-packages\\scipy\\sparse\\linalg\\_dsolve\\_superlu.cp313-win_amd64.pyd',
   'EXTENSION'),
  ('scipy\\interpolate\\_rgi_cython.cp313-win_amd64.pyd',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python313\\site-packages\\scipy\\interpolate\\_rgi_cython.cp313-win_amd64.pyd',
   'EXTENSION'),
  ('scipy\\interpolate\\_interpnd.cp313-win_amd64.pyd',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python313\\site-packages\\scipy\\interpolate\\_interpnd.cp313-win_amd64.pyd',
   'EXTENSION'),
  ('scipy\\interpolate\\_rbfinterp_pythran.cp313-win_amd64.pyd',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python313\\site-packages\\scipy\\interpolate\\_rbfinterp_pythran.cp313-win_amd64.pyd',
   'EXTENSION'),
  ('scipy\\interpolate\\_ppoly.cp313-win_amd64.pyd',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python313\\site-packages\\scipy\\interpolate\\_ppoly.cp313-win_amd64.pyd',
   'EXTENSION'),
  ('scipy\\interpolate\\_dierckx.cp313-win_amd64.pyd',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python313\\site-packages\\scipy\\interpolate\\_dierckx.cp313-win_amd64.pyd',
   'EXTENSION'),
  ('scipy\\interpolate\\_dfitpack.cp313-win_amd64.pyd',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python313\\site-packages\\scipy\\interpolate\\_dfitpack.cp313-win_amd64.pyd',
   'EXTENSION'),
  ('scipy\\interpolate\\_fitpack.cp313-win_amd64.pyd',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python313\\site-packages\\scipy\\interpolate\\_fitpack.cp313-win_amd64.pyd',
   'EXTENSION'),
  ('scipy\\spatial\\transform\\_rotation.cp313-win_amd64.pyd',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python313\\site-packages\\scipy\\spatial\\transform\\_rotation.cp313-win_amd64.pyd',
   'EXTENSION'),
  ('scipy\\spatial\\transform\\_rigid_transform.cp313-win_amd64.pyd',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python313\\site-packages\\scipy\\spatial\\transform\\_rigid_transform.cp313-win_amd64.pyd',
   'EXTENSION'),
  ('scipy\\spatial\\_voronoi.cp313-win_amd64.pyd',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python313\\site-packages\\scipy\\spatial\\_voronoi.cp313-win_amd64.pyd',
   'EXTENSION'),
  ('scipy\\spatial\\_qhull.cp313-win_amd64.pyd',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python313\\site-packages\\scipy\\spatial\\_qhull.cp313-win_amd64.pyd',
   'EXTENSION'),
  ('scipy\\spatial\\_ckdtree.cp313-win_amd64.pyd',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python313\\site-packages\\scipy\\spatial\\_ckdtree.cp313-win_amd64.pyd',
   'EXTENSION'),
  ('charset_normalizer\\md__mypyc.cp313-win_amd64.pyd',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python313\\site-packages\\charset_normalizer\\md__mypyc.cp313-win_amd64.pyd',
   'EXTENSION'),
  ('charset_normalizer\\md.cp313-win_amd64.pyd',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python313\\site-packages\\charset_normalizer\\md.cp313-win_amd64.pyd',
   'EXTENSION'),
  ('numpy\\fft\\_pocketfft_umath.cp313-win_amd64.pyd',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python313\\site-packages\\numpy\\fft\\_pocketfft_umath.cp313-win_amd64.pyd',
   'EXTENSION'),
  ('scipy\\_lib\\_fpumode.cp313-win_amd64.pyd',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python313\\site-packages\\scipy\\_lib\\_fpumode.cp313-win_amd64.pyd',
   'EXTENSION'),
  ('scipy\\_lib\\messagestream.cp313-win_amd64.pyd',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python313\\site-packages\\scipy\\_lib\\messagestream.cp313-win_amd64.pyd',
   'EXTENSION'),
  ('yaml\\_yaml.cp313-win_amd64.pyd',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python313\\site-packages\\yaml\\_yaml.cp313-win_amd64.pyd',
   'EXTENSION'),
  ('numpy\\_core\\_multiarray_tests.cp313-win_amd64.pyd',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python313\\site-packages\\numpy\\_core\\_multiarray_tests.cp313-win_amd64.pyd',
   'EXTENSION'),
  ('pyproj\\_sync.cp313-win_amd64.pyd',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python313\\site-packages\\pyproj\\_sync.cp313-win_amd64.pyd',
   'EXTENSION'),
  ('pyproj\\_transformer.cp313-win_amd64.pyd',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python313\\site-packages\\pyproj\\_transformer.cp313-win_amd64.pyd',
   'EXTENSION'),
  ('pyproj\\_crs.cp313-win_amd64.pyd',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python313\\site-packages\\pyproj\\_crs.cp313-win_amd64.pyd',
   'EXTENSION'),
  ('pyproj\\_context.cp313-win_amd64.pyd',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python313\\site-packages\\pyproj\\_context.cp313-win_amd64.pyd',
   'EXTENSION'),
  ('pyproj\\_compat.cp313-win_amd64.pyd',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python313\\site-packages\\pyproj\\_compat.cp313-win_amd64.pyd',
   'EXTENSION'),
  ('pyproj\\list.cp313-win_amd64.pyd',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python313\\site-packages\\pyproj\\list.cp313-win_amd64.pyd',
   'EXTENSION'),
  ('pyproj\\_geod.cp313-win_amd64.pyd',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python313\\site-packages\\pyproj\\_geod.cp313-win_amd64.pyd',
   'EXTENSION'),
  ('pyproj\\_version.cp313-win_amd64.pyd',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python313\\site-packages\\pyproj\\_version.cp313-win_amd64.pyd',
   'EXTENSION'),
  ('pyproj\\database.cp313-win_amd64.pyd',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python313\\site-packages\\pyproj\\database.cp313-win_amd64.pyd',
   'EXTENSION'),
  ('pyproj\\_network.cp313-win_amd64.pyd',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python313\\site-packages\\pyproj\\_network.cp313-win_amd64.pyd',
   'EXTENSION'),
  ('shapely\\lib.cp313-win_amd64.pyd',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python313\\site-packages\\shapely\\lib.cp313-win_amd64.pyd',
   'EXTENSION'),
  ('shapely\\_geometry_helpers.cp313-win_amd64.pyd',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python313\\site-packages\\shapely\\_geometry_helpers.cp313-win_amd64.pyd',
   'EXTENSION'),
  ('shapely\\_geos.cp313-win_amd64.pyd',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python313\\site-packages\\shapely\\_geos.cp313-win_amd64.pyd',
   'EXTENSION'),
  ('fiona\\_transform.cp313-win_amd64.pyd',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python313\\site-packages\\fiona\\_transform.cp313-win_amd64.pyd',
   'EXTENSION'),
  ('fiona\\crs.cp313-win_amd64.pyd',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python313\\site-packages\\fiona\\crs.cp313-win_amd64.pyd',
   'EXTENSION'),
  ('_uuid.pyd', 'C:\\Python313\\DLLs\\_uuid.pyd', 'EXTENSION'),
  ('fiona\\_err.cp313-win_amd64.pyd',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python313\\site-packages\\fiona\\_err.cp313-win_amd64.pyd',
   'EXTENSION'),
  ('fiona\\_geometry.cp313-win_amd64.pyd',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python313\\site-packages\\fiona\\_geometry.cp313-win_amd64.pyd',
   'EXTENSION'),
  ('fiona\\schema.cp313-win_amd64.pyd',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python313\\site-packages\\fiona\\schema.cp313-win_amd64.pyd',
   'EXTENSION'),
  ('fiona\\ogrext.cp313-win_amd64.pyd',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python313\\site-packages\\fiona\\ogrext.cp313-win_amd64.pyd',
   'EXTENSION'),
  ('_elementtree.pyd', 'C:\\Python313\\DLLs\\_elementtree.pyd', 'EXTENSION'),
  ('fiona\\_vsiopener.cp313-win_amd64.pyd',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python313\\site-packages\\fiona\\_vsiopener.cp313-win_amd64.pyd',
   'EXTENSION'),
  ('fiona\\_env.cp313-win_amd64.pyd',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python313\\site-packages\\fiona\\_env.cp313-win_amd64.pyd',
   'EXTENSION'),
  ('rasterio\\_warp.cp313-win_amd64.pyd',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python313\\site-packages\\rasterio\\_warp.cp313-win_amd64.pyd',
   'EXTENSION'),
  ('rasterio\\_base.cp313-win_amd64.pyd',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python313\\site-packages\\rasterio\\_base.cp313-win_amd64.pyd',
   'EXTENSION'),
  ('rasterio\\_transform.cp313-win_amd64.pyd',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python313\\site-packages\\rasterio\\_transform.cp313-win_amd64.pyd',
   'EXTENSION'),
  ('rasterio\\_filepath.cp313-win_amd64.pyd',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python313\\site-packages\\rasterio\\_filepath.cp313-win_amd64.pyd',
   'EXTENSION'),
  ('rasterio\\_io.cp313-win_amd64.pyd',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python313\\site-packages\\rasterio\\_io.cp313-win_amd64.pyd',
   'EXTENSION'),
  ('rasterio\\_fill.cp313-win_amd64.pyd',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python313\\site-packages\\rasterio\\_fill.cp313-win_amd64.pyd',
   'EXTENSION'),
  ('rasterio\\_features.cp313-win_amd64.pyd',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python313\\site-packages\\rasterio\\_features.cp313-win_amd64.pyd',
   'EXTENSION'),
  ('rasterio\\_version.cp313-win_amd64.pyd',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python313\\site-packages\\rasterio\\_version.cp313-win_amd64.pyd',
   'EXTENSION'),
  ('rasterio\\_env.cp313-win_amd64.pyd',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python313\\site-packages\\rasterio\\_env.cp313-win_amd64.pyd',
   'EXTENSION'),
  ('rasterio\\crs.cp313-win_amd64.pyd',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python313\\site-packages\\rasterio\\crs.cp313-win_amd64.pyd',
   'EXTENSION'),
  ('rasterio\\_err.cp313-win_amd64.pyd',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python313\\site-packages\\rasterio\\_err.cp313-win_amd64.pyd',
   'EXTENSION'),
  ('rasterio\\_vsiopener.cp313-win_amd64.pyd',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python313\\site-packages\\rasterio\\_vsiopener.cp313-win_amd64.pyd',
   'EXTENSION'),
  ('VCRUNTIME140.dll', 'C:\\Python313\\VCRUNTIME140.dll', 'BINARY'),
  ('api-ms-win-crt-string-l1-1-0.dll',
   'C:\\Program Files (x86)\\Windows Kits\\10\\Windows Performance '
   'Toolkit\\api-ms-win-crt-string-l1-1-0.dll',
   'BINARY'),
  ('api-ms-win-crt-heap-l1-1-0.dll',
   'C:\\Program Files (x86)\\Windows Kits\\10\\Windows Performance '
   'Toolkit\\api-ms-win-crt-heap-l1-1-0.dll',
   'BINARY'),
  ('VCRUNTIME140_1.dll', 'C:\\Python313\\VCRUNTIME140_1.dll', 'BINARY'),
  ('api-ms-win-crt-locale-l1-1-0.dll',
   'C:\\Program Files (x86)\\Windows Kits\\10\\Windows Performance '
   'Toolkit\\api-ms-win-crt-locale-l1-1-0.dll',
   'BINARY'),
  ('api-ms-win-crt-filesystem-l1-1-0.dll',
   'C:\\Program Files (x86)\\Windows Kits\\10\\Windows Performance '
   'Toolkit\\api-ms-win-crt-filesystem-l1-1-0.dll',
   'BINARY'),
  ('api-ms-win-crt-math-l1-1-0.dll',
   'C:\\Program Files (x86)\\Windows Kits\\10\\Windows Performance '
   'Toolkit\\api-ms-win-crt-math-l1-1-0.dll',
   'BINARY'),
  ('api-ms-win-crt-stdio-l1-1-0.dll',
   'C:\\Program Files (x86)\\Windows Kits\\10\\Windows Performance '
   'Toolkit\\api-ms-win-crt-stdio-l1-1-0.dll',
   'BINARY'),
  ('api-ms-win-crt-convert-l1-1-0.dll',
   'C:\\Program Files (x86)\\Windows Kits\\10\\Windows Performance '
   'Toolkit\\api-ms-win-crt-convert-l1-1-0.dll',
   'BINARY'),
  ('api-ms-win-crt-runtime-l1-1-0.dll',
   'C:\\Program Files (x86)\\Windows Kits\\10\\Windows Performance '
   'Toolkit\\api-ms-win-crt-runtime-l1-1-0.dll',
   'BINARY'),
  ('api-ms-win-crt-utility-l1-1-0.dll',
   'C:\\Program Files (x86)\\Windows Kits\\10\\Windows Performance '
   'Toolkit\\api-ms-win-crt-utility-l1-1-0.dll',
   'BINARY'),
  ('api-ms-win-crt-environment-l1-1-0.dll',
   'C:\\Program Files (x86)\\Windows Kits\\10\\Windows Performance '
   'Toolkit\\api-ms-win-crt-environment-l1-1-0.dll',
   'BINARY'),
  ('api-ms-win-crt-time-l1-1-0.dll',
   'C:\\Program Files (x86)\\Windows Kits\\10\\Windows Performance '
   'Toolkit\\api-ms-win-crt-time-l1-1-0.dll',
   'BINARY'),
  ('api-ms-win-crt-conio-l1-1-0.dll',
   'C:\\Program Files (x86)\\Windows Kits\\10\\Windows Performance '
   'Toolkit\\api-ms-win-crt-conio-l1-1-0.dll',
   'BINARY'),
  ('api-ms-win-crt-process-l1-1-0.dll',
   'C:\\Program Files (x86)\\Windows Kits\\10\\Windows Performance '
   'Toolkit\\api-ms-win-crt-process-l1-1-0.dll',
   'BINARY'),
  ('api-ms-win-crt-private-l1-1-0.dll',
   'C:\\Program Files (x86)\\Windows Kits\\10\\Windows Performance '
   'Toolkit\\api-ms-win-crt-private-l1-1-0.dll',
   'BINARY'),
  ('libcrypto-3.dll', 'C:\\Python313\\DLLs\\libcrypto-3.dll', 'BINARY'),
  ('libssl-3.dll', 'C:\\Python313\\DLLs\\libssl-3.dll', 'BINARY'),
  ('libffi-8.dll', 'C:\\Python313\\DLLs\\libffi-8.dll', 'BINARY'),
  ('tcl86t.dll', 'C:\\Python313\\DLLs\\tcl86t.dll', 'BINARY'),
  ('tk86t.dll', 'C:\\Python313\\DLLs\\tk86t.dll', 'BINARY'),
  ('python3.dll', 'C:\\Python313\\python3.dll', 'BINARY'),
  ('pywin32_system32\\pywintypes313.dll',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python313\\site-packages\\pywin32_system32\\pywintypes313.dll',
   'BINARY'),
  ('fiona.libs\\gdal-3105cd430966b2574784cc5837b520a5.dll',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python313\\site-packages\\fiona.libs\\gdal-3105cd430966b2574784cc5837b520a5.dll',
   'BINARY'),
  ('rasterio.libs\\gdal-0c91f8aa17bf5518bc6a69e15438cc5c.dll',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python313\\site-packages\\rasterio.libs\\gdal-0c91f8aa17bf5518bc6a69e15438cc5c.dll',
   'BINARY'),
  ('ucrtbase.dll',
   'C:\\Program Files (x86)\\Windows Kits\\10\\Windows Performance '
   'Toolkit\\ucrtbase.dll',
   'BINARY'),
  ('zlib1.dll', 'C:\\Python313\\DLLs\\zlib1.dll', 'BINARY'),
  ('fiona.libs\\sqlite3-0cb9f6e67fd2491114f55ace5268f7aa.dll',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python313\\site-packages\\fiona.libs\\sqlite3-0cb9f6e67fd2491114f55ace5268f7aa.dll',
   'BINARY'),
  ('fiona.libs\\tiff-b54f5da03716c60a719064c5995ad1e3.dll',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python313\\site-packages\\fiona.libs\\tiff-b54f5da03716c60a719064c5995ad1e3.dll',
   'BINARY'),
  ('fiona.libs\\geos_c-4d7a52e330cb609ee4db6bd3c1f7e7b5.dll',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python313\\site-packages\\fiona.libs\\geos_c-4d7a52e330cb609ee4db6bd3c1f7e7b5.dll',
   'BINARY'),
  ('fiona.libs\\libpng16-90b1d680f42f640415853ff0cd0b3490.dll',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python313\\site-packages\\fiona.libs\\libpng16-90b1d680f42f640415853ff0cd0b3490.dll',
   'BINARY'),
  ('fiona.libs\\geotiff-8b5e2dc8882862915ed2d750ed5319da.dll',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python313\\site-packages\\fiona.libs\\geotiff-8b5e2dc8882862915ed2d750ed5319da.dll',
   'BINARY'),
  ('fiona.libs\\pcre2-8-d813e6f0e805eab6d374671dc234e1ae.dll',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python313\\site-packages\\fiona.libs\\pcre2-8-d813e6f0e805eab6d374671dc234e1ae.dll',
   'BINARY'),
  ('fiona.libs\\libexpat-dd2f1ae151570af75ab8a932d0849f1d.dll',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python313\\site-packages\\fiona.libs\\libexpat-dd2f1ae151570af75ab8a932d0849f1d.dll',
   'BINARY'),
  ('fiona.libs\\netcdf-c388c80124356fa9f445a498e2550455.dll',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python313\\site-packages\\fiona.libs\\netcdf-c388c80124356fa9f445a498e2550455.dll',
   'BINARY'),
  ('fiona.libs\\proj-3f82123c6982a15ab288894283554e44.dll',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python313\\site-packages\\fiona.libs\\proj-3f82123c6982a15ab288894283554e44.dll',
   'BINARY'),
  ('fiona.libs\\json-c-a79da3ad89939d4d14a3819a7df4dfce.dll',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python313\\site-packages\\fiona.libs\\json-c-a79da3ad89939d4d14a3819a7df4dfce.dll',
   'BINARY'),
  ('fiona.libs\\qhull_r-ca74792ac17586b5053948c65dfa8fb1.dll',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python313\\site-packages\\fiona.libs\\qhull_r-ca74792ac17586b5053948c65dfa8fb1.dll',
   'BINARY'),
  ('fiona.libs\\liblzma-20b935dffc1aa59c3a410e536b235e0a.dll',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python313\\site-packages\\fiona.libs\\liblzma-20b935dffc1aa59c3a410e536b235e0a.dll',
   'BINARY'),
  ('fiona.libs\\minizip-fa015f03fd057686544d654bf2c4ed9f.dll',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python313\\site-packages\\fiona.libs\\minizip-fa015f03fd057686544d654bf2c4ed9f.dll',
   'BINARY'),
  ('fiona.libs\\libpq-6fb79053aa253f672cf825d175de737c.dll',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python313\\site-packages\\fiona.libs\\libpq-6fb79053aa253f672cf825d175de737c.dll',
   'BINARY'),
  ('fiona.libs\\libxml2-1dc62481c9c5a2c7dfb648c92ddebe81.dll',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python313\\site-packages\\fiona.libs\\libxml2-1dc62481c9c5a2c7dfb648c92ddebe81.dll',
   'BINARY'),
  ('fiona.libs\\gif-72f5a534bc37066175f18a3a5d84eeae.dll',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python313\\site-packages\\fiona.libs\\gif-72f5a534bc37066175f18a3a5d84eeae.dll',
   'BINARY'),
  ('fiona.libs\\openjp2-fdb0b878068e74b970c325ed55edc3c5.dll',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python313\\site-packages\\fiona.libs\\openjp2-fdb0b878068e74b970c325ed55edc3c5.dll',
   'BINARY'),
  ('fiona.libs\\hdf5-fa46ddbc0e6f5f04b4dfa68ebf54461b.dll',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python313\\site-packages\\fiona.libs\\hdf5-fa46ddbc0e6f5f04b4dfa68ebf54461b.dll',
   'BINARY'),
  ('fiona.libs\\spatialite-e03c16976c81110a807ddde663a483fb.dll',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python313\\site-packages\\fiona.libs\\spatialite-e03c16976c81110a807ddde663a483fb.dll',
   'BINARY'),
  ('fiona.libs\\libssl-3-x64-3ad97c6734bbfbaaeee5c2d6e92f2f4b.dll',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python313\\site-packages\\fiona.libs\\libssl-3-x64-3ad97c6734bbfbaaeee5c2d6e92f2f4b.dll',
   'BINARY'),
  ('fiona.libs\\iconv-2-2651e8d0d0179faf57777d2c71481e65.dll',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python313\\site-packages\\fiona.libs\\iconv-2-2651e8d0d0179faf57777d2c71481e65.dll',
   'BINARY'),
  ('fiona.libs\\libcurl-077ab0f5dc0e8b5f1ddd883462d826de.dll',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python313\\site-packages\\fiona.libs\\libcurl-077ab0f5dc0e8b5f1ddd883462d826de.dll',
   'BINARY'),
  ('fiona.libs\\Lerc-d43d8ee2be34b5c69bcf4d853338bdc2.dll',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python313\\site-packages\\fiona.libs\\Lerc-d43d8ee2be34b5c69bcf4d853338bdc2.dll',
   'BINARY'),
  ('fiona.libs\\libwebp-37f6ef74c6b7f8a08b41c1dc3407cc63.dll',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python313\\site-packages\\fiona.libs\\libwebp-37f6ef74c6b7f8a08b41c1dc3407cc63.dll',
   'BINARY'),
  ('fiona.libs\\libcrypto-3-x64-a9282680054934d0c6826e7539f4f82b.dll',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python313\\site-packages\\fiona.libs\\libcrypto-3-x64-a9282680054934d0c6826e7539f4f82b.dll',
   'BINARY'),
  ('fiona.libs\\zstd-********************************.dll',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python313\\site-packages\\fiona.libs\\zstd-********************************.dll',
   'BINARY'),
  ('fiona.libs\\msvcp140-80cac3ebc22ff38cfcdee60334932058.dll',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python313\\site-packages\\fiona.libs\\msvcp140-80cac3ebc22ff38cfcdee60334932058.dll',
   'BINARY'),
  ('fiona.libs\\zlib1-6bb9d46bd47056cba4611655f416dee7.dll',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python313\\site-packages\\fiona.libs\\zlib1-6bb9d46bd47056cba4611655f416dee7.dll',
   'BINARY'),
  ('fiona.libs\\jpeg62-b1a7291a72814164156a5130fdc64eef.dll',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python313\\site-packages\\fiona.libs\\jpeg62-b1a7291a72814164156a5130fdc64eef.dll',
   'BINARY'),
  ('rasterio.libs\\geos_c-********************************.dll',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python313\\site-packages\\rasterio.libs\\geos_c-********************************.dll',
   'BINARY'),
  ('rasterio.libs\\sqlite3-8aeeb35f58b0004b9afadb4b97984877.dll',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python313\\site-packages\\rasterio.libs\\sqlite3-8aeeb35f58b0004b9afadb4b97984877.dll',
   'BINARY'),
  ('rasterio.libs\\spatialite-928196e5c1a0f4a6cb7fcba59ca34118.dll',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python313\\site-packages\\rasterio.libs\\spatialite-928196e5c1a0f4a6cb7fcba59ca34118.dll',
   'BINARY'),
  ('rasterio.libs\\qhull_r-475a92433d384c8b2e36eefd5b24f1f7.dll',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python313\\site-packages\\rasterio.libs\\qhull_r-475a92433d384c8b2e36eefd5b24f1f7.dll',
   'BINARY'),
  ('rasterio.libs\\iconv-2-718e10f74b34deb5d5a517ae81f56bb3.dll',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python313\\site-packages\\rasterio.libs\\iconv-2-718e10f74b34deb5d5a517ae81f56bb3.dll',
   'BINARY'),
  ('rasterio.libs\\netcdf-4dcb25369f09ddb6aefeee3a493ac659.dll',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python313\\site-packages\\rasterio.libs\\netcdf-4dcb25369f09ddb6aefeee3a493ac659.dll',
   'BINARY'),
  ('rasterio.libs\\libpq-b8ca67b316eedd8fb662787b9253351c.dll',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python313\\site-packages\\rasterio.libs\\libpq-b8ca67b316eedd8fb662787b9253351c.dll',
   'BINARY'),
  ('rasterio.libs\\libxml2-562d9e91bad4f07edac6bba0ef3687f1.dll',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python313\\site-packages\\rasterio.libs\\libxml2-562d9e91bad4f07edac6bba0ef3687f1.dll',
   'BINARY'),
  ('rasterio.libs\\minizip-373a37b173dcdb89415db1d87c7b132b.dll',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python313\\site-packages\\rasterio.libs\\minizip-373a37b173dcdb89415db1d87c7b132b.dll',
   'BINARY'),
  ('rasterio.libs\\libexpat-6fdd21e1d08cc2ad28ac127cbf891fb7.dll',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python313\\site-packages\\rasterio.libs\\libexpat-6fdd21e1d08cc2ad28ac127cbf891fb7.dll',
   'BINARY'),
  ('rasterio.libs\\zlib1-d643b916c0d5b64e42c135bb9f40adf6.dll',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python313\\site-packages\\rasterio.libs\\zlib1-d643b916c0d5b64e42c135bb9f40adf6.dll',
   'BINARY'),
  ('rasterio.libs\\libcurl-38ee3c0b265a01505d13483e9e02fdd4.dll',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python313\\site-packages\\rasterio.libs\\libcurl-38ee3c0b265a01505d13483e9e02fdd4.dll',
   'BINARY'),
  ('rasterio.libs\\hdf5-7d3a7692f9b0267de16dbc0c740dbd23.dll',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python313\\site-packages\\rasterio.libs\\hdf5-7d3a7692f9b0267de16dbc0c740dbd23.dll',
   'BINARY'),
  ('rasterio.libs\\liblzma-8fb058033d3cf38ae3060739b3a14302.dll',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python313\\site-packages\\rasterio.libs\\liblzma-8fb058033d3cf38ae3060739b3a14302.dll',
   'BINARY'),
  ('rasterio.libs\\openjp2-ebedeb79a4649b7e040ea211fc6ce2b7.dll',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python313\\site-packages\\rasterio.libs\\openjp2-ebedeb79a4649b7e040ea211fc6ce2b7.dll',
   'BINARY'),
  ('rasterio.libs\\libssl-3-x64-b799ebf482c1da262d14a3764d7ee551.dll',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python313\\site-packages\\rasterio.libs\\libssl-3-x64-b799ebf482c1da262d14a3764d7ee551.dll',
   'BINARY'),
  ('rasterio.libs\\libpng16-fdac1beb1acf6794fff1690e0a579a9d.dll',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python313\\site-packages\\rasterio.libs\\libpng16-fdac1beb1acf6794fff1690e0a579a9d.dll',
   'BINARY'),
  ('rasterio.libs\\json-c-f682e0885e5b3db071744ca2dd4e1500.dll',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python313\\site-packages\\rasterio.libs\\json-c-f682e0885e5b3db071744ca2dd4e1500.dll',
   'BINARY'),
  ('rasterio.libs\\jpeg62-7b22aad5185b3090ec96fafc56df898c.dll',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python313\\site-packages\\rasterio.libs\\jpeg62-7b22aad5185b3090ec96fafc56df898c.dll',
   'BINARY'),
  ('rasterio.libs\\Lerc-31ee1bcc53aa5c2e21402901329d5b90.dll',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python313\\site-packages\\rasterio.libs\\Lerc-31ee1bcc53aa5c2e21402901329d5b90.dll',
   'BINARY'),
  ('rasterio.libs\\libwebp-22e4b075635059d80eb6faee67251802.dll',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python313\\site-packages\\rasterio.libs\\libwebp-22e4b075635059d80eb6faee67251802.dll',
   'BINARY'),
  ('rasterio.libs\\libcrypto-3-x64-2c2d24f46e8912647ae707b62db93852.dll',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python313\\site-packages\\rasterio.libs\\libcrypto-3-x64-2c2d24f46e8912647ae707b62db93852.dll',
   'BINARY'),
  ('rasterio.libs\\gif-cd7dfb88992600f7246b8a1d93973a5f.dll',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python313\\site-packages\\rasterio.libs\\gif-cd7dfb88992600f7246b8a1d93973a5f.dll',
   'BINARY'),
  ('rasterio.libs\\zstd-5e6345ecaba60462a7b81731fea22d58.dll',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python313\\site-packages\\rasterio.libs\\zstd-5e6345ecaba60462a7b81731fea22d58.dll',
   'BINARY'),
  ('rasterio.libs\\msvcp140-0e70a616186a640ed2eed9fda9f9a89f.dll',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python313\\site-packages\\rasterio.libs\\msvcp140-0e70a616186a640ed2eed9fda9f9a89f.dll',
   'BINARY'),
  ('rasterio.libs\\geotiff-ac8f46fb743754d67b935ac3d1ca3018.dll',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python313\\site-packages\\rasterio.libs\\geotiff-ac8f46fb743754d67b935ac3d1ca3018.dll',
   'BINARY'),
  ('rasterio.libs\\proj_9-139fcbe1c06b2292f008acabcc19b1c1.dll',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python313\\site-packages\\rasterio.libs\\proj_9-139fcbe1c06b2292f008acabcc19b1c1.dll',
   'BINARY'),
  ('rasterio.libs\\pcre2-8-0ac0971c4cbb5db129c3e021bdf5d9c2.dll',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python313\\site-packages\\rasterio.libs\\pcre2-8-0ac0971c4cbb5db129c3e021bdf5d9c2.dll',
   'BINARY'),
  ('rasterio.libs\\tiff-5d0bd3e78e0e701d23907f9882f83833.dll',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python313\\site-packages\\rasterio.libs\\tiff-5d0bd3e78e0e701d23907f9882f83833.dll',
   'BINARY'),
  ('api-ms-win-core-string-l1-1-0.dll',
   'C:\\Program Files (x86)\\Windows Kits\\10\\Windows Performance '
   'Toolkit\\api-ms-win-core-string-l1-1-0.dll',
   'BINARY'),
  ('api-ms-win-core-util-l1-1-0.dll',
   'C:\\Program Files (x86)\\Windows Kits\\10\\Windows Performance '
   'Toolkit\\api-ms-win-core-util-l1-1-0.dll',
   'BINARY'),
  ('api-ms-win-core-file-l1-1-0.dll',
   'C:\\Program Files (x86)\\Windows Kits\\10\\Windows Performance '
   'Toolkit\\api-ms-win-core-file-l1-1-0.dll',
   'BINARY'),
  ('api-ms-win-core-memory-l1-1-0.dll',
   'C:\\Program Files (x86)\\Windows Kits\\10\\Windows Performance '
   'Toolkit\\api-ms-win-core-memory-l1-1-0.dll',
   'BINARY'),
  ('api-ms-win-core-synch-l1-2-0.dll',
   'C:\\Program Files (x86)\\Windows Kits\\10\\Windows Performance '
   'Toolkit\\api-ms-win-core-synch-l1-2-0.dll',
   'BINARY'),
  ('api-ms-win-core-file-l2-1-0.dll',
   'C:\\Program Files (x86)\\Windows Kits\\10\\Windows Performance '
   'Toolkit\\api-ms-win-core-file-l2-1-0.dll',
   'BINARY'),
  ('api-ms-win-core-processthreads-l1-1-1.dll',
   'C:\\Program Files (x86)\\Windows Kits\\10\\Windows Performance '
   'Toolkit\\api-ms-win-core-processthreads-l1-1-1.dll',
   'BINARY'),
  ('api-ms-win-core-rtlsupport-l1-1-0.dll',
   'C:\\Program Files (x86)\\Windows Kits\\10\\Windows Performance '
   'Toolkit\\api-ms-win-core-rtlsupport-l1-1-0.dll',
   'BINARY'),
  ('api-ms-win-core-namedpipe-l1-1-0.dll',
   'C:\\Program Files (x86)\\Windows Kits\\10\\Windows Performance '
   'Toolkit\\api-ms-win-core-namedpipe-l1-1-0.dll',
   'BINARY'),
  ('api-ms-win-core-localization-l1-2-0.dll',
   'C:\\Program Files (x86)\\Windows Kits\\10\\Windows Performance '
   'Toolkit\\api-ms-win-core-localization-l1-2-0.dll',
   'BINARY'),
  ('api-ms-win-core-libraryloader-l1-1-0.dll',
   'C:\\Program Files (x86)\\Windows Kits\\10\\Windows Performance '
   'Toolkit\\api-ms-win-core-libraryloader-l1-1-0.dll',
   'BINARY'),
  ('api-ms-win-core-errorhandling-l1-1-0.dll',
   'C:\\Program Files (x86)\\Windows Kits\\10\\Windows Performance '
   'Toolkit\\api-ms-win-core-errorhandling-l1-1-0.dll',
   'BINARY'),
  ('api-ms-win-core-processenvironment-l1-1-0.dll',
   'C:\\Program Files (x86)\\Windows Kits\\10\\Windows Performance '
   'Toolkit\\api-ms-win-core-processenvironment-l1-1-0.dll',
   'BINARY'),
  ('api-ms-win-core-interlocked-l1-1-0.dll',
   'C:\\Program Files (x86)\\Windows Kits\\10\\Windows Performance '
   'Toolkit\\api-ms-win-core-interlocked-l1-1-0.dll',
   'BINARY'),
  ('api-ms-win-core-handle-l1-1-0.dll',
   'C:\\Program Files (x86)\\Windows Kits\\10\\Windows Performance '
   'Toolkit\\api-ms-win-core-handle-l1-1-0.dll',
   'BINARY'),
  ('api-ms-win-core-console-l1-1-0.dll',
   'C:\\Program Files (x86)\\Windows Kits\\10\\Windows Performance '
   'Toolkit\\api-ms-win-core-console-l1-1-0.dll',
   'BINARY'),
  ('api-ms-win-core-datetime-l1-1-0.dll',
   'C:\\Program Files (x86)\\Windows Kits\\10\\Windows Performance '
   'Toolkit\\api-ms-win-core-datetime-l1-1-0.dll',
   'BINARY'),
  ('api-ms-win-core-heap-l1-1-0.dll',
   'C:\\Program Files (x86)\\Windows Kits\\10\\Windows Performance '
   'Toolkit\\api-ms-win-core-heap-l1-1-0.dll',
   'BINARY'),
  ('api-ms-win-core-profile-l1-1-0.dll',
   'C:\\Program Files (x86)\\Windows Kits\\10\\Windows Performance '
   'Toolkit\\api-ms-win-core-profile-l1-1-0.dll',
   'BINARY'),
  ('api-ms-win-core-synch-l1-1-0.dll',
   'C:\\Program Files (x86)\\Windows Kits\\10\\Windows Performance '
   'Toolkit\\api-ms-win-core-synch-l1-1-0.dll',
   'BINARY'),
  ('api-ms-win-core-sysinfo-l1-1-0.dll',
   'C:\\Program Files (x86)\\Windows Kits\\10\\Windows Performance '
   'Toolkit\\api-ms-win-core-sysinfo-l1-1-0.dll',
   'BINARY'),
  ('api-ms-win-core-processthreads-l1-1-0.dll',
   'C:\\Program Files (x86)\\Windows Kits\\10\\Windows Performance '
   'Toolkit\\api-ms-win-core-processthreads-l1-1-0.dll',
   'BINARY'),
  ('api-ms-win-core-timezone-l1-1-0.dll',
   'C:\\Program Files (x86)\\Windows Kits\\10\\Windows Performance '
   'Toolkit\\api-ms-win-core-timezone-l1-1-0.dll',
   'BINARY'),
  ('api-ms-win-core-debug-l1-1-0.dll',
   'C:\\Program Files (x86)\\Windows Kits\\10\\Windows Performance '
   'Toolkit\\api-ms-win-core-debug-l1-1-0.dll',
   'BINARY'),
  ('api-ms-win-core-file-l1-2-0.dll',
   'C:\\Program Files (x86)\\Windows Kits\\10\\Windows Performance '
   'Toolkit\\api-ms-win-core-file-l1-2-0.dll',
   'BINARY'),
  ('fiona.libs\\geos-cf4fccef073a506b3207c8a036a9d063.dll',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python313\\site-packages\\fiona.libs\\geos-cf4fccef073a506b3207c8a036a9d063.dll',
   'BINARY'),
  ('fiona.libs\\hdf5_hl-222f136c52a099fe4b41216a1ded1bcc.dll',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python313\\site-packages\\fiona.libs\\hdf5_hl-222f136c52a099fe4b41216a1ded1bcc.dll',
   'BINARY'),
  ('fiona.libs\\szip-d0425ee11b05cea2ef5907b56ab33522.dll',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python313\\site-packages\\fiona.libs\\szip-d0425ee11b05cea2ef5907b56ab33522.dll',
   'BINARY'),
  ('fiona.libs\\freexl-1-3d1f53ba38e08cfcb484f86c84d51fbb.dll',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python313\\site-packages\\fiona.libs\\freexl-1-3d1f53ba38e08cfcb484f86c84d51fbb.dll',
   'BINARY'),
  ('fiona.libs\\libsharpyuv-cd7565f442be556cb3a5480d7bc360f8.dll',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python313\\site-packages\\fiona.libs\\libsharpyuv-cd7565f442be556cb3a5480d7bc360f8.dll',
   'BINARY'),
  ('rasterio.libs\\geos-1045aa18488ed886193a206783279614.dll',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python313\\site-packages\\rasterio.libs\\geos-1045aa18488ed886193a206783279614.dll',
   'BINARY'),
  ('rasterio.libs\\freexl-1-af8378f9f54baeee60ba29a2eb4be3a9.dll',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python313\\site-packages\\rasterio.libs\\freexl-1-af8378f9f54baeee60ba29a2eb4be3a9.dll',
   'BINARY'),
  ('rasterio.libs\\hdf5_hl-b157875d2402d925e457744c04ba1b41.dll',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python313\\site-packages\\rasterio.libs\\hdf5_hl-b157875d2402d925e457744c04ba1b41.dll',
   'BINARY'),
  ('rasterio.libs\\szip-c3c2fd778fcb3f99c51fef2ebf43efab.dll',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python313\\site-packages\\rasterio.libs\\szip-c3c2fd778fcb3f99c51fef2ebf43efab.dll',
   'BINARY'),
  ('rasterio.libs\\libsharpyuv-2e5182496a1bb260ca73afe264fe5a79.dll',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python313\\site-packages\\rasterio.libs\\libsharpyuv-2e5182496a1bb260ca73afe264fe5a79.dll',
   'BINARY')],
 [],
 [],
 [('_tcl_data\\msgs\\es_ve.msg',
   'C:\\Python313\\tcl\\tcl8.6\\msgs\\es_ve.msg',
   'DATA'),
  ('_tcl_data\\tzdata\\Asia\\Yekaterinburg',
   'C:\\Python313\\tcl\\tcl8.6\\tzdata\\Asia\\Yekaterinburg',
   'DATA'),
  ('_tcl_data\\tzdata\\Asia\\Yangon',
   'C:\\Python313\\tcl\\tcl8.6\\tzdata\\Asia\\Yangon',
   'DATA'),
  ('_tcl_data\\tzdata\\Asia\\Beirut',
   'C:\\Python313\\tcl\\tcl8.6\\tzdata\\Asia\\Beirut',
   'DATA'),
  ('_tcl_data\\tzdata\\America\\Pangnirtung',
   'C:\\Python313\\tcl\\tcl8.6\\tzdata\\America\\Pangnirtung',
   'DATA'),
  ('_tcl_data\\encoding\\cp874.enc',
   'C:\\Python313\\tcl\\tcl8.6\\encoding\\cp874.enc',
   'DATA'),
  ('_tcl_data\\tzdata\\America\\Argentina\\Mendoza',
   'C:\\Python313\\tcl\\tcl8.6\\tzdata\\America\\Argentina\\Mendoza',
   'DATA'),
  ('_tcl_data\\tzdata\\Africa\\Djibouti',
   'C:\\Python313\\tcl\\tcl8.6\\tzdata\\Africa\\Djibouti',
   'DATA'),
  ('_tcl_data\\tzdata\\America\\Nassau',
   'C:\\Python313\\tcl\\tcl8.6\\tzdata\\America\\Nassau',
   'DATA'),
  ('_tcl_data\\tzdata\\Europe\\Budapest',
   'C:\\Python313\\tcl\\tcl8.6\\tzdata\\Europe\\Budapest',
   'DATA'),
  ('_tcl_data\\tzdata\\America\\Thule',
   'C:\\Python313\\tcl\\tcl8.6\\tzdata\\America\\Thule',
   'DATA'),
  ('_tcl_data\\msgs\\da.msg',
   'C:\\Python313\\tcl\\tcl8.6\\msgs\\da.msg',
   'DATA'),
  ('_tcl_data\\tzdata\\America\\St_Thomas',
   'C:\\Python313\\tcl\\tcl8.6\\tzdata\\America\\St_Thomas',
   'DATA'),
  ('_tcl_data\\tzdata\\America\\Dawson_Creek',
   'C:\\Python313\\tcl\\tcl8.6\\tzdata\\America\\Dawson_Creek',
   'DATA'),
  ('_tcl_data\\encoding\\cp737.enc',
   'C:\\Python313\\tcl\\tcl8.6\\encoding\\cp737.enc',
   'DATA'),
  ('_tcl_data\\encoding\\symbol.enc',
   'C:\\Python313\\tcl\\tcl8.6\\encoding\\symbol.enc',
   'DATA'),
  ('_tcl_data\\tzdata\\Etc\\GMT0',
   'C:\\Python313\\tcl\\tcl8.6\\tzdata\\Etc\\GMT0',
   'DATA'),
  ('_tcl_data\\tzdata\\America\\Winnipeg',
   'C:\\Python313\\tcl\\tcl8.6\\tzdata\\America\\Winnipeg',
   'DATA'),
  ('_tcl_data\\msgs\\ar_sy.msg',
   'C:\\Python313\\tcl\\tcl8.6\\msgs\\ar_sy.msg',
   'DATA'),
  ('_tcl_data\\tzdata\\Pacific\\Rarotonga',
   'C:\\Python313\\tcl\\tcl8.6\\tzdata\\Pacific\\Rarotonga',
   'DATA'),
  ('_tcl_data\\tzdata\\America\\Montserrat',
   'C:\\Python313\\tcl\\tcl8.6\\tzdata\\America\\Montserrat',
   'DATA'),
  ('_tcl_data\\tzdata\\America\\Dawson',
   'C:\\Python313\\tcl\\tcl8.6\\tzdata\\America\\Dawson',
   'DATA'),
  ('_tcl_data\\tzdata\\Europe\\Vatican',
   'C:\\Python313\\tcl\\tcl8.6\\tzdata\\Europe\\Vatican',
   'DATA'),
  ('_tcl_data\\msgs\\es_ar.msg',
   'C:\\Python313\\tcl\\tcl8.6\\msgs\\es_ar.msg',
   'DATA'),
  ('_tcl_data\\tzdata\\America\\Detroit',
   'C:\\Python313\\tcl\\tcl8.6\\tzdata\\America\\Detroit',
   'DATA'),
  ('_tcl_data\\tzdata\\Pacific\\Funafuti',
   'C:\\Python313\\tcl\\tcl8.6\\tzdata\\Pacific\\Funafuti',
   'DATA'),
  ('_tcl_data\\msgs\\sh.msg',
   'C:\\Python313\\tcl\\tcl8.6\\msgs\\sh.msg',
   'DATA'),
  ('_tcl_data\\tzdata\\America\\Swift_Current',
   'C:\\Python313\\tcl\\tcl8.6\\tzdata\\America\\Swift_Current',
   'DATA'),
  ('_tcl_data\\tzdata\\Africa\\Juba',
   'C:\\Python313\\tcl\\tcl8.6\\tzdata\\Africa\\Juba',
   'DATA'),
  ('_tcl_data\\tzdata\\America\\Indiana\\Indianapolis',
   'C:\\Python313\\tcl\\tcl8.6\\tzdata\\America\\Indiana\\Indianapolis',
   'DATA'),
  ('_tcl_data\\msgs\\he.msg',
   'C:\\Python313\\tcl\\tcl8.6\\msgs\\he.msg',
   'DATA'),
  ('_tcl_data\\msgs\\ru_ua.msg',
   'C:\\Python313\\tcl\\tcl8.6\\msgs\\ru_ua.msg',
   'DATA'),
  ('_tcl_data\\msgs\\zh_hk.msg',
   'C:\\Python313\\tcl\\tcl8.6\\msgs\\zh_hk.msg',
   'DATA'),
  ('_tcl_data\\tzdata\\America\\Porto_Acre',
   'C:\\Python313\\tcl\\tcl8.6\\tzdata\\America\\Porto_Acre',
   'DATA'),
  ('_tcl_data\\tzdata\\Europe\\Berlin',
   'C:\\Python313\\tcl\\tcl8.6\\tzdata\\Europe\\Berlin',
   'DATA'),
  ('_tcl_data\\tzdata\\Africa\\Maputo',
   'C:\\Python313\\tcl\\tcl8.6\\tzdata\\Africa\\Maputo',
   'DATA'),
  ('_tcl_data\\tzdata\\Asia\\Novosibirsk',
   'C:\\Python313\\tcl\\tcl8.6\\tzdata\\Asia\\Novosibirsk',
   'DATA'),
  ('_tcl_data\\tzdata\\Etc\\GMT+9',
   'C:\\Python313\\tcl\\tcl8.6\\tzdata\\Etc\\GMT+9',
   'DATA'),
  ('_tcl_data\\tzdata\\Asia\\Kabul',
   'C:\\Python313\\tcl\\tcl8.6\\tzdata\\Asia\\Kabul',
   'DATA'),
  ('_tcl_data\\tzdata\\Pacific\\Fakaofo',
   'C:\\Python313\\tcl\\tcl8.6\\tzdata\\Pacific\\Fakaofo',
   'DATA'),
  ('_tcl_data\\tzdata\\Africa\\Cairo',
   'C:\\Python313\\tcl\\tcl8.6\\tzdata\\Africa\\Cairo',
   'DATA'),
  ('_tk_data\\license.terms',
   'C:\\Python313\\tcl\\tk8.6\\license.terms',
   'DATA'),
  ('_tcl_data\\msgs\\ms.msg',
   'C:\\Python313\\tcl\\tcl8.6\\msgs\\ms.msg',
   'DATA'),
  ('_tcl_data\\msgs\\eo.msg',
   'C:\\Python313\\tcl\\tcl8.6\\msgs\\eo.msg',
   'DATA'),
  ('_tk_data\\ttk\\button.tcl',
   'C:\\Python313\\tcl\\tk8.6\\ttk\\button.tcl',
   'DATA'),
  ('_tk_data\\msgs\\hu.msg', 'C:\\Python313\\tcl\\tk8.6\\msgs\\hu.msg', 'DATA'),
  ('_tcl_data\\tzdata\\America\\Argentina\\La_Rioja',
   'C:\\Python313\\tcl\\tcl8.6\\tzdata\\America\\Argentina\\La_Rioja',
   'DATA'),
  ('_tcl_data\\tzdata\\Indian\\Chagos',
   'C:\\Python313\\tcl\\tcl8.6\\tzdata\\Indian\\Chagos',
   'DATA'),
  ('_tcl_data\\tzdata\\America\\Guatemala',
   'C:\\Python313\\tcl\\tcl8.6\\tzdata\\America\\Guatemala',
   'DATA'),
  ('_tk_data\\scrlbar.tcl', 'C:\\Python313\\tcl\\tk8.6\\scrlbar.tcl', 'DATA'),
  ('_tcl_data\\tzdata\\Europe\\Kiev',
   'C:\\Python313\\tcl\\tcl8.6\\tzdata\\Europe\\Kiev',
   'DATA'),
  ('_tcl_data\\tzdata\\Africa\\Mogadishu',
   'C:\\Python313\\tcl\\tcl8.6\\tzdata\\Africa\\Mogadishu',
   'DATA'),
  ('_tk_data\\iconlist.tcl', 'C:\\Python313\\tcl\\tk8.6\\iconlist.tcl', 'DATA'),
  ('_tcl_data\\encoding\\ascii.enc',
   'C:\\Python313\\tcl\\tcl8.6\\encoding\\ascii.enc',
   'DATA'),
  ('_tcl_data\\tzdata\\Etc\\GMT-10',
   'C:\\Python313\\tcl\\tcl8.6\\tzdata\\Etc\\GMT-10',
   'DATA'),
  ('_tcl_data\\tzdata\\America\\St_Lucia',
   'C:\\Python313\\tcl\\tcl8.6\\tzdata\\America\\St_Lucia',
   'DATA'),
  ('_tcl_data\\tzdata\\Canada\\Newfoundland',
   'C:\\Python313\\tcl\\tcl8.6\\tzdata\\Canada\\Newfoundland',
   'DATA'),
  ('_tcl_data\\tzdata\\America\\Havana',
   'C:\\Python313\\tcl\\tcl8.6\\tzdata\\America\\Havana',
   'DATA'),
  ('_tcl_data\\tzdata\\America\\Ensenada',
   'C:\\Python313\\tcl\\tcl8.6\\tzdata\\America\\Ensenada',
   'DATA'),
  ('_tcl_data\\tzdata\\Europe\\Moscow',
   'C:\\Python313\\tcl\\tcl8.6\\tzdata\\Europe\\Moscow',
   'DATA'),
  ('_tcl_data\\tzdata\\Pacific\\Tarawa',
   'C:\\Python313\\tcl\\tcl8.6\\tzdata\\Pacific\\Tarawa',
   'DATA'),
  ('_tcl_data\\tzdata\\Africa\\Asmera',
   'C:\\Python313\\tcl\\tcl8.6\\tzdata\\Africa\\Asmera',
   'DATA'),
  ('_tcl_data\\tzdata\\Africa\\Lubumbashi',
   'C:\\Python313\\tcl\\tcl8.6\\tzdata\\Africa\\Lubumbashi',
   'DATA'),
  ('_tcl_data\\tzdata\\Europe\\Lisbon',
   'C:\\Python313\\tcl\\tcl8.6\\tzdata\\Europe\\Lisbon',
   'DATA'),
  ('_tcl_data\\msgs\\hu.msg',
   'C:\\Python313\\tcl\\tcl8.6\\msgs\\hu.msg',
   'DATA'),
  ('_tcl_data\\encoding\\macCyrillic.enc',
   'C:\\Python313\\tcl\\tcl8.6\\encoding\\macCyrillic.enc',
   'DATA'),
  ('_tk_data\\msgs\\sv.msg', 'C:\\Python313\\tcl\\tk8.6\\msgs\\sv.msg', 'DATA'),
  ('_tcl_data\\tzdata\\America\\Indiana\\Petersburg',
   'C:\\Python313\\tcl\\tcl8.6\\tzdata\\America\\Indiana\\Petersburg',
   'DATA'),
  ('_tcl_data\\tzdata\\Asia\\Tel_Aviv',
   'C:\\Python313\\tcl\\tcl8.6\\tzdata\\Asia\\Tel_Aviv',
   'DATA'),
  ('_tcl_data\\tzdata\\Antarctica\\Rothera',
   'C:\\Python313\\tcl\\tcl8.6\\tzdata\\Antarctica\\Rothera',
   'DATA'),
  ('_tcl_data\\tzdata\\America\\Cambridge_Bay',
   'C:\\Python313\\tcl\\tcl8.6\\tzdata\\America\\Cambridge_Bay',
   'DATA'),
  ('_tcl_data\\tzdata\\Asia\\Amman',
   'C:\\Python313\\tcl\\tcl8.6\\tzdata\\Asia\\Amman',
   'DATA'),
  ('_tcl_data\\encoding\\cp866.enc',
   'C:\\Python313\\tcl\\tcl8.6\\encoding\\cp866.enc',
   'DATA'),
  ('_tcl_data\\tzdata\\Europe\\Vaduz',
   'C:\\Python313\\tcl\\tcl8.6\\tzdata\\Europe\\Vaduz',
   'DATA'),
  ('_tcl_data\\tzdata\\Atlantic\\Jan_Mayen',
   'C:\\Python313\\tcl\\tcl8.6\\tzdata\\Atlantic\\Jan_Mayen',
   'DATA'),
  ('_tcl_data\\tzdata\\America\\Boa_Vista',
   'C:\\Python313\\tcl\\tcl8.6\\tzdata\\America\\Boa_Vista',
   'DATA'),
  ('_tcl_data\\msgs\\fr_ch.msg',
   'C:\\Python313\\tcl\\tcl8.6\\msgs\\fr_ch.msg',
   'DATA'),
  ('_tcl_data\\msgs\\es_ni.msg',
   'C:\\Python313\\tcl\\tcl8.6\\msgs\\es_ni.msg',
   'DATA'),
  ('_tcl_data\\tzdata\\Asia\\Kolkata',
   'C:\\Python313\\tcl\\tcl8.6\\tzdata\\Asia\\Kolkata',
   'DATA'),
  ('_tcl_data\\tzdata\\Europe\\Prague',
   'C:\\Python313\\tcl\\tcl8.6\\tzdata\\Europe\\Prague',
   'DATA'),
  ('_tcl_data\\msgs\\gl.msg',
   'C:\\Python313\\tcl\\tcl8.6\\msgs\\gl.msg',
   'DATA'),
  ('_tcl_data\\encoding\\iso8859-14.enc',
   'C:\\Python313\\tcl\\tcl8.6\\encoding\\iso8859-14.enc',
   'DATA'),
  ('_tk_data\\clrpick.tcl', 'C:\\Python313\\tcl\\tk8.6\\clrpick.tcl', 'DATA'),
  ('_tcl_data\\tzdata\\Etc\\UTC',
   'C:\\Python313\\tcl\\tcl8.6\\tzdata\\Etc\\UTC',
   'DATA'),
  ('_tcl_data\\tzdata\\Africa\\El_Aaiun',
   'C:\\Python313\\tcl\\tcl8.6\\tzdata\\Africa\\El_Aaiun',
   'DATA'),
  ('_tk_data\\ttk\\vistaTheme.tcl',
   'C:\\Python313\\tcl\\tk8.6\\ttk\\vistaTheme.tcl',
   'DATA'),
  ('_tcl_data\\msgs\\sr.msg',
   'C:\\Python313\\tcl\\tcl8.6\\msgs\\sr.msg',
   'DATA'),
  ('_tcl_data\\tzdata\\Asia\\Ulan_Bator',
   'C:\\Python313\\tcl\\tcl8.6\\tzdata\\Asia\\Ulan_Bator',
   'DATA'),
  ('_tcl_data\\tzdata\\SystemV\\EST5EDT',
   'C:\\Python313\\tcl\\tcl8.6\\tzdata\\SystemV\\EST5EDT',
   'DATA'),
  ('_tcl_data\\tzdata\\Asia\\Samarkand',
   'C:\\Python313\\tcl\\tcl8.6\\tzdata\\Asia\\Samarkand',
   'DATA'),
  ('_tcl_data\\tzdata\\Europe\\Riga',
   'C:\\Python313\\tcl\\tcl8.6\\tzdata\\Europe\\Riga',
   'DATA'),
  ('_tcl_data\\tzdata\\America\\Argentina\\Cordoba',
   'C:\\Python313\\tcl\\tcl8.6\\tzdata\\America\\Argentina\\Cordoba',
   'DATA'),
  ('_tcl_data\\msgs\\kok.msg',
   'C:\\Python313\\tcl\\tcl8.6\\msgs\\kok.msg',
   'DATA'),
  ('_tcl_data\\tzdata\\America\\Noronha',
   'C:\\Python313\\tcl\\tcl8.6\\tzdata\\America\\Noronha',
   'DATA'),
  ('_tcl_data\\tzdata\\Africa\\Ouagadougou',
   'C:\\Python313\\tcl\\tcl8.6\\tzdata\\Africa\\Ouagadougou',
   'DATA'),
  ('_tcl_data\\tzdata\\America\\Manaus',
   'C:\\Python313\\tcl\\tcl8.6\\tzdata\\America\\Manaus',
   'DATA'),
  ('_tcl_data\\tzdata\\America\\Kentucky\\Louisville',
   'C:\\Python313\\tcl\\tcl8.6\\tzdata\\America\\Kentucky\\Louisville',
   'DATA'),
  ('tcl8\\8.5\\tcltest-2.5.8.tm',
   'C:\\Python313\\tcl\\tcl8\\8.5\\tcltest-2.5.8.tm',
   'DATA'),
  ('_tcl_data\\tzdata\\Asia\\Anadyr',
   'C:\\Python313\\tcl\\tcl8.6\\tzdata\\Asia\\Anadyr',
   'DATA'),
  ('_tcl_data\\encoding\\cp864.enc',
   'C:\\Python313\\tcl\\tcl8.6\\encoding\\cp864.enc',
   'DATA'),
  ('_tcl_data\\msgs\\en_sg.msg',
   'C:\\Python313\\tcl\\tcl8.6\\msgs\\en_sg.msg',
   'DATA'),
  ('_tk_data\\listbox.tcl', 'C:\\Python313\\tcl\\tk8.6\\listbox.tcl', 'DATA'),
  ('_tcl_data\\tzdata\\America\\Argentina\\San_Luis',
   'C:\\Python313\\tcl\\tcl8.6\\tzdata\\America\\Argentina\\San_Luis',
   'DATA'),
  ('_tcl_data\\tzdata\\America\\Nipigon',
   'C:\\Python313\\tcl\\tcl8.6\\tzdata\\America\\Nipigon',
   'DATA'),
  ('_tcl_data\\tzdata\\Africa\\Windhoek',
   'C:\\Python313\\tcl\\tcl8.6\\tzdata\\Africa\\Windhoek',
   'DATA'),
  ('_tcl_data\\tzdata\\US\\East-Indiana',
   'C:\\Python313\\tcl\\tcl8.6\\tzdata\\US\\East-Indiana',
   'DATA'),
  ('_tcl_data\\tzdata\\Etc\\GMT+1',
   'C:\\Python313\\tcl\\tcl8.6\\tzdata\\Etc\\GMT+1',
   'DATA'),
  ('_tcl_data\\encoding\\ebcdic.enc',
   'C:\\Python313\\tcl\\tcl8.6\\encoding\\ebcdic.enc',
   'DATA'),
  ('_tcl_data\\tzdata\\Pacific\\Palau',
   'C:\\Python313\\tcl\\tcl8.6\\tzdata\\Pacific\\Palau',
   'DATA'),
  ('_tcl_data\\encoding\\cp860.enc',
   'C:\\Python313\\tcl\\tcl8.6\\encoding\\cp860.enc',
   'DATA'),
  ('_tcl_data\\encoding\\cp1254.enc',
   'C:\\Python313\\tcl\\tcl8.6\\encoding\\cp1254.enc',
   'DATA'),
  ('_tcl_data\\tzdata\\Atlantic\\Reykjavik',
   'C:\\Python313\\tcl\\tcl8.6\\tzdata\\Atlantic\\Reykjavik',
   'DATA'),
  ('_tcl_data\\encoding\\iso8859-4.enc',
   'C:\\Python313\\tcl\\tcl8.6\\encoding\\iso8859-4.enc',
   'DATA'),
  ('_tcl_data\\tzdata\\Asia\\Tomsk',
   'C:\\Python313\\tcl\\tcl8.6\\tzdata\\Asia\\Tomsk',
   'DATA'),
  ('_tcl_data\\msgs\\fa_in.msg',
   'C:\\Python313\\tcl\\tcl8.6\\msgs\\fa_in.msg',
   'DATA'),
  ('_tcl_data\\encoding\\iso8859-13.enc',
   'C:\\Python313\\tcl\\tcl8.6\\encoding\\iso8859-13.enc',
   'DATA'),
  ('_tk_data\\images\\pwrdLogo150.gif',
   'C:\\Python313\\tcl\\tk8.6\\images\\pwrdLogo150.gif',
   'DATA'),
  ('_tcl_data\\tzdata\\America\\Argentina\\San_Juan',
   'C:\\Python313\\tcl\\tcl8.6\\tzdata\\America\\Argentina\\San_Juan',
   'DATA'),
  ('_tcl_data\\tzdata\\Atlantic\\Azores',
   'C:\\Python313\\tcl\\tcl8.6\\tzdata\\Atlantic\\Azores',
   'DATA'),
  ('_tcl_data\\encoding\\macDingbats.enc',
   'C:\\Python313\\tcl\\tcl8.6\\encoding\\macDingbats.enc',
   'DATA'),
  ('_tcl_data\\tzdata\\America\\Vancouver',
   'C:\\Python313\\tcl\\tcl8.6\\tzdata\\America\\Vancouver',
   'DATA'),
  ('_tcl_data\\msgs\\zh.msg',
   'C:\\Python313\\tcl\\tcl8.6\\msgs\\zh.msg',
   'DATA'),
  ('_tcl_data\\tzdata\\Pacific\\Norfolk',
   'C:\\Python313\\tcl\\tcl8.6\\tzdata\\Pacific\\Norfolk',
   'DATA'),
  ('_tk_data\\ttk\\clamTheme.tcl',
   'C:\\Python313\\tcl\\tk8.6\\ttk\\clamTheme.tcl',
   'DATA'),
  ('_tcl_data\\encoding\\cp861.enc',
   'C:\\Python313\\tcl\\tcl8.6\\encoding\\cp861.enc',
   'DATA'),
  ('_tcl_data\\tzdata\\America\\Guyana',
   'C:\\Python313\\tcl\\tcl8.6\\tzdata\\America\\Guyana',
   'DATA'),
  ('_tcl_data\\tzdata\\Etc\\GMT-3',
   'C:\\Python313\\tcl\\tcl8.6\\tzdata\\Etc\\GMT-3',
   'DATA'),
  ('_tcl_data\\tzdata\\Brazil\\East',
   'C:\\Python313\\tcl\\tcl8.6\\tzdata\\Brazil\\East',
   'DATA'),
  ('_tcl_data\\tzdata\\Europe\\Nicosia',
   'C:\\Python313\\tcl\\tcl8.6\\tzdata\\Europe\\Nicosia',
   'DATA'),
  ('_tcl_data\\msgs\\es_hn.msg',
   'C:\\Python313\\tcl\\tcl8.6\\msgs\\es_hn.msg',
   'DATA'),
  ('_tcl_data\\tzdata\\Europe\\Oslo',
   'C:\\Python313\\tcl\\tcl8.6\\tzdata\\Europe\\Oslo',
   'DATA'),
  ('_tcl_data\\tzdata\\Australia\\Yancowinna',
   'C:\\Python313\\tcl\\tcl8.6\\tzdata\\Australia\\Yancowinna',
   'DATA'),
  ('_tcl_data\\tzdata\\Asia\\Ulaanbaatar',
   'C:\\Python313\\tcl\\tcl8.6\\tzdata\\Asia\\Ulaanbaatar',
   'DATA'),
  ('_tcl_data\\tzdata\\Africa\\Kampala',
   'C:\\Python313\\tcl\\tcl8.6\\tzdata\\Africa\\Kampala',
   'DATA'),
  ('_tcl_data\\tzdata\\Asia\\Karachi',
   'C:\\Python313\\tcl\\tcl8.6\\tzdata\\Asia\\Karachi',
   'DATA'),
  ('_tcl_data\\tzdata\\Pacific\\Efate',
   'C:\\Python313\\tcl\\tcl8.6\\tzdata\\Pacific\\Efate',
   'DATA'),
  ('_tcl_data\\encoding\\dingbats.enc',
   'C:\\Python313\\tcl\\tcl8.6\\encoding\\dingbats.enc',
   'DATA'),
  ('_tcl_data\\tzdata\\Europe\\Gibraltar',
   'C:\\Python313\\tcl\\tcl8.6\\tzdata\\Europe\\Gibraltar',
   'DATA'),
  ('_tcl_data\\msgs\\en_hk.msg',
   'C:\\Python313\\tcl\\tcl8.6\\msgs\\en_hk.msg',
   'DATA'),
  ('_tcl_data\\tzdata\\America\\Cayman',
   'C:\\Python313\\tcl\\tcl8.6\\tzdata\\America\\Cayman',
   'DATA'),
  ('_tcl_data\\tzdata\\America\\Knox_IN',
   'C:\\Python313\\tcl\\tcl8.6\\tzdata\\America\\Knox_IN',
   'DATA'),
  ('_tcl_data\\tzdata\\Europe\\London',
   'C:\\Python313\\tcl\\tcl8.6\\tzdata\\Europe\\London',
   'DATA'),
  ('_tcl_data\\tzdata\\Europe\\Samara',
   'C:\\Python313\\tcl\\tcl8.6\\tzdata\\Europe\\Samara',
   'DATA'),
  ('_tcl_data\\tzdata\\Australia\\ACT',
   'C:\\Python313\\tcl\\tcl8.6\\tzdata\\Australia\\ACT',
   'DATA'),
  ('_tcl_data\\tzdata\\Asia\\Almaty',
   'C:\\Python313\\tcl\\tcl8.6\\tzdata\\Asia\\Almaty',
   'DATA'),
  ('_tcl_data\\tzdata\\Asia\\Urumqi',
   'C:\\Python313\\tcl\\tcl8.6\\tzdata\\Asia\\Urumqi',
   'DATA'),
  ('_tcl_data\\tzdata\\Africa\\Nairobi',
   'C:\\Python313\\tcl\\tcl8.6\\tzdata\\Africa\\Nairobi',
   'DATA'),
  ('_tcl_data\\msgs\\sk.msg',
   'C:\\Python313\\tcl\\tcl8.6\\msgs\\sk.msg',
   'DATA'),
  ('_tcl_data\\tzdata\\Asia\\Dacca',
   'C:\\Python313\\tcl\\tcl8.6\\tzdata\\Asia\\Dacca',
   'DATA'),
  ('_tcl_data\\encoding\\tis-620.enc',
   'C:\\Python313\\tcl\\tcl8.6\\encoding\\tis-620.enc',
   'DATA'),
  ('_tcl_data\\tzdata\\Australia\\Tasmania',
   'C:\\Python313\\tcl\\tcl8.6\\tzdata\\Australia\\Tasmania',
   'DATA'),
  ('_tk_data\\ttk\\utils.tcl',
   'C:\\Python313\\tcl\\tk8.6\\ttk\\utils.tcl',
   'DATA'),
  ('_tcl_data\\tzdata\\Europe\\Kirov',
   'C:\\Python313\\tcl\\tcl8.6\\tzdata\\Europe\\Kirov',
   'DATA'),
  ('_tcl_data\\encoding\\iso8859-16.enc',
   'C:\\Python313\\tcl\\tcl8.6\\encoding\\iso8859-16.enc',
   'DATA'),
  ('_tcl_data\\tzdata\\America\\Moncton',
   'C:\\Python313\\tcl\\tcl8.6\\tzdata\\America\\Moncton',
   'DATA'),
  ('_tcl_data\\tzdata\\US\\Indiana-Starke',
   'C:\\Python313\\tcl\\tcl8.6\\tzdata\\US\\Indiana-Starke',
   'DATA'),
  ('_tcl_data\\msgs\\hr.msg',
   'C:\\Python313\\tcl\\tcl8.6\\msgs\\hr.msg',
   'DATA'),
  ('_tk_data\\panedwindow.tcl',
   'C:\\Python313\\tcl\\tk8.6\\panedwindow.tcl',
   'DATA'),
  ('_tcl_data\\tzdata\\Asia\\Rangoon',
   'C:\\Python313\\tcl\\tcl8.6\\tzdata\\Asia\\Rangoon',
   'DATA'),
  ('_tcl_data\\tzdata\\America\\Rankin_Inlet',
   'C:\\Python313\\tcl\\tcl8.6\\tzdata\\America\\Rankin_Inlet',
   'DATA'),
  ('_tcl_data\\tzdata\\Pacific\\Guam',
   'C:\\Python313\\tcl\\tcl8.6\\tzdata\\Pacific\\Guam',
   'DATA'),
  ('_tcl_data\\tzdata\\America\\Recife',
   'C:\\Python313\\tcl\\tcl8.6\\tzdata\\America\\Recife',
   'DATA'),
  ('_tcl_data\\tzdata\\Asia\\Krasnoyarsk',
   'C:\\Python313\\tcl\\tcl8.6\\tzdata\\Asia\\Krasnoyarsk',
   'DATA'),
  ('_tcl_data\\tzdata\\Africa\\Lusaka',
   'C:\\Python313\\tcl\\tcl8.6\\tzdata\\Africa\\Lusaka',
   'DATA'),
  ('_tcl_data\\tzdata\\America\\Costa_Rica',
   'C:\\Python313\\tcl\\tcl8.6\\tzdata\\America\\Costa_Rica',
   'DATA'),
  ('_tcl_data\\tzdata\\Africa\\Maseru',
   'C:\\Python313\\tcl\\tcl8.6\\tzdata\\Africa\\Maseru',
   'DATA'),
  ('_tcl_data\\encoding\\cns11643.enc',
   'C:\\Python313\\tcl\\tcl8.6\\encoding\\cns11643.enc',
   'DATA'),
  ('_tcl_data\\tzdata\\Canada\\Atlantic',
   'C:\\Python313\\tcl\\tcl8.6\\tzdata\\Canada\\Atlantic',
   'DATA'),
  ('_tcl_data\\tzdata\\Europe\\Tirane',
   'C:\\Python313\\tcl\\tcl8.6\\tzdata\\Europe\\Tirane',
   'DATA'),
  ('_tk_data\\dialog.tcl', 'C:\\Python313\\tcl\\tk8.6\\dialog.tcl', 'DATA'),
  ('_tcl_data\\msgs\\id_id.msg',
   'C:\\Python313\\tcl\\tcl8.6\\msgs\\id_id.msg',
   'DATA'),
  ('_tcl_data\\msgs\\zh_cn.msg',
   'C:\\Python313\\tcl\\tcl8.6\\msgs\\zh_cn.msg',
   'DATA'),
  ('_tcl_data\\tzdata\\Pacific\\Truk',
   'C:\\Python313\\tcl\\tcl8.6\\tzdata\\Pacific\\Truk',
   'DATA'),
  ('_tcl_data\\tzdata\\Africa\\Libreville',
   'C:\\Python313\\tcl\\tcl8.6\\tzdata\\Africa\\Libreville',
   'DATA'),
  ('_tcl_data\\tzdata\\America\\Rainy_River',
   'C:\\Python313\\tcl\\tcl8.6\\tzdata\\America\\Rainy_River',
   'DATA'),
  ('_tcl_data\\tzdata\\Australia\\Melbourne',
   'C:\\Python313\\tcl\\tcl8.6\\tzdata\\Australia\\Melbourne',
   'DATA'),
  ('_tcl_data\\tzdata\\Antarctica\\South_Pole',
   'C:\\Python313\\tcl\\tcl8.6\\tzdata\\Antarctica\\South_Pole',
   'DATA'),
  ('_tcl_data\\tzdata\\America\\Jujuy',
   'C:\\Python313\\tcl\\tcl8.6\\tzdata\\America\\Jujuy',
   'DATA'),
  ('_tcl_data\\tzdata\\Pacific\\Easter',
   'C:\\Python313\\tcl\\tcl8.6\\tzdata\\Pacific\\Easter',
   'DATA'),
  ('_tcl_data\\tzdata\\America\\Belem',
   'C:\\Python313\\tcl\\tcl8.6\\tzdata\\America\\Belem',
   'DATA'),
  ('_tcl_data\\tzdata\\Africa\\Timbuktu',
   'C:\\Python313\\tcl\\tcl8.6\\tzdata\\Africa\\Timbuktu',
   'DATA'),
  ('_tcl_data\\msgs\\es_pa.msg',
   'C:\\Python313\\tcl\\tcl8.6\\msgs\\es_pa.msg',
   'DATA'),
  ('_tcl_data\\tzdata\\Etc\\GMT-12',
   'C:\\Python313\\tcl\\tcl8.6\\tzdata\\Etc\\GMT-12',
   'DATA'),
  ('_tcl_data\\tzdata\\America\\Montevideo',
   'C:\\Python313\\tcl\\tcl8.6\\tzdata\\America\\Montevideo',
   'DATA'),
  ('_tcl_data\\tzdata\\Africa\\Douala',
   'C:\\Python313\\tcl\\tcl8.6\\tzdata\\Africa\\Douala',
   'DATA'),
  ('_tcl_data\\tzdata\\Hongkong',
   'C:\\Python313\\tcl\\tcl8.6\\tzdata\\Hongkong',
   'DATA'),
  ('_tcl_data\\tzdata\\America\\Montreal',
   'C:\\Python313\\tcl\\tcl8.6\\tzdata\\America\\Montreal',
   'DATA'),
  ('_tcl_data\\msgs\\te_in.msg',
   'C:\\Python313\\tcl\\tcl8.6\\msgs\\te_in.msg',
   'DATA'),
  ('_tk_data\\ttk\\cursors.tcl',
   'C:\\Python313\\tcl\\tk8.6\\ttk\\cursors.tcl',
   'DATA'),
  ('_tcl_data\\tzdata\\Pacific\\Wake',
   'C:\\Python313\\tcl\\tcl8.6\\tzdata\\Pacific\\Wake',
   'DATA'),
  ('_tk_data\\xmfbox.tcl', 'C:\\Python313\\tcl\\tk8.6\\xmfbox.tcl', 'DATA'),
  ('_tk_data\\choosedir.tcl',
   'C:\\Python313\\tcl\\tk8.6\\choosedir.tcl',
   'DATA'),
  ('_tcl_data\\tzdata\\America\\Marigot',
   'C:\\Python313\\tcl\\tcl8.6\\tzdata\\America\\Marigot',
   'DATA'),
  ('_tcl_data\\encoding\\cp1257.enc',
   'C:\\Python313\\tcl\\tcl8.6\\encoding\\cp1257.enc',
   'DATA'),
  ('_tcl_data\\encoding\\cp857.enc',
   'C:\\Python313\\tcl\\tcl8.6\\encoding\\cp857.enc',
   'DATA'),
  ('_tk_data\\msgs\\el.msg', 'C:\\Python313\\tcl\\tk8.6\\msgs\\el.msg', 'DATA'),
  ('_tcl_data\\tzdata\\Europe\\Warsaw',
   'C:\\Python313\\tcl\\tcl8.6\\tzdata\\Europe\\Warsaw',
   'DATA'),
  ('_tcl_data\\tzdata\\Asia\\Qatar',
   'C:\\Python313\\tcl\\tcl8.6\\tzdata\\Asia\\Qatar',
   'DATA'),
  ('_tcl_data\\tzdata\\Indian\\Mayotte',
   'C:\\Python313\\tcl\\tcl8.6\\tzdata\\Indian\\Mayotte',
   'DATA'),
  ('_tcl_data\\msgs\\es_bo.msg',
   'C:\\Python313\\tcl\\tcl8.6\\msgs\\es_bo.msg',
   'DATA'),
  ('_tcl_data\\tzdata\\Canada\\Eastern',
   'C:\\Python313\\tcl\\tcl8.6\\tzdata\\Canada\\Eastern',
   'DATA'),
  ('_tcl_data\\tzdata\\Africa\\Bissau',
   'C:\\Python313\\tcl\\tcl8.6\\tzdata\\Africa\\Bissau',
   'DATA'),
  ('_tcl_data\\msgs\\es_pe.msg',
   'C:\\Python313\\tcl\\tcl8.6\\msgs\\es_pe.msg',
   'DATA'),
  ('_tcl_data\\tzdata\\America\\Matamoros',
   'C:\\Python313\\tcl\\tcl8.6\\tzdata\\America\\Matamoros',
   'DATA'),
  ('_tcl_data\\tzdata\\Pacific\\Enderbury',
   'C:\\Python313\\tcl\\tcl8.6\\tzdata\\Pacific\\Enderbury',
   'DATA'),
  ('_tcl_data\\encoding\\koi8-t.enc',
   'C:\\Python313\\tcl\\tcl8.6\\encoding\\koi8-t.enc',
   'DATA'),
  ('_tk_data\\msgs\\it.msg', 'C:\\Python313\\tcl\\tk8.6\\msgs\\it.msg', 'DATA'),
  ('_tcl_data\\tzdata\\CST6CDT',
   'C:\\Python313\\tcl\\tcl8.6\\tzdata\\CST6CDT',
   'DATA'),
  ('_tcl_data\\encoding\\macTurkish.enc',
   'C:\\Python313\\tcl\\tcl8.6\\encoding\\macTurkish.enc',
   'DATA'),
  ('_tcl_data\\msgs\\eu.msg',
   'C:\\Python313\\tcl\\tcl8.6\\msgs\\eu.msg',
   'DATA'),
  ('_tcl_data\\tzdata\\Universal',
   'C:\\Python313\\tcl\\tcl8.6\\tzdata\\Universal',
   'DATA'),
  ('_tcl_data\\tzdata\\America\\Ojinaga',
   'C:\\Python313\\tcl\\tcl8.6\\tzdata\\America\\Ojinaga',
   'DATA'),
  ('_tcl_data\\tzdata\\Africa\\Dar_es_Salaam',
   'C:\\Python313\\tcl\\tcl8.6\\tzdata\\Africa\\Dar_es_Salaam',
   'DATA'),
  ('_tcl_data\\encoding\\iso2022.enc',
   'C:\\Python313\\tcl\\tcl8.6\\encoding\\iso2022.enc',
   'DATA'),
  ('_tcl_data\\tzdata\\America\\Edmonton',
   'C:\\Python313\\tcl\\tcl8.6\\tzdata\\America\\Edmonton',
   'DATA'),
  ('_tcl_data\\tzdata\\America\\Grand_Turk',
   'C:\\Python313\\tcl\\tcl8.6\\tzdata\\America\\Grand_Turk',
   'DATA'),
  ('_tcl_data\\tzdata\\Africa\\Johannesburg',
   'C:\\Python313\\tcl\\tcl8.6\\tzdata\\Africa\\Johannesburg',
   'DATA'),
  ('_tcl_data\\msgs\\eu_es.msg',
   'C:\\Python313\\tcl\\tcl8.6\\msgs\\eu_es.msg',
   'DATA'),
  ('_tcl_data\\msgs\\it.msg',
   'C:\\Python313\\tcl\\tcl8.6\\msgs\\it.msg',
   'DATA'),
  ('_tk_data\\mkpsenc.tcl', 'C:\\Python313\\tcl\\tk8.6\\mkpsenc.tcl', 'DATA'),
  ('_tcl_data\\tzdata\\Africa\\Kinshasa',
   'C:\\Python313\\tcl\\tcl8.6\\tzdata\\Africa\\Kinshasa',
   'DATA'),
  ('tcl8\\8.6\\http-2.9.8.tm',
   'C:\\Python313\\tcl\\tcl8\\8.6\\http-2.9.8.tm',
   'DATA'),
  ('_tcl_data\\tzdata\\US\\Hawaii',
   'C:\\Python313\\tcl\\tcl8.6\\tzdata\\US\\Hawaii',
   'DATA'),
  ('_tcl_data\\tzdata\\Portugal',
   'C:\\Python313\\tcl\\tcl8.6\\tzdata\\Portugal',
   'DATA'),
  ('_tcl_data\\tzdata\\America\\Argentina\\Rio_Gallegos',
   'C:\\Python313\\tcl\\tcl8.6\\tzdata\\America\\Argentina\\Rio_Gallegos',
   'DATA'),
  ('_tcl_data\\tzdata\\America\\Creston',
   'C:\\Python313\\tcl\\tcl8.6\\tzdata\\America\\Creston',
   'DATA'),
  ('_tcl_data\\word.tcl', 'C:\\Python313\\tcl\\tcl8.6\\word.tcl', 'DATA'),
  ('_tcl_data\\tzdata\\Pacific\\Kwajalein',
   'C:\\Python313\\tcl\\tcl8.6\\tzdata\\Pacific\\Kwajalein',
   'DATA'),
  ('_tcl_data\\tzdata\\Africa\\Casablanca',
   'C:\\Python313\\tcl\\tcl8.6\\tzdata\\Africa\\Casablanca',
   'DATA'),
  ('_tk_data\\ttk\\panedwindow.tcl',
   'C:\\Python313\\tcl\\tk8.6\\ttk\\panedwindow.tcl',
   'DATA'),
  ('_tcl_data\\tm.tcl', 'C:\\Python313\\tcl\\tcl8.6\\tm.tcl', 'DATA'),
  ('_tcl_data\\tzdata\\SystemV\\YST9YDT',
   'C:\\Python313\\tcl\\tcl8.6\\tzdata\\SystemV\\YST9YDT',
   'DATA'),
  ('_tcl_data\\tzdata\\Pacific\\Johnston',
   'C:\\Python313\\tcl\\tcl8.6\\tzdata\\Pacific\\Johnston',
   'DATA'),
  ('_tcl_data\\tzdata\\US\\Alaska',
   'C:\\Python313\\tcl\\tcl8.6\\tzdata\\US\\Alaska',
   'DATA'),
  ('_tcl_data\\tzdata\\Asia\\Shanghai',
   'C:\\Python313\\tcl\\tcl8.6\\tzdata\\Asia\\Shanghai',
   'DATA'),
  ('_tcl_data\\msgs\\mr_in.msg',
   'C:\\Python313\\tcl\\tcl8.6\\msgs\\mr_in.msg',
   'DATA'),
  ('_tcl_data\\tzdata\\America\\Tegucigalpa',
   'C:\\Python313\\tcl\\tcl8.6\\tzdata\\America\\Tegucigalpa',
   'DATA'),
  ('_tcl_data\\msgs\\lv.msg',
   'C:\\Python313\\tcl\\tcl8.6\\msgs\\lv.msg',
   'DATA'),
  ('_tcl_data\\encoding\\iso8859-2.enc',
   'C:\\Python313\\tcl\\tcl8.6\\encoding\\iso8859-2.enc',
   'DATA'),
  ('_tcl_data\\tzdata\\Pacific\\Fiji',
   'C:\\Python313\\tcl\\tcl8.6\\tzdata\\Pacific\\Fiji',
   'DATA'),
  ('_tcl_data\\tzdata\\Etc\\GMT-0',
   'C:\\Python313\\tcl\\tcl8.6\\tzdata\\Etc\\GMT-0',
   'DATA'),
  ('_tcl_data\\msgs\\kw_gb.msg',
   'C:\\Python313\\tcl\\tcl8.6\\msgs\\kw_gb.msg',
   'DATA'),
  ('_tcl_data\\tzdata\\Asia\\Hong_Kong',
   'C:\\Python313\\tcl\\tcl8.6\\tzdata\\Asia\\Hong_Kong',
   'DATA'),
  ('_tcl_data\\tzdata\\Asia\\Seoul',
   'C:\\Python313\\tcl\\tcl8.6\\tzdata\\Asia\\Seoul',
   'DATA'),
  ('_tcl_data\\tzdata\\Iceland',
   'C:\\Python313\\tcl\\tcl8.6\\tzdata\\Iceland',
   'DATA'),
  ('_tcl_data\\tzdata\\America\\Fort_Wayne',
   'C:\\Python313\\tcl\\tcl8.6\\tzdata\\America\\Fort_Wayne',
   'DATA'),
  ('_tk_data\\images\\README',
   'C:\\Python313\\tcl\\tk8.6\\images\\README',
   'DATA'),
  ('_tcl_data\\encoding\\iso8859-3.enc',
   'C:\\Python313\\tcl\\tcl8.6\\encoding\\iso8859-3.enc',
   'DATA'),
  ('_tcl_data\\tzdata\\Asia\\Oral',
   'C:\\Python313\\tcl\\tcl8.6\\tzdata\\Asia\\Oral',
   'DATA'),
  ('_tcl_data\\tzdata\\America\\Inuvik',
   'C:\\Python313\\tcl\\tcl8.6\\tzdata\\America\\Inuvik',
   'DATA'),
  ('_tcl_data\\tzdata\\Indian\\Comoro',
   'C:\\Python313\\tcl\\tcl8.6\\tzdata\\Indian\\Comoro',
   'DATA'),
  ('_tcl_data\\encoding\\macRomania.enc',
   'C:\\Python313\\tcl\\tcl8.6\\encoding\\macRomania.enc',
   'DATA'),
  ('_tcl_data\\tzdata\\Asia\\Macao',
   'C:\\Python313\\tcl\\tcl8.6\\tzdata\\Asia\\Macao',
   'DATA'),
  ('_tcl_data\\encoding\\cp1253.enc',
   'C:\\Python313\\tcl\\tcl8.6\\encoding\\cp1253.enc',
   'DATA'),
  ('_tcl_data\\tzdata\\Libya',
   'C:\\Python313\\tcl\\tcl8.6\\tzdata\\Libya',
   'DATA'),
  ('_tk_data\\images\\logo64.gif',
   'C:\\Python313\\tcl\\tk8.6\\images\\logo64.gif',
   'DATA'),
  ('_tcl_data\\tzdata\\Asia\\Ashgabat',
   'C:\\Python313\\tcl\\tcl8.6\\tzdata\\Asia\\Ashgabat',
   'DATA'),
  ('_tcl_data\\package.tcl', 'C:\\Python313\\tcl\\tcl8.6\\package.tcl', 'DATA'),
  ('_tcl_data\\tzdata\\W-SU',
   'C:\\Python313\\tcl\\tcl8.6\\tzdata\\W-SU',
   'DATA'),
  ('_tcl_data\\msgs\\nn.msg',
   'C:\\Python313\\tcl\\tcl8.6\\msgs\\nn.msg',
   'DATA'),
  ('_tcl_data\\msgs\\es_mx.msg',
   'C:\\Python313\\tcl\\tcl8.6\\msgs\\es_mx.msg',
   'DATA'),
  ('_tcl_data\\tzdata\\WET', 'C:\\Python313\\tcl\\tcl8.6\\tzdata\\WET', 'DATA'),
  ('_tcl_data\\tzdata\\America\\Hermosillo',
   'C:\\Python313\\tcl\\tcl8.6\\tzdata\\America\\Hermosillo',
   'DATA'),
  ('_tcl_data\\tzdata\\Europe\\Istanbul',
   'C:\\Python313\\tcl\\tcl8.6\\tzdata\\Europe\\Istanbul',
   'DATA'),
  ('_tcl_data\\msgs\\es_uy.msg',
   'C:\\Python313\\tcl\\tcl8.6\\msgs\\es_uy.msg',
   'DATA'),
  ('_tcl_data\\tzdata\\Australia\\Queensland',
   'C:\\Python313\\tcl\\tcl8.6\\tzdata\\Australia\\Queensland',
   'DATA'),
  ('_tcl_data\\tzdata\\Europe\\Bratislava',
   'C:\\Python313\\tcl\\tcl8.6\\tzdata\\Europe\\Bratislava',
   'DATA'),
  ('_tcl_data\\tzdata\\Europe\\Madrid',
   'C:\\Python313\\tcl\\tcl8.6\\tzdata\\Europe\\Madrid',
   'DATA'),
  ('_tcl_data\\tzdata\\America\\Anguilla',
   'C:\\Python313\\tcl\\tcl8.6\\tzdata\\America\\Anguilla',
   'DATA'),
  ('_tcl_data\\tzdata\\America\\Aruba',
   'C:\\Python313\\tcl\\tcl8.6\\tzdata\\America\\Aruba',
   'DATA'),
  ('_tcl_data\\tzdata\\America\\Santo_Domingo',
   'C:\\Python313\\tcl\\tcl8.6\\tzdata\\America\\Santo_Domingo',
   'DATA'),
  ('_tcl_data\\msgs\\en_ie.msg',
   'C:\\Python313\\tcl\\tcl8.6\\msgs\\en_ie.msg',
   'DATA'),
  ('_tk_data\\msgs\\nl.msg', 'C:\\Python313\\tcl\\tk8.6\\msgs\\nl.msg', 'DATA'),
  ('_tcl_data\\tzdata\\Europe\\Zaporozhye',
   'C:\\Python313\\tcl\\tcl8.6\\tzdata\\Europe\\Zaporozhye',
   'DATA'),
  ('_tcl_data\\tzdata\\America\\Rio_Branco',
   'C:\\Python313\\tcl\\tcl8.6\\tzdata\\America\\Rio_Branco',
   'DATA'),
  ('_tcl_data\\tzdata\\Asia\\Katmandu',
   'C:\\Python313\\tcl\\tcl8.6\\tzdata\\Asia\\Katmandu',
   'DATA'),
  ('_tcl_data\\encoding\\cp936.enc',
   'C:\\Python313\\tcl\\tcl8.6\\encoding\\cp936.enc',
   'DATA'),
  ('_tcl_data\\tzdata\\Europe\\Volgograd',
   'C:\\Python313\\tcl\\tcl8.6\\tzdata\\Europe\\Volgograd',
   'DATA'),
  ('_tcl_data\\tzdata\\Asia\\Muscat',
   'C:\\Python313\\tcl\\tcl8.6\\tzdata\\Asia\\Muscat',
   'DATA'),
  ('_tcl_data\\tzdata\\Africa\\Lome',
   'C:\\Python313\\tcl\\tcl8.6\\tzdata\\Africa\\Lome',
   'DATA'),
  ('_tcl_data\\encoding\\gb1988.enc',
   'C:\\Python313\\tcl\\tcl8.6\\encoding\\gb1988.enc',
   'DATA'),
  ('_tcl_data\\tzdata\\America\\Mazatlan',
   'C:\\Python313\\tcl\\tcl8.6\\tzdata\\America\\Mazatlan',
   'DATA'),
  ('_tcl_data\\tzdata\\Africa\\Porto-Novo',
   'C:\\Python313\\tcl\\tcl8.6\\tzdata\\Africa\\Porto-Novo',
   'DATA'),
  ('_tcl_data\\msgs\\nb.msg',
   'C:\\Python313\\tcl\\tcl8.6\\msgs\\nb.msg',
   'DATA'),
  ('_tcl_data\\tzdata\\Arctic\\Longyearbyen',
   'C:\\Python313\\tcl\\tcl8.6\\tzdata\\Arctic\\Longyearbyen',
   'DATA'),
  ('_tk_data\\menu.tcl', 'C:\\Python313\\tcl\\tk8.6\\menu.tcl', 'DATA'),
  ('_tcl_data\\tzdata\\Asia\\Omsk',
   'C:\\Python313\\tcl\\tcl8.6\\tzdata\\Asia\\Omsk',
   'DATA'),
  ('_tcl_data\\tzdata\\Asia\\Phnom_Penh',
   'C:\\Python313\\tcl\\tcl8.6\\tzdata\\Asia\\Phnom_Penh',
   'DATA'),
  ('_tk_data\\ttk\\ttk.tcl', 'C:\\Python313\\tcl\\tk8.6\\ttk\\ttk.tcl', 'DATA'),
  ('_tcl_data\\tzdata\\Asia\\Riyadh',
   'C:\\Python313\\tcl\\tcl8.6\\tzdata\\Asia\\Riyadh',
   'DATA'),
  ('_tcl_data\\tzdata\\Pacific\\Bougainville',
   'C:\\Python313\\tcl\\tcl8.6\\tzdata\\Pacific\\Bougainville',
   'DATA'),
  ('_tcl_data\\tzdata\\Indian\\Kerguelen',
   'C:\\Python313\\tcl\\tcl8.6\\tzdata\\Indian\\Kerguelen',
   'DATA'),
  ('_tcl_data\\tzdata\\Chile\\EasterIsland',
   'C:\\Python313\\tcl\\tcl8.6\\tzdata\\Chile\\EasterIsland',
   'DATA'),
  ('_tcl_data\\auto.tcl', 'C:\\Python313\\tcl\\tcl8.6\\auto.tcl', 'DATA'),
  ('_tcl_data\\tzdata\\US\\Central',
   'C:\\Python313\\tcl\\tcl8.6\\tzdata\\US\\Central',
   'DATA'),
  ('_tcl_data\\msgs\\es_co.msg',
   'C:\\Python313\\tcl\\tcl8.6\\msgs\\es_co.msg',
   'DATA'),
  ('_tcl_data\\encoding\\cp1252.enc',
   'C:\\Python313\\tcl\\tcl8.6\\encoding\\cp1252.enc',
   'DATA'),
  ('_tcl_data\\tzdata\\Europe\\Zagreb',
   'C:\\Python313\\tcl\\tcl8.6\\tzdata\\Europe\\Zagreb',
   'DATA'),
  ('_tcl_data\\tzdata\\Antarctica\\DumontDUrville',
   'C:\\Python313\\tcl\\tcl8.6\\tzdata\\Antarctica\\DumontDUrville',
   'DATA'),
  ('_tcl_data\\msgs\\gv_gb.msg',
   'C:\\Python313\\tcl\\tcl8.6\\msgs\\gv_gb.msg',
   'DATA'),
  ('_tcl_data\\tzdata\\Pacific\\Yap',
   'C:\\Python313\\tcl\\tcl8.6\\tzdata\\Pacific\\Yap',
   'DATA'),
  ('_tcl_data\\tzdata\\Europe\\Isle_of_Man',
   'C:\\Python313\\tcl\\tcl8.6\\tzdata\\Europe\\Isle_of_Man',
   'DATA'),
  ('_tcl_data\\tzdata\\America\\Argentina\\Salta',
   'C:\\Python313\\tcl\\tcl8.6\\tzdata\\America\\Argentina\\Salta',
   'DATA'),
  ('_tcl_data\\msgs\\pt_br.msg',
   'C:\\Python313\\tcl\\tcl8.6\\msgs\\pt_br.msg',
   'DATA'),
  ('_tcl_data\\tzdata\\America\\Panama',
   'C:\\Python313\\tcl\\tcl8.6\\tzdata\\America\\Panama',
   'DATA'),
  ('_tcl_data\\tzdata\\Zulu',
   'C:\\Python313\\tcl\\tcl8.6\\tzdata\\Zulu',
   'DATA'),
  ('_tcl_data\\tzdata\\SystemV\\MST7',
   'C:\\Python313\\tcl\\tcl8.6\\tzdata\\SystemV\\MST7',
   'DATA'),
  ('_tcl_data\\tzdata\\America\\Ciudad_Juarez',
   'C:\\Python313\\tcl\\tcl8.6\\tzdata\\America\\Ciudad_Juarez',
   'DATA'),
  ('_tcl_data\\tzdata\\Europe\\Tiraspol',
   'C:\\Python313\\tcl\\tcl8.6\\tzdata\\Europe\\Tiraspol',
   'DATA'),
  ('_tk_data\\bgerror.tcl', 'C:\\Python313\\tcl\\tk8.6\\bgerror.tcl', 'DATA'),
  ('_tcl_data\\msgs\\ru.msg',
   'C:\\Python313\\tcl\\tcl8.6\\msgs\\ru.msg',
   'DATA'),
  ('_tcl_data\\tzdata\\Australia\\Sydney',
   'C:\\Python313\\tcl\\tcl8.6\\tzdata\\Australia\\Sydney',
   'DATA'),
  ('_tcl_data\\tzdata\\US\\Samoa',
   'C:\\Python313\\tcl\\tcl8.6\\tzdata\\US\\Samoa',
   'DATA'),
  ('_tcl_data\\msgs\\kw.msg',
   'C:\\Python313\\tcl\\tcl8.6\\msgs\\kw.msg',
   'DATA'),
  ('_tcl_data\\encoding\\cp862.enc',
   'C:\\Python313\\tcl\\tcl8.6\\encoding\\cp862.enc',
   'DATA'),
  ('_tcl_data\\tzdata\\Europe\\Stockholm',
   'C:\\Python313\\tcl\\tcl8.6\\tzdata\\Europe\\Stockholm',
   'DATA'),
  ('_tk_data\\megawidget.tcl',
   'C:\\Python313\\tcl\\tk8.6\\megawidget.tcl',
   'DATA'),
  ('_tcl_data\\tzdata\\SystemV\\MST7MDT',
   'C:\\Python313\\tcl\\tcl8.6\\tzdata\\SystemV\\MST7MDT',
   'DATA'),
  ('_tcl_data\\msgs\\ta.msg',
   'C:\\Python313\\tcl\\tcl8.6\\msgs\\ta.msg',
   'DATA'),
  ('_tk_data\\ttk\\progress.tcl',
   'C:\\Python313\\tcl\\tk8.6\\ttk\\progress.tcl',
   'DATA'),
  ('_tcl_data\\tzdata\\America\\Yellowknife',
   'C:\\Python313\\tcl\\tcl8.6\\tzdata\\America\\Yellowknife',
   'DATA'),
  ('_tk_data\\ttk\\notebook.tcl',
   'C:\\Python313\\tcl\\tk8.6\\ttk\\notebook.tcl',
   'DATA'),
  ('_tcl_data\\msgs\\en_bw.msg',
   'C:\\Python313\\tcl\\tcl8.6\\msgs\\en_bw.msg',
   'DATA'),
  ('_tcl_data\\tzdata\\America\\Coral_Harbour',
   'C:\\Python313\\tcl\\tcl8.6\\tzdata\\America\\Coral_Harbour',
   'DATA'),
  ('_tcl_data\\msgs\\mr.msg',
   'C:\\Python313\\tcl\\tcl8.6\\msgs\\mr.msg',
   'DATA'),
  ('_tcl_data\\tzdata\\Europe\\Copenhagen',
   'C:\\Python313\\tcl\\tcl8.6\\tzdata\\Europe\\Copenhagen',
   'DATA'),
  ('_tcl_data\\tzdata\\Asia\\Tashkent',
   'C:\\Python313\\tcl\\tcl8.6\\tzdata\\Asia\\Tashkent',
   'DATA'),
  ('_tcl_data\\tzdata\\America\\Asuncion',
   'C:\\Python313\\tcl\\tcl8.6\\tzdata\\America\\Asuncion',
   'DATA'),
  ('_tcl_data\\tzdata\\Etc\\GMT',
   'C:\\Python313\\tcl\\tcl8.6\\tzdata\\Etc\\GMT',
   'DATA'),
  ('_tcl_data\\tzdata\\America\\Cuiaba',
   'C:\\Python313\\tcl\\tcl8.6\\tzdata\\America\\Cuiaba',
   'DATA'),
  ('_tcl_data\\tzdata\\SystemV\\YST9',
   'C:\\Python313\\tcl\\tcl8.6\\tzdata\\SystemV\\YST9',
   'DATA'),
  ('_tcl_data\\tzdata\\America\\Indiana\\Winamac',
   'C:\\Python313\\tcl\\tcl8.6\\tzdata\\America\\Indiana\\Winamac',
   'DATA'),
  ('_tcl_data\\encoding\\iso8859-7.enc',
   'C:\\Python313\\tcl\\tcl8.6\\encoding\\iso8859-7.enc',
   'DATA'),
  ('_tcl_data\\encoding\\cp852.enc',
   'C:\\Python313\\tcl\\tcl8.6\\encoding\\cp852.enc',
   'DATA'),
  ('_tcl_data\\tzdata\\America\\Buenos_Aires',
   'C:\\Python313\\tcl\\tcl8.6\\tzdata\\America\\Buenos_Aires',
   'DATA'),
  ('_tcl_data\\tzdata\\Europe\\Brussels',
   'C:\\Python313\\tcl\\tcl8.6\\tzdata\\Europe\\Brussels',
   'DATA'),
  ('_tk_data\\unsupported.tcl',
   'C:\\Python313\\tcl\\tk8.6\\unsupported.tcl',
   'DATA'),
  ('tcl8\\8.4\\platform\\shell-1.1.4.tm',
   'C:\\Python313\\tcl\\tcl8\\8.4\\platform\\shell-1.1.4.tm',
   'DATA'),
  ('_tcl_data\\encoding\\cp950.enc',
   'C:\\Python313\\tcl\\tcl8.6\\encoding\\cp950.enc',
   'DATA'),
  ('_tcl_data\\tzdata\\America\\El_Salvador',
   'C:\\Python313\\tcl\\tcl8.6\\tzdata\\America\\El_Salvador',
   'DATA'),
  ('_tcl_data\\msgs\\ar_in.msg',
   'C:\\Python313\\tcl\\tcl8.6\\msgs\\ar_in.msg',
   'DATA'),
  ('_tcl_data\\tzdata\\Pacific\\Kiritimati',
   'C:\\Python313\\tcl\\tcl8.6\\tzdata\\Pacific\\Kiritimati',
   'DATA'),
  ('_tcl_data\\tzdata\\Europe\\Andorra',
   'C:\\Python313\\tcl\\tcl8.6\\tzdata\\Europe\\Andorra',
   'DATA'),
  ('_tcl_data\\tzdata\\America\\Curacao',
   'C:\\Python313\\tcl\\tcl8.6\\tzdata\\America\\Curacao',
   'DATA'),
  ('_tcl_data\\msgs\\hi.msg',
   'C:\\Python313\\tcl\\tcl8.6\\msgs\\hi.msg',
   'DATA'),
  ('_tcl_data\\msgs\\fa.msg',
   'C:\\Python313\\tcl\\tcl8.6\\msgs\\fa.msg',
   'DATA'),
  ('_tcl_data\\tzdata\\America\\Sitka',
   'C:\\Python313\\tcl\\tcl8.6\\tzdata\\America\\Sitka',
   'DATA'),
  ('_tcl_data\\encoding\\gb2312.enc',
   'C:\\Python313\\tcl\\tcl8.6\\encoding\\gb2312.enc',
   'DATA'),
  ('_tcl_data\\tzdata\\Asia\\Manila',
   'C:\\Python313\\tcl\\tcl8.6\\tzdata\\Asia\\Manila',
   'DATA'),
  ('_tcl_data\\encoding\\cp863.enc',
   'C:\\Python313\\tcl\\tcl8.6\\encoding\\cp863.enc',
   'DATA'),
  ('_tcl_data\\tzdata\\Africa\\Blantyre',
   'C:\\Python313\\tcl\\tcl8.6\\tzdata\\Africa\\Blantyre',
   'DATA'),
  ('_tcl_data\\msgs\\bn.msg',
   'C:\\Python313\\tcl\\tcl8.6\\msgs\\bn.msg',
   'DATA'),
  ('_tcl_data\\msgs\\it_ch.msg',
   'C:\\Python313\\tcl\\tcl8.6\\msgs\\it_ch.msg',
   'DATA'),
  ('_tcl_data\\msgs\\zh_tw.msg',
   'C:\\Python313\\tcl\\tcl8.6\\msgs\\zh_tw.msg',
   'DATA'),
  ('_tcl_data\\tzdata\\Europe\\Dublin',
   'C:\\Python313\\tcl\\tcl8.6\\tzdata\\Europe\\Dublin',
   'DATA'),
  ('_tcl_data\\tzdata\\Africa\\Ceuta',
   'C:\\Python313\\tcl\\tcl8.6\\tzdata\\Africa\\Ceuta',
   'DATA'),
  ('_tk_data\\entry.tcl', 'C:\\Python313\\tcl\\tk8.6\\entry.tcl', 'DATA'),
  ('_tcl_data\\tzdata\\Australia\\Victoria',
   'C:\\Python313\\tcl\\tcl8.6\\tzdata\\Australia\\Victoria',
   'DATA'),
  ('_tcl_data\\msgs\\fr_be.msg',
   'C:\\Python313\\tcl\\tcl8.6\\msgs\\fr_be.msg',
   'DATA'),
  ('_tcl_data\\tzdata\\Asia\\Vladivostok',
   'C:\\Python313\\tcl\\tcl8.6\\tzdata\\Asia\\Vladivostok',
   'DATA'),
  ('_tcl_data\\tzdata\\America\\Porto_Velho',
   'C:\\Python313\\tcl\\tcl8.6\\tzdata\\America\\Porto_Velho',
   'DATA'),
  ('_tcl_data\\tzdata\\Asia\\Kathmandu',
   'C:\\Python313\\tcl\\tcl8.6\\tzdata\\Asia\\Kathmandu',
   'DATA'),
  ('_tcl_data\\tzdata\\SystemV\\PST8PDT',
   'C:\\Python313\\tcl\\tcl8.6\\tzdata\\SystemV\\PST8PDT',
   'DATA'),
  ('_tcl_data\\tzdata\\Asia\\Macau',
   'C:\\Python313\\tcl\\tcl8.6\\tzdata\\Asia\\Macau',
   'DATA'),
  ('_tcl_data\\tzdata\\Asia\\Qyzylorda',
   'C:\\Python313\\tcl\\tcl8.6\\tzdata\\Asia\\Qyzylorda',
   'DATA'),
  ('_tcl_data\\tzdata\\Asia\\Sakhalin',
   'C:\\Python313\\tcl\\tcl8.6\\tzdata\\Asia\\Sakhalin',
   'DATA'),
  ('_tcl_data\\tzdata\\America\\Jamaica',
   'C:\\Python313\\tcl\\tcl8.6\\tzdata\\America\\Jamaica',
   'DATA'),
  ('_tk_data\\msgs\\de.msg', 'C:\\Python313\\tcl\\tk8.6\\msgs\\de.msg', 'DATA'),
  ('_tcl_data\\tzdata\\Greenwich',
   'C:\\Python313\\tcl\\tcl8.6\\tzdata\\Greenwich',
   'DATA'),
  ('_tcl_data\\encoding\\cp932.enc',
   'C:\\Python313\\tcl\\tcl8.6\\encoding\\cp932.enc',
   'DATA'),
  ('_tcl_data\\tzdata\\Africa\\Asmara',
   'C:\\Python313\\tcl\\tcl8.6\\tzdata\\Africa\\Asmara',
   'DATA'),
  ('_tk_data\\text.tcl', 'C:\\Python313\\tcl\\tk8.6\\text.tcl', 'DATA'),
  ('_tcl_data\\tzdata\\Asia\\Famagusta',
   'C:\\Python313\\tcl\\tcl8.6\\tzdata\\Asia\\Famagusta',
   'DATA'),
  ('_tcl_data\\tzdata\\America\\Guayaquil',
   'C:\\Python313\\tcl\\tcl8.6\\tzdata\\America\\Guayaquil',
   'DATA'),
  ('_tcl_data\\tzdata\\Europe\\San_Marino',
   'C:\\Python313\\tcl\\tcl8.6\\tzdata\\Europe\\San_Marino',
   'DATA'),
  ('_tcl_data\\tzdata\\Europe\\Amsterdam',
   'C:\\Python313\\tcl\\tcl8.6\\tzdata\\Europe\\Amsterdam',
   'DATA'),
  ('_tk_data\\msgs\\en_gb.msg',
   'C:\\Python313\\tcl\\tk8.6\\msgs\\en_gb.msg',
   'DATA'),
  ('_tcl_data\\tzdata\\Pacific\\Pitcairn',
   'C:\\Python313\\tcl\\tcl8.6\\tzdata\\Pacific\\Pitcairn',
   'DATA'),
  ('_tcl_data\\tzdata\\Etc\\GMT+2',
   'C:\\Python313\\tcl\\tcl8.6\\tzdata\\Etc\\GMT+2',
   'DATA'),
  ('_tcl_data\\tzdata\\Asia\\Gaza',
   'C:\\Python313\\tcl\\tcl8.6\\tzdata\\Asia\\Gaza',
   'DATA'),
  ('_tcl_data\\tzdata\\America\\New_York',
   'C:\\Python313\\tcl\\tcl8.6\\tzdata\\America\\New_York',
   'DATA'),
  ('_tcl_data\\tzdata\\US\\Aleutian',
   'C:\\Python313\\tcl\\tcl8.6\\tzdata\\US\\Aleutian',
   'DATA'),
  ('_tcl_data\\tzdata\\America\\Los_Angeles',
   'C:\\Python313\\tcl\\tcl8.6\\tzdata\\America\\Los_Angeles',
   'DATA'),
  ('_tcl_data\\encoding\\cp1255.enc',
   'C:\\Python313\\tcl\\tcl8.6\\encoding\\cp1255.enc',
   'DATA'),
  ('_tk_data\\focus.tcl', 'C:\\Python313\\tcl\\tk8.6\\focus.tcl', 'DATA'),
  ('_tcl_data\\tzdata\\Mexico\\BajaSur',
   'C:\\Python313\\tcl\\tcl8.6\\tzdata\\Mexico\\BajaSur',
   'DATA'),
  ('_tcl_data\\msgs\\ga_ie.msg',
   'C:\\Python313\\tcl\\tcl8.6\\msgs\\ga_ie.msg',
   'DATA'),
  ('_tcl_data\\tzdata\\America\\Virgin',
   'C:\\Python313\\tcl\\tcl8.6\\tzdata\\America\\Virgin',
   'DATA'),
  ('_tcl_data\\tzdata\\Europe\\Saratov',
   'C:\\Python313\\tcl\\tcl8.6\\tzdata\\Europe\\Saratov',
   'DATA'),
  ('_tcl_data\\msgs\\mk.msg',
   'C:\\Python313\\tcl\\tcl8.6\\msgs\\mk.msg',
   'DATA'),
  ('_tcl_data\\tzdata\\Pacific\\Chuuk',
   'C:\\Python313\\tcl\\tcl8.6\\tzdata\\Pacific\\Chuuk',
   'DATA'),
  ('_tcl_data\\tzdata\\America\\Araguaina',
   'C:\\Python313\\tcl\\tcl8.6\\tzdata\\America\\Araguaina',
   'DATA'),
  ('_tcl_data\\msgs\\es_cr.msg',
   'C:\\Python313\\tcl\\tcl8.6\\msgs\\es_cr.msg',
   'DATA'),
  ('_tcl_data\\tzdata\\America\\St_Vincent',
   'C:\\Python313\\tcl\\tcl8.6\\tzdata\\America\\St_Vincent',
   'DATA'),
  ('_tcl_data\\msgs\\th.msg',
   'C:\\Python313\\tcl\\tcl8.6\\msgs\\th.msg',
   'DATA'),
  ('_tcl_data\\tzdata\\Israel',
   'C:\\Python313\\tcl\\tcl8.6\\tzdata\\Israel',
   'DATA'),
  ('_tcl_data\\tzdata\\Asia\\Chita',
   'C:\\Python313\\tcl\\tcl8.6\\tzdata\\Asia\\Chita',
   'DATA'),
  ('_tcl_data\\tzdata\\Africa\\Lagos',
   'C:\\Python313\\tcl\\tcl8.6\\tzdata\\Africa\\Lagos',
   'DATA'),
  ('_tcl_data\\tzdata\\Asia\\Brunei',
   'C:\\Python313\\tcl\\tcl8.6\\tzdata\\Asia\\Brunei',
   'DATA'),
  ('_tcl_data\\tzdata\\America\\Indiana\\Tell_City',
   'C:\\Python313\\tcl\\tcl8.6\\tzdata\\America\\Indiana\\Tell_City',
   'DATA'),
  ('_tk_data\\images\\pwrdLogo200.gif',
   'C:\\Python313\\tcl\\tk8.6\\images\\pwrdLogo200.gif',
   'DATA'),
  ('_tcl_data\\encoding\\macThai.enc',
   'C:\\Python313\\tcl\\tcl8.6\\encoding\\macThai.enc',
   'DATA'),
  ('_tcl_data\\tzdata\\America\\Metlakatla',
   'C:\\Python313\\tcl\\tcl8.6\\tzdata\\America\\Metlakatla',
   'DATA'),
  ('_tcl_data\\tzdata\\Asia\\Tbilisi',
   'C:\\Python313\\tcl\\tcl8.6\\tzdata\\Asia\\Tbilisi',
   'DATA'),
  ('_tcl_data\\msgs\\de.msg',
   'C:\\Python313\\tcl\\tcl8.6\\msgs\\de.msg',
   'DATA'),
  ('_tcl_data\\msgs\\es_ec.msg',
   'C:\\Python313\\tcl\\tcl8.6\\msgs\\es_ec.msg',
   'DATA'),
  ('_tcl_data\\encoding\\macRoman.enc',
   'C:\\Python313\\tcl\\tcl8.6\\encoding\\macRoman.enc',
   'DATA'),
  ('_tcl_data\\tzdata\\GB-Eire',
   'C:\\Python313\\tcl\\tcl8.6\\tzdata\\GB-Eire',
   'DATA'),
  ('_tcl_data\\tzdata\\Australia\\LHI',
   'C:\\Python313\\tcl\\tcl8.6\\tzdata\\Australia\\LHI',
   'DATA'),
  ('_tcl_data\\tzdata\\America\\Kralendijk',
   'C:\\Python313\\tcl\\tcl8.6\\tzdata\\America\\Kralendijk',
   'DATA'),
  ('_tcl_data\\encoding\\macIceland.enc',
   'C:\\Python313\\tcl\\tcl8.6\\encoding\\macIceland.enc',
   'DATA'),
  ('_tcl_data\\tzdata\\US\\Eastern',
   'C:\\Python313\\tcl\\tcl8.6\\tzdata\\US\\Eastern',
   'DATA'),
  ('_tcl_data\\tzdata\\America\\Managua',
   'C:\\Python313\\tcl\\tcl8.6\\tzdata\\America\\Managua',
   'DATA'),
  ('_tcl_data\\tzdata\\Kwajalein',
   'C:\\Python313\\tcl\\tcl8.6\\tzdata\\Kwajalein',
   'DATA'),
  ('_tcl_data\\tzdata\\Australia\\Perth',
   'C:\\Python313\\tcl\\tcl8.6\\tzdata\\Australia\\Perth',
   'DATA'),
  ('_tcl_data\\encoding\\cp437.enc',
   'C:\\Python313\\tcl\\tcl8.6\\encoding\\cp437.enc',
   'DATA'),
  ('_tcl_data\\tzdata\\Navajo',
   'C:\\Python313\\tcl\\tcl8.6\\tzdata\\Navajo',
   'DATA'),
  ('_tcl_data\\tzdata\\EET', 'C:\\Python313\\tcl\\tcl8.6\\tzdata\\EET', 'DATA'),
  ('_tcl_data\\tzdata\\Asia\\Kashgar',
   'C:\\Python313\\tcl\\tcl8.6\\tzdata\\Asia\\Kashgar',
   'DATA'),
  ('_tcl_data\\tzdata\\SystemV\\AST4',
   'C:\\Python313\\tcl\\tcl8.6\\tzdata\\SystemV\\AST4',
   'DATA'),
  ('_tcl_data\\msgs\\sq.msg',
   'C:\\Python313\\tcl\\tcl8.6\\msgs\\sq.msg',
   'DATA'),
  ('_tcl_data\\tzdata\\Asia\\Jayapura',
   'C:\\Python313\\tcl\\tcl8.6\\tzdata\\Asia\\Jayapura',
   'DATA'),
  ('_tcl_data\\tzdata\\Africa\\Conakry',
   'C:\\Python313\\tcl\\tcl8.6\\tzdata\\Africa\\Conakry',
   'DATA'),
  ('_tcl_data\\tzdata\\Europe\\Zurich',
   'C:\\Python313\\tcl\\tcl8.6\\tzdata\\Europe\\Zurich',
   'DATA'),
  ('_tcl_data\\msgs\\ms_my.msg',
   'C:\\Python313\\tcl\\tcl8.6\\msgs\\ms_my.msg',
   'DATA'),
  ('_tk_data\\ttk\\aquaTheme.tcl',
   'C:\\Python313\\tcl\\tk8.6\\ttk\\aquaTheme.tcl',
   'DATA'),
  ('_tcl_data\\tzdata\\America\\Sao_Paulo',
   'C:\\Python313\\tcl\\tcl8.6\\tzdata\\America\\Sao_Paulo',
   'DATA'),
  ('_tcl_data\\tzdata\\Pacific\\Auckland',
   'C:\\Python313\\tcl\\tcl8.6\\tzdata\\Pacific\\Auckland',
   'DATA'),
  ('_tcl_data\\msgs\\cs.msg',
   'C:\\Python313\\tcl\\tcl8.6\\msgs\\cs.msg',
   'DATA'),
  ('_tcl_data\\tzdata\\Africa\\Nouakchott',
   'C:\\Python313\\tcl\\tcl8.6\\tzdata\\Africa\\Nouakchott',
   'DATA'),
  ('_tcl_data\\msgs\\sw.msg',
   'C:\\Python313\\tcl\\tcl8.6\\msgs\\sw.msg',
   'DATA'),
  ('_tcl_data\\tzdata\\America\\Goose_Bay',
   'C:\\Python313\\tcl\\tcl8.6\\tzdata\\America\\Goose_Bay',
   'DATA'),
  ('_tcl_data\\tzdata\\America\\St_Barthelemy',
   'C:\\Python313\\tcl\\tcl8.6\\tzdata\\America\\St_Barthelemy',
   'DATA'),
  ('_tcl_data\\tzdata\\Africa\\Accra',
   'C:\\Python313\\tcl\\tcl8.6\\tzdata\\Africa\\Accra',
   'DATA'),
  ('_tcl_data\\tzdata\\America\\Chicago',
   'C:\\Python313\\tcl\\tcl8.6\\tzdata\\America\\Chicago',
   'DATA'),
  ('_tcl_data\\tclIndex', 'C:\\Python313\\tcl\\tcl8.6\\tclIndex', 'DATA'),
  ('_tk_data\\icons.tcl', 'C:\\Python313\\tcl\\tk8.6\\icons.tcl', 'DATA'),
  ('_tcl_data\\tzdata\\America\\Yakutat',
   'C:\\Python313\\tcl\\tcl8.6\\tzdata\\America\\Yakutat',
   'DATA'),
  ('_tcl_data\\tzdata\\America\\Miquelon',
   'C:\\Python313\\tcl\\tcl8.6\\tzdata\\America\\Miquelon',
   'DATA'),
  ('_tcl_data\\msgs\\es_cl.msg',
   'C:\\Python313\\tcl\\tcl8.6\\msgs\\es_cl.msg',
   'DATA'),
  ('_tk_data\\images\\tai-ku.gif',
   'C:\\Python313\\tcl\\tk8.6\\images\\tai-ku.gif',
   'DATA'),
  ('_tcl_data\\encoding\\cp865.enc',
   'C:\\Python313\\tcl\\tcl8.6\\encoding\\cp865.enc',
   'DATA'),
  ('_tcl_data\\tzdata\\Europe\\Monaco',
   'C:\\Python313\\tcl\\tcl8.6\\tzdata\\Europe\\Monaco',
   'DATA'),
  ('_tcl_data\\tzdata\\America\\Port-au-Prince',
   'C:\\Python313\\tcl\\tcl8.6\\tzdata\\America\\Port-au-Prince',
   'DATA'),
  ('_tcl_data\\msgs\\en_za.msg',
   'C:\\Python313\\tcl\\tcl8.6\\msgs\\en_za.msg',
   'DATA'),
  ('_tcl_data\\tzdata\\America\\Whitehorse',
   'C:\\Python313\\tcl\\tcl8.6\\tzdata\\America\\Whitehorse',
   'DATA'),
  ('_tcl_data\\msgs\\es_do.msg',
   'C:\\Python313\\tcl\\tcl8.6\\msgs\\es_do.msg',
   'DATA'),
  ('_tcl_data\\tzdata\\Asia\\Harbin',
   'C:\\Python313\\tcl\\tcl8.6\\tzdata\\Asia\\Harbin',
   'DATA'),
  ('_tcl_data\\tzdata\\Etc\\GMT+0',
   'C:\\Python313\\tcl\\tcl8.6\\tzdata\\Etc\\GMT+0',
   'DATA'),
  ('_tcl_data\\tzdata\\Africa\\Addis_Ababa',
   'C:\\Python313\\tcl\\tcl8.6\\tzdata\\Africa\\Addis_Ababa',
   'DATA'),
  ('_tcl_data\\tzdata\\America\\Indiana\\Marengo',
   'C:\\Python313\\tcl\\tcl8.6\\tzdata\\America\\Indiana\\Marengo',
   'DATA'),
  ('_tcl_data\\tzdata\\America\\Louisville',
   'C:\\Python313\\tcl\\tcl8.6\\tzdata\\America\\Louisville',
   'DATA'),
  ('_tcl_data\\msgs\\sl.msg',
   'C:\\Python313\\tcl\\tcl8.6\\msgs\\sl.msg',
   'DATA'),
  ('_tcl_data\\encoding\\cp775.enc',
   'C:\\Python313\\tcl\\tcl8.6\\encoding\\cp775.enc',
   'DATA'),
  ('_tk_data\\msgs\\en.msg', 'C:\\Python313\\tcl\\tk8.6\\msgs\\en.msg', 'DATA'),
  ('_tcl_data\\encoding\\euc-jp.enc',
   'C:\\Python313\\tcl\\tcl8.6\\encoding\\euc-jp.enc',
   'DATA'),
  ('_tk_data\\ttk\\spinbox.tcl',
   'C:\\Python313\\tcl\\tk8.6\\ttk\\spinbox.tcl',
   'DATA'),
  ('_tcl_data\\tzdata\\Europe\\Ljubljana',
   'C:\\Python313\\tcl\\tcl8.6\\tzdata\\Europe\\Ljubljana',
   'DATA'),
  ('_tcl_data\\tzdata\\Europe\\Bucharest',
   'C:\\Python313\\tcl\\tcl8.6\\tzdata\\Europe\\Bucharest',
   'DATA'),
  ('_tcl_data\\tzdata\\Asia\\Kuala_Lumpur',
   'C:\\Python313\\tcl\\tcl8.6\\tzdata\\Asia\\Kuala_Lumpur',
   'DATA'),
  ('_tcl_data\\tzdata\\Etc\\GMT-9',
   'C:\\Python313\\tcl\\tcl8.6\\tzdata\\Etc\\GMT-9',
   'DATA'),
  ('_tcl_data\\tzdata\\Europe\\Belfast',
   'C:\\Python313\\tcl\\tcl8.6\\tzdata\\Europe\\Belfast',
   'DATA'),
  ('_tcl_data\\tzdata\\Africa\\Freetown',
   'C:\\Python313\\tcl\\tcl8.6\\tzdata\\Africa\\Freetown',
   'DATA'),
  ('_tcl_data\\tzdata\\Jamaica',
   'C:\\Python313\\tcl\\tcl8.6\\tzdata\\Jamaica',
   'DATA'),
  ('_tcl_data\\tzdata\\Pacific\\Ponape',
   'C:\\Python313\\tcl\\tcl8.6\\tzdata\\Pacific\\Ponape',
   'DATA'),
  ('_tcl_data\\tzdata\\Asia\\Kuwait',
   'C:\\Python313\\tcl\\tcl8.6\\tzdata\\Asia\\Kuwait',
   'DATA'),
  ('_tk_data\\ttk\\entry.tcl',
   'C:\\Python313\\tcl\\tk8.6\\ttk\\entry.tcl',
   'DATA'),
  ('_tcl_data\\tzdata\\Asia\\Taipei',
   'C:\\Python313\\tcl\\tcl8.6\\tzdata\\Asia\\Taipei',
   'DATA'),
  ('_tcl_data\\tzdata\\America\\Monterrey',
   'C:\\Python313\\tcl\\tcl8.6\\tzdata\\America\\Monterrey',
   'DATA'),
  ('_tcl_data\\tzdata\\Asia\\Ashkhabad',
   'C:\\Python313\\tcl\\tcl8.6\\tzdata\\Asia\\Ashkhabad',
   'DATA'),
  ('_tcl_data\\tzdata\\America\\Argentina\\Buenos_Aires',
   'C:\\Python313\\tcl\\tcl8.6\\tzdata\\America\\Argentina\\Buenos_Aires',
   'DATA'),
  ('_tcl_data\\tzdata\\Antarctica\\McMurdo',
   'C:\\Python313\\tcl\\tcl8.6\\tzdata\\Antarctica\\McMurdo',
   'DATA'),
  ('_tk_data\\ttk\\scale.tcl',
   'C:\\Python313\\tcl\\tk8.6\\ttk\\scale.tcl',
   'DATA'),
  ('_tk_data\\ttk\\combobox.tcl',
   'C:\\Python313\\tcl\\tk8.6\\ttk\\combobox.tcl',
   'DATA'),
  ('_tk_data\\ttk\\defaults.tcl',
   'C:\\Python313\\tcl\\tk8.6\\ttk\\defaults.tcl',
   'DATA'),
  ('_tcl_data\\tzdata\\Europe\\Kyiv',
   'C:\\Python313\\tcl\\tcl8.6\\tzdata\\Europe\\Kyiv',
   'DATA'),
  ('_tcl_data\\tzdata\\Asia\\Ust-Nera',
   'C:\\Python313\\tcl\\tcl8.6\\tzdata\\Asia\\Ust-Nera',
   'DATA'),
  ('_tcl_data\\tzdata\\Asia\\Yakutsk',
   'C:\\Python313\\tcl\\tcl8.6\\tzdata\\Asia\\Yakutsk',
   'DATA'),
  ('_tcl_data\\tzdata\\America\\Cancun',
   'C:\\Python313\\tcl\\tcl8.6\\tzdata\\America\\Cancun',
   'DATA'),
  ('_tcl_data\\tzdata\\Asia\\Ho_Chi_Minh',
   'C:\\Python313\\tcl\\tcl8.6\\tzdata\\Asia\\Ho_Chi_Minh',
   'DATA'),
  ('_tcl_data\\tzdata\\Etc\\GMT+6',
   'C:\\Python313\\tcl\\tcl8.6\\tzdata\\Etc\\GMT+6',
   'DATA'),
  ('_tcl_data\\tzdata\\America\\Argentina\\Jujuy',
   'C:\\Python313\\tcl\\tcl8.6\\tzdata\\America\\Argentina\\Jujuy',
   'DATA'),
  ('_tcl_data\\tzdata\\Antarctica\\Casey',
   'C:\\Python313\\tcl\\tcl8.6\\tzdata\\Antarctica\\Casey',
   'DATA'),
  ('_tcl_data\\tzdata\\America\\Caracas',
   'C:\\Python313\\tcl\\tcl8.6\\tzdata\\America\\Caracas',
   'DATA'),
  ('_tk_data\\obsolete.tcl', 'C:\\Python313\\tcl\\tk8.6\\obsolete.tcl', 'DATA'),
  ('_tcl_data\\tzdata\\GB', 'C:\\Python313\\tcl\\tcl8.6\\tzdata\\GB', 'DATA'),
  ('_tcl_data\\tzdata\\Asia\\Dubai',
   'C:\\Python313\\tcl\\tcl8.6\\tzdata\\Asia\\Dubai',
   'DATA'),
  ('_tcl_data\\tzdata\\America\\Argentina\\Catamarca',
   'C:\\Python313\\tcl\\tcl8.6\\tzdata\\America\\Argentina\\Catamarca',
   'DATA'),
  ('_tcl_data\\tzdata\\Asia\\Nicosia',
   'C:\\Python313\\tcl\\tcl8.6\\tzdata\\Asia\\Nicosia',
   'DATA'),
  ('_tcl_data\\tzdata\\Asia\\Jakarta',
   'C:\\Python313\\tcl\\tcl8.6\\tzdata\\Asia\\Jakarta',
   'DATA'),
  ('_tcl_data\\tzdata\\Brazil\\Acre',
   'C:\\Python313\\tcl\\tcl8.6\\tzdata\\Brazil\\Acre',
   'DATA'),
  ('_tcl_data\\msgs\\fo_fo.msg',
   'C:\\Python313\\tcl\\tcl8.6\\msgs\\fo_fo.msg',
   'DATA'),
  ('_tcl_data\\tzdata\\Asia\\Irkutsk',
   'C:\\Python313\\tcl\\tcl8.6\\tzdata\\Asia\\Irkutsk',
   'DATA'),
  ('_tcl_data\\tzdata\\Australia\\West',
   'C:\\Python313\\tcl\\tcl8.6\\tzdata\\Australia\\West',
   'DATA'),
  ('_tcl_data\\msgs\\ta_in.msg',
   'C:\\Python313\\tcl\\tcl8.6\\msgs\\ta_in.msg',
   'DATA'),
  ('_tcl_data\\tzdata\\Asia\\Magadan',
   'C:\\Python313\\tcl\\tcl8.6\\tzdata\\Asia\\Magadan',
   'DATA'),
  ('_tcl_data\\tzdata\\GMT', 'C:\\Python313\\tcl\\tcl8.6\\tzdata\\GMT', 'DATA'),
  ('_tcl_data\\tzdata\\America\\Guadeloupe',
   'C:\\Python313\\tcl\\tcl8.6\\tzdata\\America\\Guadeloupe',
   'DATA'),
  ('_tcl_data\\msgs\\es_gt.msg',
   'C:\\Python313\\tcl\\tcl8.6\\msgs\\es_gt.msg',
   'DATA'),
  ('_tcl_data\\msgs\\ar_lb.msg',
   'C:\\Python313\\tcl\\tcl8.6\\msgs\\ar_lb.msg',
   'DATA'),
  ('_tcl_data\\tzdata\\Africa\\Monrovia',
   'C:\\Python313\\tcl\\tcl8.6\\tzdata\\Africa\\Monrovia',
   'DATA'),
  ('_tcl_data\\tzdata\\Asia\\Dili',
   'C:\\Python313\\tcl\\tcl8.6\\tzdata\\Asia\\Dili',
   'DATA'),
  ('_tcl_data\\encoding\\macCentEuro.enc',
   'C:\\Python313\\tcl\\tcl8.6\\encoding\\macCentEuro.enc',
   'DATA'),
  ('_tcl_data\\msgs\\de_at.msg',
   'C:\\Python313\\tcl\\tcl8.6\\msgs\\de_at.msg',
   'DATA'),
  ('_tk_data\\images\\pwrdLogo100.gif',
   'C:\\Python313\\tcl\\tk8.6\\images\\pwrdLogo100.gif',
   'DATA'),
  ('_tcl_data\\tzdata\\Asia\\Qostanay',
   'C:\\Python313\\tcl\\tcl8.6\\tzdata\\Asia\\Qostanay',
   'DATA'),
  ('_tcl_data\\tzdata\\US\\Pacific',
   'C:\\Python313\\tcl\\tcl8.6\\tzdata\\US\\Pacific',
   'DATA'),
  ('_tcl_data\\tzdata\\Africa\\Bamako',
   'C:\\Python313\\tcl\\tcl8.6\\tzdata\\Africa\\Bamako',
   'DATA'),
  ('_tcl_data\\tzdata\\America\\Kentucky\\Monticello',
   'C:\\Python313\\tcl\\tcl8.6\\tzdata\\America\\Kentucky\\Monticello',
   'DATA'),
  ('_tcl_data\\tzdata\\Pacific\\Tahiti',
   'C:\\Python313\\tcl\\tcl8.6\\tzdata\\Pacific\\Tahiti',
   'DATA'),
  ('_tcl_data\\parray.tcl', 'C:\\Python313\\tcl\\tcl8.6\\parray.tcl', 'DATA'),
  ('_tcl_data\\tzdata\\America\\St_Kitts',
   'C:\\Python313\\tcl\\tcl8.6\\tzdata\\America\\St_Kitts',
   'DATA'),
  ('_tcl_data\\tzdata\\Etc\\GMT-11',
   'C:\\Python313\\tcl\\tcl8.6\\tzdata\\Etc\\GMT-11',
   'DATA'),
  ('_tcl_data\\tzdata\\Asia\\Thimbu',
   'C:\\Python313\\tcl\\tcl8.6\\tzdata\\Asia\\Thimbu',
   'DATA'),
  ('_tcl_data\\tzdata\\Pacific\\Niue',
   'C:\\Python313\\tcl\\tcl8.6\\tzdata\\Pacific\\Niue',
   'DATA'),
  ('_tcl_data\\tzdata\\Etc\\GMT-6',
   'C:\\Python313\\tcl\\tcl8.6\\tzdata\\Etc\\GMT-6',
   'DATA'),
  ('_tcl_data\\tzdata\\Asia\\Bangkok',
   'C:\\Python313\\tcl\\tcl8.6\\tzdata\\Asia\\Bangkok',
   'DATA'),
  ('_tcl_data\\tzdata\\Asia\\Khandyga',
   'C:\\Python313\\tcl\\tcl8.6\\tzdata\\Asia\\Khandyga',
   'DATA'),
  ('_tcl_data\\tzdata\\America\\Bogota',
   'C:\\Python313\\tcl\\tcl8.6\\tzdata\\America\\Bogota',
   'DATA'),
  ('_tcl_data\\tzdata\\Europe\\Vienna',
   'C:\\Python313\\tcl\\tcl8.6\\tzdata\\Europe\\Vienna',
   'DATA'),
  ('_tcl_data\\tzdata\\Asia\\Aden',
   'C:\\Python313\\tcl\\tcl8.6\\tzdata\\Asia\\Aden',
   'DATA'),
  ('_tcl_data\\tzdata\\Atlantic\\Canary',
   'C:\\Python313\\tcl\\tcl8.6\\tzdata\\Atlantic\\Canary',
   'DATA'),
  ('_tcl_data\\tzdata\\Asia\\Hovd',
   'C:\\Python313\\tcl\\tcl8.6\\tzdata\\Asia\\Hovd',
   'DATA'),
  ('_tcl_data\\encoding\\cp855.enc',
   'C:\\Python313\\tcl\\tcl8.6\\encoding\\cp855.enc',
   'DATA'),
  ('_tcl_data\\tzdata\\Indian\\Antananarivo',
   'C:\\Python313\\tcl\\tcl8.6\\tzdata\\Indian\\Antananarivo',
   'DATA'),
  ('_tcl_data\\tzdata\\America\\Campo_Grande',
   'C:\\Python313\\tcl\\tcl8.6\\tzdata\\America\\Campo_Grande',
   'DATA'),
  ('_tcl_data\\tzdata\\America\\Port_of_Spain',
   'C:\\Python313\\tcl\\tcl8.6\\tzdata\\America\\Port_of_Spain',
   'DATA'),
  ('_tcl_data\\tzdata\\US\\Arizona',
   'C:\\Python313\\tcl\\tcl8.6\\tzdata\\US\\Arizona',
   'DATA'),
  ('_tcl_data\\tzdata\\Pacific\\Pago_Pago',
   'C:\\Python313\\tcl\\tcl8.6\\tzdata\\Pacific\\Pago_Pago',
   'DATA'),
  ('_tcl_data\\tzdata\\America\\North_Dakota\\Beulah',
   'C:\\Python313\\tcl\\tcl8.6\\tzdata\\America\\North_Dakota\\Beulah',
   'DATA'),
  ('_tcl_data\\tzdata\\Asia\\Damascus',
   'C:\\Python313\\tcl\\tcl8.6\\tzdata\\Asia\\Damascus',
   'DATA'),
  ('_tcl_data\\encoding\\cp949.enc',
   'C:\\Python313\\tcl\\tcl8.6\\encoding\\cp949.enc',
   'DATA'),
  ('_tcl_data\\msgs\\fi.msg',
   'C:\\Python313\\tcl\\tcl8.6\\msgs\\fi.msg',
   'DATA'),
  ('_tcl_data\\tzdata\\Pacific\\Gambier',
   'C:\\Python313\\tcl\\tcl8.6\\tzdata\\Pacific\\Gambier',
   'DATA'),
  ('_tcl_data\\msgs\\ga.msg',
   'C:\\Python313\\tcl\\tcl8.6\\msgs\\ga.msg',
   'DATA'),
  ('_tcl_data\\msgs\\hi_in.msg',
   'C:\\Python313\\tcl\\tcl8.6\\msgs\\hi_in.msg',
   'DATA'),
  ('_tcl_data\\tzdata\\Europe\\Kaliningrad',
   'C:\\Python313\\tcl\\tcl8.6\\tzdata\\Europe\\Kaliningrad',
   'DATA'),
  ('_tcl_data\\tzdata\\Asia\\Atyrau',
   'C:\\Python313\\tcl\\tcl8.6\\tzdata\\Asia\\Atyrau',
   'DATA'),
  ('_tk_data\\images\\logo.eps',
   'C:\\Python313\\tcl\\tk8.6\\images\\logo.eps',
   'DATA'),
  ('_tcl_data\\tzdata\\Etc\\GMT+12',
   'C:\\Python313\\tcl\\tcl8.6\\tzdata\\Etc\\GMT+12',
   'DATA'),
  ('_tcl_data\\tzdata\\Antarctica\\Davis',
   'C:\\Python313\\tcl\\tcl8.6\\tzdata\\Antarctica\\Davis',
   'DATA'),
  ('_tcl_data\\tzdata\\Asia\\Aqtobe',
   'C:\\Python313\\tcl\\tcl8.6\\tzdata\\Asia\\Aqtobe',
   'DATA'),
  ('_tcl_data\\tzdata\\ROK', 'C:\\Python313\\tcl\\tcl8.6\\tzdata\\ROK', 'DATA'),
  ('_tcl_data\\tzdata\\GMT-0',
   'C:\\Python313\\tcl\\tcl8.6\\tzdata\\GMT-0',
   'DATA'),
  ('_tcl_data\\tzdata\\Mexico\\BajaNorte',
   'C:\\Python313\\tcl\\tcl8.6\\tzdata\\Mexico\\BajaNorte',
   'DATA'),
  ('_tcl_data\\tzdata\\Australia\\Eucla',
   'C:\\Python313\\tcl\\tcl8.6\\tzdata\\Australia\\Eucla',
   'DATA'),
  ('_tcl_data\\tzdata\\Pacific\\Nauru',
   'C:\\Python313\\tcl\\tcl8.6\\tzdata\\Pacific\\Nauru',
   'DATA'),
  ('_tcl_data\\tzdata\\Etc\\GMT+11',
   'C:\\Python313\\tcl\\tcl8.6\\tzdata\\Etc\\GMT+11',
   'DATA'),
  ('_tcl_data\\encoding\\iso8859-15.enc',
   'C:\\Python313\\tcl\\tcl8.6\\encoding\\iso8859-15.enc',
   'DATA'),
  ('_tcl_data\\msgs\\fr.msg',
   'C:\\Python313\\tcl\\tcl8.6\\msgs\\fr.msg',
   'DATA'),
  ('_tk_data\\button.tcl', 'C:\\Python313\\tcl\\tk8.6\\button.tcl', 'DATA'),
  ('_tcl_data\\encoding\\gb12345.enc',
   'C:\\Python313\\tcl\\tcl8.6\\encoding\\gb12345.enc',
   'DATA'),
  ('_tcl_data\\tzdata\\Egypt',
   'C:\\Python313\\tcl\\tcl8.6\\tzdata\\Egypt',
   'DATA'),
  ('_tcl_data\\tzdata\\America\\Chihuahua',
   'C:\\Python313\\tcl\\tcl8.6\\tzdata\\America\\Chihuahua',
   'DATA'),
  ('_tcl_data\\encoding\\iso8859-10.enc',
   'C:\\Python313\\tcl\\tcl8.6\\encoding\\iso8859-10.enc',
   'DATA'),
  ('_tk_data\\msgs\\cs.msg', 'C:\\Python313\\tcl\\tk8.6\\msgs\\cs.msg', 'DATA'),
  ('_tcl_data\\tzdata\\Asia\\Chongqing',
   'C:\\Python313\\tcl\\tcl8.6\\tzdata\\Asia\\Chongqing',
   'DATA'),
  ('_tcl_data\\tzdata\\Africa\\Abidjan',
   'C:\\Python313\\tcl\\tcl8.6\\tzdata\\Africa\\Abidjan',
   'DATA'),
  ('_tcl_data\\encoding\\cp869.enc',
   'C:\\Python313\\tcl\\tcl8.6\\encoding\\cp869.enc',
   'DATA'),
  ('_tcl_data\\tzdata\\Europe\\Jersey',
   'C:\\Python313\\tcl\\tcl8.6\\tzdata\\Europe\\Jersey',
   'DATA'),
  ('_tcl_data\\tzdata\\America\\Cordoba',
   'C:\\Python313\\tcl\\tcl8.6\\tzdata\\America\\Cordoba',
   'DATA'),
  ('_tcl_data\\tzdata\\America\\Nuuk',
   'C:\\Python313\\tcl\\tcl8.6\\tzdata\\America\\Nuuk',
   'DATA'),
  ('_tcl_data\\msgs\\is.msg',
   'C:\\Python313\\tcl\\tcl8.6\\msgs\\is.msg',
   'DATA'),
  ('_tcl_data\\msgs\\te.msg',
   'C:\\Python313\\tcl\\tcl8.6\\msgs\\te.msg',
   'DATA'),
  ('_tcl_data\\msgs\\af_za.msg',
   'C:\\Python313\\tcl\\tcl8.6\\msgs\\af_za.msg',
   'DATA'),
  ('_tcl_data\\tzdata\\SystemV\\CST6',
   'C:\\Python313\\tcl\\tcl8.6\\tzdata\\SystemV\\CST6',
   'DATA'),
  ('_tcl_data\\msgs\\zh_sg.msg',
   'C:\\Python313\\tcl\\tcl8.6\\msgs\\zh_sg.msg',
   'DATA'),
  ('_tcl_data\\tzdata\\Australia\\Lord_Howe',
   'C:\\Python313\\tcl\\tcl8.6\\tzdata\\Australia\\Lord_Howe',
   'DATA'),
  ('_tcl_data\\msgs\\ca.msg',
   'C:\\Python313\\tcl\\tcl8.6\\msgs\\ca.msg',
   'DATA'),
  ('_tcl_data\\tzdata\\Asia\\Baku',
   'C:\\Python313\\tcl\\tcl8.6\\tzdata\\Asia\\Baku',
   'DATA'),
  ('_tcl_data\\msgs\\et.msg',
   'C:\\Python313\\tcl\\tcl8.6\\msgs\\et.msg',
   'DATA'),
  ('_tcl_data\\encoding\\koi8-u.enc',
   'C:\\Python313\\tcl\\tcl8.6\\encoding\\koi8-u.enc',
   'DATA'),
  ('_tk_data\\images\\pwrdLogo175.gif',
   'C:\\Python313\\tcl\\tk8.6\\images\\pwrdLogo175.gif',
   'DATA'),
  ('_tcl_data\\tzdata\\ROC', 'C:\\Python313\\tcl\\tcl8.6\\tzdata\\ROC', 'DATA'),
  ('_tcl_data\\tzdata\\America\\Mexico_City',
   'C:\\Python313\\tcl\\tcl8.6\\tzdata\\America\\Mexico_City',
   'DATA'),
  ('_tcl_data\\tzdata\\Asia\\Makassar',
   'C:\\Python313\\tcl\\tcl8.6\\tzdata\\Asia\\Makassar',
   'DATA'),
  ('_tcl_data\\tzdata\\Asia\\Chungking',
   'C:\\Python313\\tcl\\tcl8.6\\tzdata\\Asia\\Chungking',
   'DATA'),
  ('_tcl_data\\tzdata\\Pacific\\Kanton',
   'C:\\Python313\\tcl\\tcl8.6\\tzdata\\Pacific\\Kanton',
   'DATA'),
  ('_tcl_data\\msgs\\ar_jo.msg',
   'C:\\Python313\\tcl\\tcl8.6\\msgs\\ar_jo.msg',
   'DATA'),
  ('_tcl_data\\tzdata\\CET', 'C:\\Python313\\tcl\\tcl8.6\\tzdata\\CET', 'DATA'),
  ('_tcl_data\\tzdata\\America\\Indiana\\Vevay',
   'C:\\Python313\\tcl\\tcl8.6\\tzdata\\America\\Indiana\\Vevay',
   'DATA'),
  ('_tcl_data\\tzdata\\America\\Indiana\\Knox',
   'C:\\Python313\\tcl\\tcl8.6\\tzdata\\America\\Indiana\\Knox',
   'DATA'),
  ('_tcl_data\\tzdata\\HST', 'C:\\Python313\\tcl\\tcl8.6\\tzdata\\HST', 'DATA'),
  ('_tcl_data\\tzdata\\Asia\\Hebron',
   'C:\\Python313\\tcl\\tcl8.6\\tzdata\\Asia\\Hebron',
   'DATA'),
  ('_tcl_data\\tzdata\\Europe\\Minsk',
   'C:\\Python313\\tcl\\tcl8.6\\tzdata\\Europe\\Minsk',
   'DATA'),
  ('_tcl_data\\encoding\\cp850.enc',
   'C:\\Python313\\tcl\\tcl8.6\\encoding\\cp850.enc',
   'DATA'),
  ('_tcl_data\\encoding\\jis0201.enc',
   'C:\\Python313\\tcl\\tcl8.6\\encoding\\jis0201.enc',
   'DATA'),
  ('_tcl_data\\tzdata\\SystemV\\HST10',
   'C:\\Python313\\tcl\\tcl8.6\\tzdata\\SystemV\\HST10',
   'DATA'),
  ('_tk_data\\ttk\\fonts.tcl',
   'C:\\Python313\\tcl\\tk8.6\\ttk\\fonts.tcl',
   'DATA'),
  ('_tcl_data\\tzdata\\Europe\\Simferopol',
   'C:\\Python313\\tcl\\tcl8.6\\tzdata\\Europe\\Simferopol',
   'DATA'),
  ('_tk_data\\images\\logoLarge.gif',
   'C:\\Python313\\tcl\\tk8.6\\images\\logoLarge.gif',
   'DATA'),
  ('_tcl_data\\msgs\\en_nz.msg',
   'C:\\Python313\\tcl\\tcl8.6\\msgs\\en_nz.msg',
   'DATA'),
  ('_tcl_data\\tzdata\\Europe\\Helsinki',
   'C:\\Python313\\tcl\\tcl8.6\\tzdata\\Europe\\Helsinki',
   'DATA'),
  ('_tk_data\\msgs\\eo.msg', 'C:\\Python313\\tcl\\tk8.6\\msgs\\eo.msg', 'DATA'),
  ('_tcl_data\\tzdata\\Atlantic\\Faeroe',
   'C:\\Python313\\tcl\\tcl8.6\\tzdata\\Atlantic\\Faeroe',
   'DATA'),
  ('_tcl_data\\tzdata\\America\\Grenada',
   'C:\\Python313\\tcl\\tcl8.6\\tzdata\\America\\Grenada',
   'DATA'),
  ('_tcl_data\\tzdata\\America\\Regina',
   'C:\\Python313\\tcl\\tcl8.6\\tzdata\\America\\Regina',
   'DATA'),
  ('_tcl_data\\history.tcl', 'C:\\Python313\\tcl\\tcl8.6\\history.tcl', 'DATA'),
  ('_tcl_data\\tzdata\\Australia\\Broken_Hill',
   'C:\\Python313\\tcl\\tcl8.6\\tzdata\\Australia\\Broken_Hill',
   'DATA'),
  ('_tcl_data\\msgs\\gl_es.msg',
   'C:\\Python313\\tcl\\tcl8.6\\msgs\\gl_es.msg',
   'DATA'),
  ('_tcl_data\\msgs\\bn_in.msg',
   'C:\\Python313\\tcl\\tcl8.6\\msgs\\bn_in.msg',
   'DATA'),
  ('_tcl_data\\msgs\\vi.msg',
   'C:\\Python313\\tcl\\tcl8.6\\msgs\\vi.msg',
   'DATA'),
  ('_tcl_data\\tzdata\\UTC', 'C:\\Python313\\tcl\\tcl8.6\\tzdata\\UTC', 'DATA'),
  ('_tcl_data\\tzdata\\Africa\\Tunis',
   'C:\\Python313\\tcl\\tcl8.6\\tzdata\\Africa\\Tunis',
   'DATA'),
  ('_tcl_data\\msgs\\en_ph.msg',
   'C:\\Python313\\tcl\\tcl8.6\\msgs\\en_ph.msg',
   'DATA'),
  ('_tcl_data\\http1.0\\pkgIndex.tcl',
   'C:\\Python313\\tcl\\tcl8.6\\http1.0\\pkgIndex.tcl',
   'DATA'),
  ('_tk_data\\msgbox.tcl', 'C:\\Python313\\tcl\\tk8.6\\msgbox.tcl', 'DATA'),
  ('_tk_data\\tkfbox.tcl', 'C:\\Python313\\tcl\\tk8.6\\tkfbox.tcl', 'DATA'),
  ('_tcl_data\\encoding\\iso8859-8.enc',
   'C:\\Python313\\tcl\\tcl8.6\\encoding\\iso8859-8.enc',
   'DATA'),
  ('_tcl_data\\encoding\\macCroatian.enc',
   'C:\\Python313\\tcl\\tcl8.6\\encoding\\macCroatian.enc',
   'DATA'),
  ('_tcl_data\\tzdata\\America\\Toronto',
   'C:\\Python313\\tcl\\tcl8.6\\tzdata\\America\\Toronto',
   'DATA'),
  ('_tcl_data\\msgs\\el.msg',
   'C:\\Python313\\tcl\\tcl8.6\\msgs\\el.msg',
   'DATA'),
  ('_tcl_data\\tzdata\\Cuba',
   'C:\\Python313\\tcl\\tcl8.6\\tzdata\\Cuba',
   'DATA'),
  ('_tcl_data\\encoding\\macGreek.enc',
   'C:\\Python313\\tcl\\tcl8.6\\encoding\\macGreek.enc',
   'DATA'),
  ('_tcl_data\\tzdata\\Asia\\Bishkek',
   'C:\\Python313\\tcl\\tcl8.6\\tzdata\\Asia\\Bishkek',
   'DATA'),
  ('_tcl_data\\tzdata\\Chile\\Continental',
   'C:\\Python313\\tcl\\tcl8.6\\tzdata\\Chile\\Continental',
   'DATA'),
  ('_tcl_data\\tzdata\\Asia\\Barnaul',
   'C:\\Python313\\tcl\\tcl8.6\\tzdata\\Asia\\Barnaul',
   'DATA'),
  ('_tcl_data\\tzdata\\America\\Thunder_Bay',
   'C:\\Python313\\tcl\\tcl8.6\\tzdata\\America\\Thunder_Bay',
   'DATA'),
  ('_tcl_data\\tzdata\\Etc\\GMT+8',
   'C:\\Python313\\tcl\\tcl8.6\\tzdata\\Etc\\GMT+8',
   'DATA'),
  ('_tcl_data\\tzdata\\Europe\\Ulyanovsk',
   'C:\\Python313\\tcl\\tcl8.6\\tzdata\\Europe\\Ulyanovsk',
   'DATA'),
  ('_tcl_data\\encoding\\shiftjis.enc',
   'C:\\Python313\\tcl\\tcl8.6\\encoding\\shiftjis.enc',
   'DATA'),
  ('_tcl_data\\tzdata\\America\\North_Dakota\\New_Salem',
   'C:\\Python313\\tcl\\tcl8.6\\tzdata\\America\\North_Dakota\\New_Salem',
   'DATA'),
  ('_tcl_data\\msgs\\en_au.msg',
   'C:\\Python313\\tcl\\tcl8.6\\msgs\\en_au.msg',
   'DATA'),
  ('_tcl_data\\tzdata\\Antarctica\\Macquarie',
   'C:\\Python313\\tcl\\tcl8.6\\tzdata\\Antarctica\\Macquarie',
   'DATA'),
  ('_tcl_data\\tzdata\\Pacific\\Honolulu',
   'C:\\Python313\\tcl\\tcl8.6\\tzdata\\Pacific\\Honolulu',
   'DATA'),
  ('_tcl_data\\safe.tcl', 'C:\\Python313\\tcl\\tcl8.6\\safe.tcl', 'DATA'),
  ('_tcl_data\\tzdata\\Asia\\Tehran',
   'C:\\Python313\\tcl\\tcl8.6\\tzdata\\Asia\\Tehran',
   'DATA'),
  ('_tcl_data\\tzdata\\America\\Punta_Arenas',
   'C:\\Python313\\tcl\\tcl8.6\\tzdata\\America\\Punta_Arenas',
   'DATA'),
  ('_tcl_data\\encoding\\iso2022-jp.enc',
   'C:\\Python313\\tcl\\tcl8.6\\encoding\\iso2022-jp.enc',
   'DATA'),
  ('_tcl_data\\tzdata\\Etc\\GMT-14',
   'C:\\Python313\\tcl\\tcl8.6\\tzdata\\Etc\\GMT-14',
   'DATA'),
  ('_tcl_data\\opt0.4\\pkgIndex.tcl',
   'C:\\Python313\\tcl\\tcl8.6\\opt0.4\\pkgIndex.tcl',
   'DATA'),
  ('_tcl_data\\tzdata\\Canada\\Pacific',
   'C:\\Python313\\tcl\\tcl8.6\\tzdata\\Canada\\Pacific',
   'DATA'),
  ('_tcl_data\\tzdata\\Asia\\Dushanbe',
   'C:\\Python313\\tcl\\tcl8.6\\tzdata\\Asia\\Dushanbe',
   'DATA'),
  ('_tcl_data\\tzdata\\Indian\\Reunion',
   'C:\\Python313\\tcl\\tcl8.6\\tzdata\\Indian\\Reunion',
   'DATA'),
  ('_tcl_data\\tzdata\\Africa\\Brazzaville',
   'C:\\Python313\\tcl\\tcl8.6\\tzdata\\Africa\\Brazzaville',
   'DATA'),
  ('_tcl_data\\tzdata\\America\\Halifax',
   'C:\\Python313\\tcl\\tcl8.6\\tzdata\\America\\Halifax',
   'DATA'),
  ('_tcl_data\\msgs\\bg.msg',
   'C:\\Python313\\tcl\\tcl8.6\\msgs\\bg.msg',
   'DATA'),
  ('_tcl_data\\tzdata\\Australia\\NSW',
   'C:\\Python313\\tcl\\tcl8.6\\tzdata\\Australia\\NSW',
   'DATA'),
  ('_tcl_data\\msgs\\ko_kr.msg',
   'C:\\Python313\\tcl\\tcl8.6\\msgs\\ko_kr.msg',
   'DATA'),
  ('_tcl_data\\tzdata\\Asia\\Kamchatka',
   'C:\\Python313\\tcl\\tcl8.6\\tzdata\\Asia\\Kamchatka',
   'DATA'),
  ('_tcl_data\\encoding\\cp1258.enc',
   'C:\\Python313\\tcl\\tcl8.6\\encoding\\cp1258.enc',
   'DATA'),
  ('_tcl_data\\tzdata\\Australia\\Lindeman',
   'C:\\Python313\\tcl\\tcl8.6\\tzdata\\Australia\\Lindeman',
   'DATA'),
  ('_tcl_data\\tzdata\\Pacific\\Saipan',
   'C:\\Python313\\tcl\\tcl8.6\\tzdata\\Pacific\\Saipan',
   'DATA'),
  ('_tcl_data\\msgs\\es_pr.msg',
   'C:\\Python313\\tcl\\tcl8.6\\msgs\\es_pr.msg',
   'DATA'),
  ('_tcl_data\\tzdata\\MST', 'C:\\Python313\\tcl\\tcl8.6\\tzdata\\MST', 'DATA'),
  ('_tcl_data\\tzdata\\Europe\\Guernsey',
   'C:\\Python313\\tcl\\tcl8.6\\tzdata\\Europe\\Guernsey',
   'DATA'),
  ('_tcl_data\\tzdata\\Asia\\Baghdad',
   'C:\\Python313\\tcl\\tcl8.6\\tzdata\\Asia\\Baghdad',
   'DATA'),
  ('_tcl_data\\tzdata\\Australia\\Hobart',
   'C:\\Python313\\tcl\\tcl8.6\\tzdata\\Australia\\Hobart',
   'DATA'),
  ('_tcl_data\\tzdata\\US\\Mountain',
   'C:\\Python313\\tcl\\tcl8.6\\tzdata\\US\\Mountain',
   'DATA'),
  ('_tcl_data\\tzdata\\America\\Bahia_Banderas',
   'C:\\Python313\\tcl\\tcl8.6\\tzdata\\America\\Bahia_Banderas',
   'DATA'),
  ('_tk_data\\ttk\\menubutton.tcl',
   'C:\\Python313\\tcl\\tk8.6\\ttk\\menubutton.tcl',
   'DATA'),
  ('_tcl_data\\tzdata\\Pacific\\Chatham',
   'C:\\Python313\\tcl\\tcl8.6\\tzdata\\Pacific\\Chatham',
   'DATA'),
  ('_tcl_data\\encoding\\jis0212.enc',
   'C:\\Python313\\tcl\\tcl8.6\\encoding\\jis0212.enc',
   'DATA'),
  ('_tcl_data\\tzdata\\MET', 'C:\\Python313\\tcl\\tcl8.6\\tzdata\\MET', 'DATA'),
  ('_tcl_data\\tzdata\\Etc\\Zulu',
   'C:\\Python313\\tcl\\tcl8.6\\tzdata\\Etc\\Zulu',
   'DATA'),
  ('_tcl_data\\tzdata\\America\\Fortaleza',
   'C:\\Python313\\tcl\\tcl8.6\\tzdata\\America\\Fortaleza',
   'DATA'),
  ('_tcl_data\\tzdata\\America\\Indianapolis',
   'C:\\Python313\\tcl\\tcl8.6\\tzdata\\America\\Indianapolis',
   'DATA'),
  ('_tcl_data\\encoding\\euc-cn.enc',
   'C:\\Python313\\tcl\\tcl8.6\\encoding\\euc-cn.enc',
   'DATA'),
  ('_tcl_data\\encoding\\iso8859-1.enc',
   'C:\\Python313\\tcl\\tcl8.6\\encoding\\iso8859-1.enc',
   'DATA'),
  ('_tk_data\\msgs\\ru.msg', 'C:\\Python313\\tcl\\tk8.6\\msgs\\ru.msg', 'DATA'),
  ('_tcl_data\\tzdata\\America\\Scoresbysund',
   'C:\\Python313\\tcl\\tcl8.6\\tzdata\\America\\Scoresbysund',
   'DATA'),
  ('_tcl_data\\msgs\\ro.msg',
   'C:\\Python313\\tcl\\tcl8.6\\msgs\\ro.msg',
   'DATA'),
  ('_tcl_data\\msgs\\uk.msg',
   'C:\\Python313\\tcl\\tcl8.6\\msgs\\uk.msg',
   'DATA'),
  ('_tcl_data\\tzdata\\America\\Dominica',
   'C:\\Python313\\tcl\\tcl8.6\\tzdata\\America\\Dominica',
   'DATA'),
  ('_tcl_data\\tzdata\\Australia\\North',
   'C:\\Python313\\tcl\\tcl8.6\\tzdata\\Australia\\North',
   'DATA'),
  ('_tcl_data\\tzdata\\Europe\\Rome',
   'C:\\Python313\\tcl\\tcl8.6\\tzdata\\Europe\\Rome',
   'DATA'),
  ('_tcl_data\\tzdata\\Pacific\\Wallis',
   'C:\\Python313\\tcl\\tcl8.6\\tzdata\\Pacific\\Wallis',
   'DATA'),
  ('_tcl_data\\tzdata\\America\\Tijuana',
   'C:\\Python313\\tcl\\tcl8.6\\tzdata\\America\\Tijuana',
   'DATA'),
  ('_tcl_data\\tzdata\\America\\Eirunepe',
   'C:\\Python313\\tcl\\tcl8.6\\tzdata\\America\\Eirunepe',
   'DATA'),
  ('_tk_data\\optMenu.tcl', 'C:\\Python313\\tcl\\tk8.6\\optMenu.tcl', 'DATA'),
  ('_tcl_data\\tzdata\\America\\Fort_Nelson',
   'C:\\Python313\\tcl\\tcl8.6\\tzdata\\America\\Fort_Nelson',
   'DATA'),
  ('_tcl_data\\tzdata\\Asia\\Kuching',
   'C:\\Python313\\tcl\\tcl8.6\\tzdata\\Asia\\Kuching',
   'DATA'),
  ('_tcl_data\\encoding\\koi8-r.enc',
   'C:\\Python313\\tcl\\tcl8.6\\encoding\\koi8-r.enc',
   'DATA'),
  ('_tcl_data\\tzdata\\Africa\\Sao_Tome',
   'C:\\Python313\\tcl\\tcl8.6\\tzdata\\Africa\\Sao_Tome',
   'DATA'),
  ('_tk_data\\tclIndex', 'C:\\Python313\\tcl\\tk8.6\\tclIndex', 'DATA'),
  ('_tcl_data\\tzdata\\Atlantic\\South_Georgia',
   'C:\\Python313\\tcl\\tcl8.6\\tzdata\\Atlantic\\South_Georgia',
   'DATA'),
  ('_tk_data\\images\\pwrdLogo.eps',
   'C:\\Python313\\tcl\\tk8.6\\images\\pwrdLogo.eps',
   'DATA'),
  ('_tcl_data\\tzdata\\Etc\\GMT+3',
   'C:\\Python313\\tcl\\tcl8.6\\tzdata\\Etc\\GMT+3',
   'DATA'),
  ('_tcl_data\\tzdata\\US\\Michigan',
   'C:\\Python313\\tcl\\tcl8.6\\tzdata\\US\\Michigan',
   'DATA'),
  ('_tcl_data\\clock.tcl', 'C:\\Python313\\tcl\\tcl8.6\\clock.tcl', 'DATA'),
  ('_tcl_data\\msgs\\be.msg',
   'C:\\Python313\\tcl\\tcl8.6\\msgs\\be.msg',
   'DATA'),
  ('_tcl_data\\tzdata\\Pacific\\Noumea',
   'C:\\Python313\\tcl\\tcl8.6\\tzdata\\Pacific\\Noumea',
   'DATA'),
  ('_tcl_data\\tzdata\\Europe\\Malta',
   'C:\\Python313\\tcl\\tcl8.6\\tzdata\\Europe\\Malta',
   'DATA'),
  ('_tcl_data\\tzdata\\Mexico\\General',
   'C:\\Python313\\tcl\\tcl8.6\\tzdata\\Mexico\\General',
   'DATA'),
  ('_tcl_data\\tzdata\\Etc\\UCT',
   'C:\\Python313\\tcl\\tcl8.6\\tzdata\\Etc\\UCT',
   'DATA'),
  ('_tcl_data\\tzdata\\Canada\\Mountain',
   'C:\\Python313\\tcl\\tcl8.6\\tzdata\\Canada\\Mountain',
   'DATA'),
  ('_tcl_data\\tzdata\\Africa\\Harare',
   'C:\\Python313\\tcl\\tcl8.6\\tzdata\\Africa\\Harare',
   'DATA'),
  ('_tcl_data\\tzdata\\Europe\\Chisinau',
   'C:\\Python313\\tcl\\tcl8.6\\tzdata\\Europe\\Chisinau',
   'DATA'),
  ('_tcl_data\\tzdata\\America\\Antigua',
   'C:\\Python313\\tcl\\tcl8.6\\tzdata\\America\\Antigua',
   'DATA'),
  ('_tcl_data\\encoding\\euc-kr.enc',
   'C:\\Python313\\tcl\\tcl8.6\\encoding\\euc-kr.enc',
   'DATA'),
  ('_tcl_data\\tzdata\\America\\Bahia',
   'C:\\Python313\\tcl\\tcl8.6\\tzdata\\America\\Bahia',
   'DATA'),
  ('_tcl_data\\tzdata\\Africa\\Bujumbura',
   'C:\\Python313\\tcl\\tcl8.6\\tzdata\\Africa\\Bujumbura',
   'DATA'),
  ('_tcl_data\\tzdata\\Africa\\Khartoum',
   'C:\\Python313\\tcl\\tcl8.6\\tzdata\\Africa\\Khartoum',
   'DATA'),
  ('_tcl_data\\msgs\\kl.msg',
   'C:\\Python313\\tcl\\tcl8.6\\msgs\\kl.msg',
   'DATA'),
  ('_tcl_data\\tzdata\\Australia\\South',
   'C:\\Python313\\tcl\\tcl8.6\\tzdata\\Australia\\South',
   'DATA'),
  ('_tcl_data\\msgs\\gv.msg',
   'C:\\Python313\\tcl\\tcl8.6\\msgs\\gv.msg',
   'DATA'),
  ('_tcl_data\\tzdata\\Australia\\Canberra',
   'C:\\Python313\\tcl\\tcl8.6\\tzdata\\Australia\\Canberra',
   'DATA'),
  ('_tk_data\\ttk\\winTheme.tcl',
   'C:\\Python313\\tcl\\tk8.6\\ttk\\winTheme.tcl',
   'DATA'),
  ('_tcl_data\\tzdata\\Asia\\Vientiane',
   'C:\\Python313\\tcl\\tcl8.6\\tzdata\\Asia\\Vientiane',
   'DATA'),
  ('_tcl_data\\tzdata\\Asia\\Choibalsan',
   'C:\\Python313\\tcl\\tcl8.6\\tzdata\\Asia\\Choibalsan',
   'DATA'),
  ('_tcl_data\\tzdata\\Brazil\\West',
   'C:\\Python313\\tcl\\tcl8.6\\tzdata\\Brazil\\West',
   'DATA'),
  ('_tcl_data\\encoding\\iso8859-9.enc',
   'C:\\Python313\\tcl\\tcl8.6\\encoding\\iso8859-9.enc',
   'DATA'),
  ('_tcl_data\\encoding\\macUkraine.enc',
   'C:\\Python313\\tcl\\tcl8.6\\encoding\\macUkraine.enc',
   'DATA'),
  ('_tcl_data\\encoding\\ksc5601.enc',
   'C:\\Python313\\tcl\\tcl8.6\\encoding\\ksc5601.enc',
   'DATA'),
  ('_tcl_data\\tzdata\\America\\Argentina\\Ushuaia',
   'C:\\Python313\\tcl\\tcl8.6\\tzdata\\America\\Argentina\\Ushuaia',
   'DATA'),
  ('_tcl_data\\tzdata\\Europe\\Uzhgorod',
   'C:\\Python313\\tcl\\tcl8.6\\tzdata\\Europe\\Uzhgorod',
   'DATA'),
  ('_tcl_data\\tzdata\\Europe\\Skopje',
   'C:\\Python313\\tcl\\tcl8.6\\tzdata\\Europe\\Skopje',
   'DATA'),
  ('_tcl_data\\tzdata\\America\\Atikokan',
   'C:\\Python313\\tcl\\tcl8.6\\tzdata\\America\\Atikokan',
   'DATA'),
  ('_tcl_data\\tzdata\\GMT0',
   'C:\\Python313\\tcl\\tcl8.6\\tzdata\\GMT0',
   'DATA'),
  ('_tcl_data\\msgs\\fr_ca.msg',
   'C:\\Python313\\tcl\\tcl8.6\\msgs\\fr_ca.msg',
   'DATA'),
  ('_tcl_data\\encoding\\gb2312-raw.enc',
   'C:\\Python313\\tcl\\tcl8.6\\encoding\\gb2312-raw.enc',
   'DATA'),
  ('_tcl_data\\encoding\\iso8859-11.enc',
   'C:\\Python313\\tcl\\tcl8.6\\encoding\\iso8859-11.enc',
   'DATA'),
  ('_tk_data\\scale.tcl', 'C:\\Python313\\tcl\\tk8.6\\scale.tcl', 'DATA'),
  ('_tk_data\\images\\logo100.gif',
   'C:\\Python313\\tcl\\tk8.6\\images\\logo100.gif',
   'DATA'),
  ('_tcl_data\\tzdata\\Pacific\\Pohnpei',
   'C:\\Python313\\tcl\\tcl8.6\\tzdata\\Pacific\\Pohnpei',
   'DATA'),
  ('_tcl_data\\tzdata\\Asia\\Yerevan',
   'C:\\Python313\\tcl\\tcl8.6\\tzdata\\Asia\\Yerevan',
   'DATA'),
  ('_tcl_data\\msgs\\af.msg',
   'C:\\Python313\\tcl\\tcl8.6\\msgs\\af.msg',
   'DATA'),
  ('_tcl_data\\tzdata\\Asia\\Istanbul',
   'C:\\Python313\\tcl\\tcl8.6\\tzdata\\Asia\\Istanbul',
   'DATA'),
  ('_tcl_data\\msgs\\nl_be.msg',
   'C:\\Python313\\tcl\\tcl8.6\\msgs\\nl_be.msg',
   'DATA'),
  ('_tcl_data\\tzdata\\Canada\\Yukon',
   'C:\\Python313\\tcl\\tcl8.6\\tzdata\\Canada\\Yukon',
   'DATA'),
  ('_tcl_data\\tzdata\\SystemV\\CST6CDT',
   'C:\\Python313\\tcl\\tcl8.6\\tzdata\\SystemV\\CST6CDT',
   'DATA'),
  ('_tcl_data\\tzdata\\Asia\\Singapore',
   'C:\\Python313\\tcl\\tcl8.6\\tzdata\\Asia\\Singapore',
   'DATA'),
  ('_tcl_data\\tzdata\\Pacific\\Guadalcanal',
   'C:\\Python313\\tcl\\tcl8.6\\tzdata\\Pacific\\Guadalcanal',
   'DATA'),
  ('_tcl_data\\tzdata\\Africa\\Mbabane',
   'C:\\Python313\\tcl\\tcl8.6\\tzdata\\Africa\\Mbabane',
   'DATA'),
  ('_tcl_data\\tzdata\\America\\Puerto_Rico',
   'C:\\Python313\\tcl\\tcl8.6\\tzdata\\America\\Puerto_Rico',
   'DATA'),
  ('_tcl_data\\http1.0\\http.tcl',
   'C:\\Python313\\tcl\\tcl8.6\\http1.0\\http.tcl',
   'DATA'),
  ('_tcl_data\\msgs\\id.msg',
   'C:\\Python313\\tcl\\tcl8.6\\msgs\\id.msg',
   'DATA'),
  ('_tcl_data\\tzdata\\Atlantic\\Stanley',
   'C:\\Python313\\tcl\\tcl8.6\\tzdata\\Atlantic\\Stanley',
   'DATA'),
  ('_tcl_data\\encoding\\cp1256.enc',
   'C:\\Python313\\tcl\\tcl8.6\\encoding\\cp1256.enc',
   'DATA'),
  ('_tcl_data\\tzdata\\Africa\\Banjul',
   'C:\\Python313\\tcl\\tcl8.6\\tzdata\\Africa\\Banjul',
   'DATA'),
  ('_tk_data\\ttk\\scrollbar.tcl',
   'C:\\Python313\\tcl\\tk8.6\\ttk\\scrollbar.tcl',
   'DATA'),
  ('_tcl_data\\tzdata\\Asia\\Calcutta',
   'C:\\Python313\\tcl\\tcl8.6\\tzdata\\Asia\\Calcutta',
   'DATA'),
  ('_tcl_data\\tzdata\\Europe\\Busingen',
   'C:\\Python313\\tcl\\tcl8.6\\tzdata\\Europe\\Busingen',
   'DATA'),
  ('_tcl_data\\encoding\\koi8-ru.enc',
   'C:\\Python313\\tcl\\tcl8.6\\encoding\\koi8-ru.enc',
   'DATA'),
  ('_tcl_data\\init.tcl', 'C:\\Python313\\tcl\\tcl8.6\\init.tcl', 'DATA'),
  ('_tcl_data\\tzdata\\Japan',
   'C:\\Python313\\tcl\\tcl8.6\\tzdata\\Japan',
   'DATA'),
  ('_tk_data\\ttk\\treeview.tcl',
   'C:\\Python313\\tcl\\tk8.6\\ttk\\treeview.tcl',
   'DATA'),
  ('_tcl_data\\tzdata\\SystemV\\PST8',
   'C:\\Python313\\tcl\\tcl8.6\\tzdata\\SystemV\\PST8',
   'DATA'),
  ('_tcl_data\\msgs\\mt.msg',
   'C:\\Python313\\tcl\\tcl8.6\\msgs\\mt.msg',
   'DATA'),
  ('_tcl_data\\msgs\\ar.msg',
   'C:\\Python313\\tcl\\tcl8.6\\msgs\\ar.msg',
   'DATA'),
  ('_tcl_data\\tzdata\\America\\Santarem',
   'C:\\Python313\\tcl\\tcl8.6\\tzdata\\America\\Santarem',
   'DATA'),
  ('_tcl_data\\msgs\\tr.msg',
   'C:\\Python313\\tcl\\tcl8.6\\msgs\\tr.msg',
   'DATA'),
  ('_tcl_data\\msgs\\es_sv.msg',
   'C:\\Python313\\tcl\\tcl8.6\\msgs\\es_sv.msg',
   'DATA'),
  ('_tk_data\\ttk\\classicTheme.tcl',
   'C:\\Python313\\tcl\\tk8.6\\ttk\\classicTheme.tcl',
   'DATA'),
  ('_tcl_data\\msgs\\ko.msg',
   'C:\\Python313\\tcl\\tcl8.6\\msgs\\ko.msg',
   'DATA'),
  ('_tcl_data\\tzdata\\Europe\\Sarajevo',
   'C:\\Python313\\tcl\\tcl8.6\\tzdata\\Europe\\Sarajevo',
   'DATA'),
  ('_tcl_data\\tzdata\\Asia\\Srednekolymsk',
   'C:\\Python313\\tcl\\tcl8.6\\tzdata\\Asia\\Srednekolymsk',
   'DATA'),
  ('_tcl_data\\msgs\\es_py.msg',
   'C:\\Python313\\tcl\\tcl8.6\\msgs\\es_py.msg',
   'DATA'),
  ('_tcl_data\\tzdata\\America\\Rosario',
   'C:\\Python313\\tcl\\tcl8.6\\tzdata\\America\\Rosario',
   'DATA'),
  ('_tcl_data\\tzdata\\Asia\\Pyongyang',
   'C:\\Python313\\tcl\\tcl8.6\\tzdata\\Asia\\Pyongyang',
   'DATA'),
  ('_tcl_data\\tzdata\\Africa\\Niamey',
   'C:\\Python313\\tcl\\tcl8.6\\tzdata\\Africa\\Niamey',
   'DATA'),
  ('_tcl_data\\tzdata\\Antarctica\\Troll',
   'C:\\Python313\\tcl\\tcl8.6\\tzdata\\Antarctica\\Troll',
   'DATA'),
  ('_tk_data\\ttk\\altTheme.tcl',
   'C:\\Python313\\tcl\\tk8.6\\ttk\\altTheme.tcl',
   'DATA'),
  ('_tcl_data\\tzdata\\Etc\\GMT-2',
   'C:\\Python313\\tcl\\tcl8.6\\tzdata\\Etc\\GMT-2',
   'DATA'),
  ('_tcl_data\\tzdata\\Asia\\Novokuznetsk',
   'C:\\Python313\\tcl\\tcl8.6\\tzdata\\Asia\\Novokuznetsk',
   'DATA'),
  ('_tcl_data\\tzdata\\America\\Maceio',
   'C:\\Python313\\tcl\\tcl8.6\\tzdata\\America\\Maceio',
   'DATA'),
  ('_tcl_data\\tzdata\\America\\North_Dakota\\Center',
   'C:\\Python313\\tcl\\tcl8.6\\tzdata\\America\\North_Dakota\\Center',
   'DATA'),
  ('_tcl_data\\tzdata\\Europe\\Luxembourg',
   'C:\\Python313\\tcl\\tcl8.6\\tzdata\\Europe\\Luxembourg',
   'DATA'),
  ('_tk_data\\msgs\\fi.msg', 'C:\\Python313\\tcl\\tk8.6\\msgs\\fi.msg', 'DATA'),
  ('_tcl_data\\tzdata\\Etc\\GMT-1',
   'C:\\Python313\\tcl\\tcl8.6\\tzdata\\Etc\\GMT-1',
   'DATA'),
  ('_tk_data\\comdlg.tcl', 'C:\\Python313\\tcl\\tk8.6\\comdlg.tcl', 'DATA'),
  ('_tcl_data\\tzdata\\PST8PDT',
   'C:\\Python313\\tcl\\tcl8.6\\tzdata\\PST8PDT',
   'DATA'),
  ('_tcl_data\\msgs\\en_ca.msg',
   'C:\\Python313\\tcl\\tcl8.6\\msgs\\en_ca.msg',
   'DATA'),
  ('_tcl_data\\msgs\\ja.msg',
   'C:\\Python313\\tcl\\tcl8.6\\msgs\\ja.msg',
   'DATA'),
  ('_tcl_data\\tzdata\\SystemV\\EST5',
   'C:\\Python313\\tcl\\tcl8.6\\tzdata\\SystemV\\EST5',
   'DATA'),
  ('_tcl_data\\tzdata\\Europe\\Astrakhan',
   'C:\\Python313\\tcl\\tcl8.6\\tzdata\\Europe\\Astrakhan',
   'DATA'),
  ('_tcl_data\\tzdata\\Asia\\Ujung_Pandang',
   'C:\\Python313\\tcl\\tcl8.6\\tzdata\\Asia\\Ujung_Pandang',
   'DATA'),
  ('_tcl_data\\tzdata\\Brazil\\DeNoronha',
   'C:\\Python313\\tcl\\tcl8.6\\tzdata\\Brazil\\DeNoronha',
   'DATA'),
  ('_tcl_data\\msgs\\sv.msg',
   'C:\\Python313\\tcl\\tcl8.6\\msgs\\sv.msg',
   'DATA'),
  ('_tk_data\\images\\pwrdLogo75.gif',
   'C:\\Python313\\tcl\\tk8.6\\images\\pwrdLogo75.gif',
   'DATA'),
  ('_tcl_data\\tzdata\\Australia\\Darwin',
   'C:\\Python313\\tcl\\tcl8.6\\tzdata\\Australia\\Darwin',
   'DATA'),
  ('_tcl_data\\tzdata\\Eire',
   'C:\\Python313\\tcl\\tcl8.6\\tzdata\\Eire',
   'DATA'),
  ('_tcl_data\\tzdata\\Indian\\Maldives',
   'C:\\Python313\\tcl\\tcl8.6\\tzdata\\Indian\\Maldives',
   'DATA'),
  ('_tcl_data\\tzdata\\Indian\\Mauritius',
   'C:\\Python313\\tcl\\tcl8.6\\tzdata\\Indian\\Mauritius',
   'DATA'),
  ('_tcl_data\\tzdata\\Antarctica\\Mawson',
   'C:\\Python313\\tcl\\tcl8.6\\tzdata\\Antarctica\\Mawson',
   'DATA'),
  ('_tcl_data\\tzdata\\Pacific\\Tongatapu',
   'C:\\Python313\\tcl\\tcl8.6\\tzdata\\Pacific\\Tongatapu',
   'DATA'),
  ('_tk_data\\spinbox.tcl', 'C:\\Python313\\tcl\\tk8.6\\spinbox.tcl', 'DATA'),
  ('_tcl_data\\tzdata\\America\\Tortola',
   'C:\\Python313\\tcl\\tcl8.6\\tzdata\\America\\Tortola',
   'DATA'),
  ('_tcl_data\\msgs\\de_be.msg',
   'C:\\Python313\\tcl\\tcl8.6\\msgs\\de_be.msg',
   'DATA'),
  ('_tcl_data\\tzdata\\America\\Lower_Princes',
   'C:\\Python313\\tcl\\tcl8.6\\tzdata\\America\\Lower_Princes',
   'DATA'),
  ('_tcl_data\\encoding\\iso8859-6.enc',
   'C:\\Python313\\tcl\\tcl8.6\\encoding\\iso8859-6.enc',
   'DATA'),
  ('_tk_data\\ttk\\sizegrip.tcl',
   'C:\\Python313\\tcl\\tk8.6\\ttk\\sizegrip.tcl',
   'DATA'),
  ('_tcl_data\\tzdata\\Asia\\Thimphu',
   'C:\\Python313\\tcl\\tcl8.6\\tzdata\\Asia\\Thimphu',
   'DATA'),
  ('_tcl_data\\tzdata\\Antarctica\\Vostok',
   'C:\\Python313\\tcl\\tcl8.6\\tzdata\\Antarctica\\Vostok',
   'DATA'),
  ('_tcl_data\\msgs\\fa_ir.msg',
   'C:\\Python313\\tcl\\tcl8.6\\msgs\\fa_ir.msg',
   'DATA'),
  ('_tcl_data\\msgs\\nl.msg',
   'C:\\Python313\\tcl\\tcl8.6\\msgs\\nl.msg',
   'DATA'),
  ('_tk_data\\ttk\\xpTheme.tcl',
   'C:\\Python313\\tcl\\tk8.6\\ttk\\xpTheme.tcl',
   'DATA'),
  ('_tcl_data\\encoding\\iso2022-kr.enc',
   'C:\\Python313\\tcl\\tcl8.6\\encoding\\iso2022-kr.enc',
   'DATA'),
  ('_tcl_data\\tzdata\\Africa\\Malabo',
   'C:\\Python313\\tcl\\tcl8.6\\tzdata\\Africa\\Malabo',
   'DATA'),
  ('_tk_data\\msgs\\da.msg', 'C:\\Python313\\tcl\\tk8.6\\msgs\\da.msg', 'DATA'),
  ('_tcl_data\\tzdata\\Pacific\\Kosrae',
   'C:\\Python313\\tcl\\tcl8.6\\tzdata\\Pacific\\Kosrae',
   'DATA'),
  ('_tcl_data\\tzdata\\America\\Argentina\\ComodRivadavia',
   'C:\\Python313\\tcl\\tcl8.6\\tzdata\\America\\Argentina\\ComodRivadavia',
   'DATA'),
  ('_tcl_data\\tzdata\\Etc\\GMT+10',
   'C:\\Python313\\tcl\\tcl8.6\\tzdata\\Etc\\GMT+10',
   'DATA'),
  ('_tcl_data\\tzdata\\America\\Menominee',
   'C:\\Python313\\tcl\\tcl8.6\\tzdata\\America\\Menominee',
   'DATA'),
  ('_tcl_data\\tzdata\\America\\Argentina\\Tucuman',
   'C:\\Python313\\tcl\\tcl8.6\\tzdata\\America\\Argentina\\Tucuman',
   'DATA'),
  ('_tk_data\\tearoff.tcl', 'C:\\Python313\\tcl\\tk8.6\\tearoff.tcl', 'DATA'),
  ('_tcl_data\\encoding\\macJapan.enc',
   'C:\\Python313\\tcl\\tcl8.6\\encoding\\macJapan.enc',
   'DATA'),
  ('_tcl_data\\tzdata\\America\\Catamarca',
   'C:\\Python313\\tcl\\tcl8.6\\tzdata\\America\\Catamarca',
   'DATA'),
  ('_tcl_data\\tzdata\\Etc\\GMT-5',
   'C:\\Python313\\tcl\\tcl8.6\\tzdata\\Etc\\GMT-5',
   'DATA'),
  ('_tcl_data\\tzdata\\America\\Iqaluit',
   'C:\\Python313\\tcl\\tcl8.6\\tzdata\\America\\Iqaluit',
   'DATA'),
  ('_tcl_data\\tzdata\\America\\Santa_Isabel',
   'C:\\Python313\\tcl\\tcl8.6\\tzdata\\America\\Santa_Isabel',
   'DATA'),
  ('_tk_data\\images\\logoMed.gif',
   'C:\\Python313\\tcl\\tk8.6\\images\\logoMed.gif',
   'DATA'),
  ('_tcl_data\\tzdata\\Pacific\\Marquesas',
   'C:\\Python313\\tcl\\tcl8.6\\tzdata\\Pacific\\Marquesas',
   'DATA'),
  ('_tcl_data\\tzdata\\Pacific\\Midway',
   'C:\\Python313\\tcl\\tcl8.6\\tzdata\\Pacific\\Midway',
   'DATA'),
  ('_tcl_data\\tzdata\\Etc\\GMT+5',
   'C:\\Python313\\tcl\\tcl8.6\\tzdata\\Etc\\GMT+5',
   'DATA'),
  ('_tcl_data\\tzdata\\Etc\\GMT+7',
   'C:\\Python313\\tcl\\tcl8.6\\tzdata\\Etc\\GMT+7',
   'DATA'),
  ('_tcl_data\\tzdata\\America\\Atka',
   'C:\\Python313\\tcl\\tcl8.6\\tzdata\\America\\Atka',
   'DATA'),
  ('_tcl_data\\tzdata\\America\\Phoenix',
   'C:\\Python313\\tcl\\tcl8.6\\tzdata\\America\\Phoenix',
   'DATA'),
  ('_tk_data\\pkgIndex.tcl', 'C:\\Python313\\tcl\\tk8.6\\pkgIndex.tcl', 'DATA'),
  ('_tcl_data\\tzdata\\Africa\\Dakar',
   'C:\\Python313\\tcl\\tcl8.6\\tzdata\\Africa\\Dakar',
   'DATA'),
  ('_tcl_data\\encoding\\jis0208.enc',
   'C:\\Python313\\tcl\\tcl8.6\\encoding\\jis0208.enc',
   'DATA'),
  ('_tcl_data\\tzdata\\America\\St_Johns',
   'C:\\Python313\\tcl\\tcl8.6\\tzdata\\America\\St_Johns',
   'DATA'),
  ('_tk_data\\msgs\\fr.msg', 'C:\\Python313\\tcl\\tk8.6\\msgs\\fr.msg', 'DATA'),
  ('_tcl_data\\tzdata\\America\\Nome',
   'C:\\Python313\\tcl\\tcl8.6\\tzdata\\America\\Nome',
   'DATA'),
  ('_tcl_data\\tzdata\\Europe\\Vilnius',
   'C:\\Python313\\tcl\\tcl8.6\\tzdata\\Europe\\Vilnius',
   'DATA'),
  ('tcl8\\8.4\\platform-1.0.19.tm',
   'C:\\Python313\\tcl\\tcl8\\8.4\\platform-1.0.19.tm',
   'DATA'),
  ('_tcl_data\\tzdata\\GMT+0',
   'C:\\Python313\\tcl\\tcl8.6\\tzdata\\GMT+0',
   'DATA'),
  ('_tcl_data\\tzdata\\America\\Godthab',
   'C:\\Python313\\tcl\\tcl8.6\\tzdata\\America\\Godthab',
   'DATA'),
  ('_tcl_data\\tzdata\\Iran',
   'C:\\Python313\\tcl\\tcl8.6\\tzdata\\Iran',
   'DATA'),
  ('_tcl_data\\tzdata\\Etc\\GMT-13',
   'C:\\Python313\\tcl\\tcl8.6\\tzdata\\Etc\\GMT-13',
   'DATA'),
  ('_tcl_data\\tzdata\\America\\Denver',
   'C:\\Python313\\tcl\\tcl8.6\\tzdata\\America\\Denver',
   'DATA'),
  ('_tk_data\\msgs\\pl.msg', 'C:\\Python313\\tcl\\tk8.6\\msgs\\pl.msg', 'DATA'),
  ('_tk_data\\safetk.tcl', 'C:\\Python313\\tcl\\tk8.6\\safetk.tcl', 'DATA'),
  ('_tcl_data\\tzdata\\NZ', 'C:\\Python313\\tcl\\tcl8.6\\tzdata\\NZ', 'DATA'),
  ('_tcl_data\\tzdata\\Europe\\Tallinn',
   'C:\\Python313\\tcl\\tcl8.6\\tzdata\\Europe\\Tallinn',
   'DATA'),
  ('_tcl_data\\tzdata\\Asia\\Colombo',
   'C:\\Python313\\tcl\\tcl8.6\\tzdata\\Asia\\Colombo',
   'DATA'),
  ('_tcl_data\\tzdata\\America\\Juneau',
   'C:\\Python313\\tcl\\tcl8.6\\tzdata\\America\\Juneau',
   'DATA'),
  ('_tcl_data\\tzdata\\America\\Indiana\\Vincennes',
   'C:\\Python313\\tcl\\tcl8.6\\tzdata\\America\\Indiana\\Vincennes',
   'DATA'),
  ('_tcl_data\\tzdata\\Atlantic\\Faroe',
   'C:\\Python313\\tcl\\tcl8.6\\tzdata\\Atlantic\\Faroe',
   'DATA'),
  ('_tcl_data\\tzdata\\Africa\\Luanda',
   'C:\\Python313\\tcl\\tcl8.6\\tzdata\\Africa\\Luanda',
   'DATA'),
  ('_tcl_data\\msgs\\pt.msg',
   'C:\\Python313\\tcl\\tcl8.6\\msgs\\pt.msg',
   'DATA'),
  ('_tcl_data\\tzdata\\Etc\\GMT+4',
   'C:\\Python313\\tcl\\tcl8.6\\tzdata\\Etc\\GMT+4',
   'DATA'),
  ('_tcl_data\\tzdata\\Etc\\Greenwich',
   'C:\\Python313\\tcl\\tcl8.6\\tzdata\\Etc\\Greenwich',
   'DATA'),
  ('_tcl_data\\tzdata\\Africa\\Bangui',
   'C:\\Python313\\tcl\\tcl8.6\\tzdata\\Africa\\Bangui',
   'DATA'),
  ('_tcl_data\\tzdata\\Asia\\Dhaka',
   'C:\\Python313\\tcl\\tcl8.6\\tzdata\\Asia\\Dhaka',
   'DATA'),
  ('_tk_data\\palette.tcl', 'C:\\Python313\\tcl\\tk8.6\\palette.tcl', 'DATA'),
  ('_tcl_data\\tzdata\\Europe\\Paris',
   'C:\\Python313\\tcl\\tcl8.6\\tzdata\\Europe\\Paris',
   'DATA'),
  ('_tcl_data\\encoding\\big5.enc',
   'C:\\Python313\\tcl\\tcl8.6\\encoding\\big5.enc',
   'DATA'),
  ('_tcl_data\\tzdata\\Europe\\Mariehamn',
   'C:\\Python313\\tcl\\tcl8.6\\tzdata\\Europe\\Mariehamn',
   'DATA'),
  ('_tcl_data\\tzdata\\America\\Resolute',
   'C:\\Python313\\tcl\\tcl8.6\\tzdata\\America\\Resolute',
   'DATA'),
  ('_tcl_data\\encoding\\cp1250.enc',
   'C:\\Python313\\tcl\\tcl8.6\\encoding\\cp1250.enc',
   'DATA'),
  ('_tcl_data\\msgs\\fo.msg',
   'C:\\Python313\\tcl\\tcl8.6\\msgs\\fo.msg',
   'DATA'),
  ('_tcl_data\\msgs\\kl_gl.msg',
   'C:\\Python313\\tcl\\tcl8.6\\msgs\\kl_gl.msg',
   'DATA'),
  ('_tcl_data\\tzdata\\Atlantic\\Cape_Verde',
   'C:\\Python313\\tcl\\tcl8.6\\tzdata\\Atlantic\\Cape_Verde',
   'DATA'),
  ('_tcl_data\\tzdata\\Etc\\GMT-7',
   'C:\\Python313\\tcl\\tcl8.6\\tzdata\\Etc\\GMT-7',
   'DATA'),
  ('_tcl_data\\msgs\\lt.msg',
   'C:\\Python313\\tcl\\tcl8.6\\msgs\\lt.msg',
   'DATA'),
  ('_tcl_data\\tzdata\\EST5EDT',
   'C:\\Python313\\tcl\\tcl8.6\\tzdata\\EST5EDT',
   'DATA'),
  ('_tcl_data\\tzdata\\America\\Lima',
   'C:\\Python313\\tcl\\tcl8.6\\tzdata\\America\\Lima',
   'DATA'),
  ('_tcl_data\\tzdata\\America\\Merida',
   'C:\\Python313\\tcl\\tcl8.6\\tzdata\\America\\Merida',
   'DATA'),
  ('_tcl_data\\tzdata\\America\\Cayenne',
   'C:\\Python313\\tcl\\tcl8.6\\tzdata\\America\\Cayenne',
   'DATA'),
  ('_tcl_data\\tzdata\\Asia\\Bahrain',
   'C:\\Python313\\tcl\\tcl8.6\\tzdata\\Asia\\Bahrain',
   'DATA'),
  ('_tcl_data\\tzdata\\Africa\\Gaborone',
   'C:\\Python313\\tcl\\tcl8.6\\tzdata\\Africa\\Gaborone',
   'DATA'),
  ('_tcl_data\\tzdata\\Asia\\Aqtau',
   'C:\\Python313\\tcl\\tcl8.6\\tzdata\\Asia\\Aqtau',
   'DATA'),
  ('_tcl_data\\tzdata\\Indian\\Cocos',
   'C:\\Python313\\tcl\\tcl8.6\\tzdata\\Indian\\Cocos',
   'DATA'),
  ('_tcl_data\\tzdata\\America\\Anchorage',
   'C:\\Python313\\tcl\\tcl8.6\\tzdata\\America\\Anchorage',
   'DATA'),
  ('_tcl_data\\tzdata\\Etc\\Universal',
   'C:\\Python313\\tcl\\tcl8.6\\tzdata\\Etc\\Universal',
   'DATA'),
  ('_tcl_data\\tzdata\\Indian\\Mahe',
   'C:\\Python313\\tcl\\tcl8.6\\tzdata\\Indian\\Mahe',
   'DATA'),
  ('_tcl_data\\tzdata\\America\\Barbados',
   'C:\\Python313\\tcl\\tcl8.6\\tzdata\\America\\Barbados',
   'DATA'),
  ('_tcl_data\\tzdata\\Etc\\GMT-8',
   'C:\\Python313\\tcl\\tcl8.6\\tzdata\\Etc\\GMT-8',
   'DATA'),
  ('_tcl_data\\tzdata\\MST7MDT',
   'C:\\Python313\\tcl\\tcl8.6\\tzdata\\MST7MDT',
   'DATA'),
  ('_tcl_data\\tzdata\\Indian\\Christmas',
   'C:\\Python313\\tcl\\tcl8.6\\tzdata\\Indian\\Christmas',
   'DATA'),
  ('_tcl_data\\tzdata\\Pacific\\Apia',
   'C:\\Python313\\tcl\\tcl8.6\\tzdata\\Pacific\\Apia',
   'DATA'),
  ('_tcl_data\\tzdata\\UCT', 'C:\\Python313\\tcl\\tcl8.6\\tzdata\\UCT', 'DATA'),
  ('_tk_data\\tk.tcl', 'C:\\Python313\\tcl\\tk8.6\\tk.tcl', 'DATA'),
  ('_tcl_data\\tzdata\\Antarctica\\Palmer',
   'C:\\Python313\\tcl\\tcl8.6\\tzdata\\Antarctica\\Palmer',
   'DATA'),
  ('_tcl_data\\tzdata\\America\\Adak',
   'C:\\Python313\\tcl\\tcl8.6\\tzdata\\America\\Adak',
   'DATA'),
  ('_tcl_data\\tzdata\\Australia\\Brisbane',
   'C:\\Python313\\tcl\\tcl8.6\\tzdata\\Australia\\Brisbane',
   'DATA'),
  ('_tcl_data\\tzdata\\Asia\\Jerusalem',
   'C:\\Python313\\tcl\\tcl8.6\\tzdata\\Asia\\Jerusalem',
   'DATA'),
  ('_tcl_data\\tzdata\\Africa\\Ndjamena',
   'C:\\Python313\\tcl\\tcl8.6\\tzdata\\Africa\\Ndjamena',
   'DATA'),
  ('_tcl_data\\tzdata\\Europe\\Sofia',
   'C:\\Python313\\tcl\\tcl8.6\\tzdata\\Europe\\Sofia',
   'DATA'),
  ('_tk_data\\msgs\\zh_cn.msg',
   'C:\\Python313\\tcl\\tk8.6\\msgs\\zh_cn.msg',
   'DATA'),
  ('_tcl_data\\tzdata\\Australia\\Adelaide',
   'C:\\Python313\\tcl\\tcl8.6\\tzdata\\Australia\\Adelaide',
   'DATA'),
  ('_tcl_data\\tzdata\\America\\Blanc-Sablon',
   'C:\\Python313\\tcl\\tcl8.6\\tzdata\\America\\Blanc-Sablon',
   'DATA'),
  ('_tcl_data\\tzdata\\Africa\\Tripoli',
   'C:\\Python313\\tcl\\tcl8.6\\tzdata\\Africa\\Tripoli',
   'DATA'),
  ('_tcl_data\\msgs\\en_gb.msg',
   'C:\\Python313\\tcl\\tcl8.6\\msgs\\en_gb.msg',
   'DATA'),
  ('_tcl_data\\msgs\\en_zw.msg',
   'C:\\Python313\\tcl\\tcl8.6\\msgs\\en_zw.msg',
   'DATA'),
  ('_tcl_data\\tzdata\\America\\Danmarkshavn',
   'C:\\Python313\\tcl\\tcl8.6\\tzdata\\America\\Danmarkshavn',
   'DATA'),
  ('_tk_data\\msgs\\es.msg', 'C:\\Python313\\tcl\\tk8.6\\msgs\\es.msg', 'DATA'),
  ('_tcl_data\\tzdata\\SystemV\\AST4ADT',
   'C:\\Python313\\tcl\\tcl8.6\\tzdata\\SystemV\\AST4ADT',
   'DATA'),
  ('_tcl_data\\tzdata\\EST', 'C:\\Python313\\tcl\\tcl8.6\\tzdata\\EST', 'DATA'),
  ('_tcl_data\\tzdata\\Europe\\Belgrade',
   'C:\\Python313\\tcl\\tcl8.6\\tzdata\\Europe\\Belgrade',
   'DATA'),
  ('_tcl_data\\tzdata\\America\\Paramaribo',
   'C:\\Python313\\tcl\\tcl8.6\\tzdata\\America\\Paramaribo',
   'DATA'),
  ('_tcl_data\\tzdata\\Turkey',
   'C:\\Python313\\tcl\\tcl8.6\\tzdata\\Turkey',
   'DATA'),
  ('_tcl_data\\tzdata\\Pacific\\Port_Moresby',
   'C:\\Python313\\tcl\\tcl8.6\\tzdata\\Pacific\\Port_Moresby',
   'DATA'),
  ('_tcl_data\\tzdata\\Europe\\Podgorica',
   'C:\\Python313\\tcl\\tcl8.6\\tzdata\\Europe\\Podgorica',
   'DATA'),
  ('_tcl_data\\tzdata\\Atlantic\\Bermuda',
   'C:\\Python313\\tcl\\tcl8.6\\tzdata\\Atlantic\\Bermuda',
   'DATA'),
  ('_tcl_data\\tzdata\\Canada\\Central',
   'C:\\Python313\\tcl\\tcl8.6\\tzdata\\Canada\\Central',
   'DATA'),
  ('_tcl_data\\tzdata\\Atlantic\\St_Helena',
   'C:\\Python313\\tcl\\tcl8.6\\tzdata\\Atlantic\\St_Helena',
   'DATA'),
  ('_tcl_data\\tzdata\\Asia\\Pontianak',
   'C:\\Python313\\tcl\\tcl8.6\\tzdata\\Asia\\Pontianak',
   'DATA'),
  ('_tcl_data\\tzdata\\Africa\\Algiers',
   'C:\\Python313\\tcl\\tcl8.6\\tzdata\\Africa\\Algiers',
   'DATA'),
  ('_tcl_data\\tzdata\\Asia\\Saigon',
   'C:\\Python313\\tcl\\tcl8.6\\tzdata\\Asia\\Saigon',
   'DATA'),
  ('_tcl_data\\tzdata\\Asia\\Tokyo',
   'C:\\Python313\\tcl\\tcl8.6\\tzdata\\Asia\\Tokyo',
   'DATA'),
  ('_tcl_data\\tzdata\\Poland',
   'C:\\Python313\\tcl\\tcl8.6\\tzdata\\Poland',
   'DATA'),
  ('_tk_data\\console.tcl', 'C:\\Python313\\tcl\\tk8.6\\console.tcl', 'DATA'),
  ('_tk_data\\fontchooser.tcl',
   'C:\\Python313\\tcl\\tk8.6\\fontchooser.tcl',
   'DATA'),
  ('tcl8\\8.5\\msgcat-1.6.1.tm',
   'C:\\Python313\\tcl\\tcl8\\8.5\\msgcat-1.6.1.tm',
   'DATA'),
  ('_tcl_data\\tzdata\\Singapore',
   'C:\\Python313\\tcl\\tcl8.6\\tzdata\\Singapore',
   'DATA'),
  ('_tcl_data\\tzdata\\Europe\\Athens',
   'C:\\Python313\\tcl\\tcl8.6\\tzdata\\Europe\\Athens',
   'DATA'),
  ('_tcl_data\\tzdata\\Africa\\Kigali',
   'C:\\Python313\\tcl\\tcl8.6\\tzdata\\Africa\\Kigali',
   'DATA'),
  ('_tcl_data\\msgs\\en_be.msg',
   'C:\\Python313\\tcl\\tcl8.6\\msgs\\en_be.msg',
   'DATA'),
  ('_tcl_data\\tzdata\\Canada\\Saskatchewan',
   'C:\\Python313\\tcl\\tcl8.6\\tzdata\\Canada\\Saskatchewan',
   'DATA'),
  ('_tcl_data\\tzdata\\America\\Mendoza',
   'C:\\Python313\\tcl\\tcl8.6\\tzdata\\America\\Mendoza',
   'DATA'),
  ('_tcl_data\\tzdata\\America\\Martinique',
   'C:\\Python313\\tcl\\tcl8.6\\tzdata\\America\\Martinique',
   'DATA'),
  ('_tcl_data\\opt0.4\\optparse.tcl',
   'C:\\Python313\\tcl\\tcl8.6\\opt0.4\\optparse.tcl',
   'DATA'),
  ('_tcl_data\\tzdata\\Etc\\GMT-4',
   'C:\\Python313\\tcl\\tcl8.6\\tzdata\\Etc\\GMT-4',
   'DATA'),
  ('_tcl_data\\tzdata\\Atlantic\\Madeira',
   'C:\\Python313\\tcl\\tcl8.6\\tzdata\\Atlantic\\Madeira',
   'DATA'),
  ('_tcl_data\\tzdata\\America\\Glace_Bay',
   'C:\\Python313\\tcl\\tcl8.6\\tzdata\\America\\Glace_Bay',
   'DATA'),
  ('_tcl_data\\tzdata\\America\\Boise',
   'C:\\Python313\\tcl\\tcl8.6\\tzdata\\America\\Boise',
   'DATA'),
  ('_tcl_data\\encoding\\cp1251.enc',
   'C:\\Python313\\tcl\\tcl8.6\\encoding\\cp1251.enc',
   'DATA'),
  ('_tcl_data\\tzdata\\America\\Shiprock',
   'C:\\Python313\\tcl\\tcl8.6\\tzdata\\America\\Shiprock',
   'DATA'),
  ('_tcl_data\\tzdata\\Pacific\\Samoa',
   'C:\\Python313\\tcl\\tcl8.6\\tzdata\\Pacific\\Samoa',
   'DATA'),
  ('_tcl_data\\tzdata\\America\\Belize',
   'C:\\Python313\\tcl\\tcl8.6\\tzdata\\America\\Belize',
   'DATA'),
  ('_tcl_data\\msgs\\kok_in.msg',
   'C:\\Python313\\tcl\\tcl8.6\\msgs\\kok_in.msg',
   'DATA'),
  ('_tcl_data\\tzdata\\Antarctica\\Syowa',
   'C:\\Python313\\tcl\\tcl8.6\\tzdata\\Antarctica\\Syowa',
   'DATA'),
  ('_tcl_data\\tzdata\\NZ-CHAT',
   'C:\\Python313\\tcl\\tcl8.6\\tzdata\\NZ-CHAT',
   'DATA'),
  ('_tcl_data\\tzdata\\Pacific\\Galapagos',
   'C:\\Python313\\tcl\\tcl8.6\\tzdata\\Pacific\\Galapagos',
   'DATA'),
  ('_tk_data\\msgs\\pt.msg', 'C:\\Python313\\tcl\\tk8.6\\msgs\\pt.msg', 'DATA'),
  ('_tcl_data\\tzdata\\America\\Santiago',
   'C:\\Python313\\tcl\\tcl8.6\\tzdata\\America\\Santiago',
   'DATA'),
  ('_tcl_data\\msgs\\en_in.msg',
   'C:\\Python313\\tcl\\tcl8.6\\msgs\\en_in.msg',
   'DATA'),
  ('_tcl_data\\msgs\\es.msg',
   'C:\\Python313\\tcl\\tcl8.6\\msgs\\es.msg',
   'DATA'),
  ('_tcl_data\\tzdata\\PRC', 'C:\\Python313\\tcl\\tcl8.6\\tzdata\\PRC', 'DATA'),
  ('_tcl_data\\encoding\\iso8859-5.enc',
   'C:\\Python313\\tcl\\tcl8.6\\encoding\\iso8859-5.enc',
   'DATA'),
  ('_tcl_data\\tzdata\\America\\La_Paz',
   'C:\\Python313\\tcl\\tcl8.6\\tzdata\\America\\La_Paz',
   'DATA'),
  ('_tcl_data\\tzdata\\Pacific\\Majuro',
   'C:\\Python313\\tcl\\tcl8.6\\tzdata\\Pacific\\Majuro',
   'DATA'),
  ('_tcl_data\\msgs\\pl.msg',
   'C:\\Python313\\tcl\\tcl8.6\\msgs\\pl.msg',
   'DATA'),
  ('_tcl_data\\tzdata\\Australia\\Currie',
   'C:\\Python313\\tcl\\tcl8.6\\tzdata\\Australia\\Currie',
   'DATA'),
  ('setuptools\\_vendor\\jaraco\\text\\Lorem ipsum.txt',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python313\\site-packages\\setuptools\\_vendor\\jaraco\\text\\Lorem '
   'ipsum.txt',
   'DATA'),
  ('setuptools\\_vendor\\importlib_metadata-8.0.0.dist-info\\REQUESTED',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python313\\site-packages\\setuptools\\_vendor\\importlib_metadata-8.0.0.dist-info\\REQUESTED',
   'DATA'),
  ('setuptools\\_vendor\\importlib_metadata-8.0.0.dist-info\\LICENSE',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python313\\site-packages\\setuptools\\_vendor\\importlib_metadata-8.0.0.dist-info\\LICENSE',
   'DATA'),
  ('setuptools\\_vendor\\importlib_metadata-8.0.0.dist-info\\RECORD',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python313\\site-packages\\setuptools\\_vendor\\importlib_metadata-8.0.0.dist-info\\RECORD',
   'DATA'),
  ('setuptools\\_vendor\\importlib_metadata-8.0.0.dist-info\\METADATA',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python313\\site-packages\\setuptools\\_vendor\\importlib_metadata-8.0.0.dist-info\\METADATA',
   'DATA'),
  ('setuptools\\_vendor\\importlib_metadata-8.0.0.dist-info\\WHEEL',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python313\\site-packages\\setuptools\\_vendor\\importlib_metadata-8.0.0.dist-info\\WHEEL',
   'DATA'),
  ('setuptools\\_vendor\\importlib_metadata-8.0.0.dist-info\\INSTALLER',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python313\\site-packages\\setuptools\\_vendor\\importlib_metadata-8.0.0.dist-info\\INSTALLER',
   'DATA'),
  ('setuptools\\_vendor\\importlib_metadata-8.0.0.dist-info\\top_level.txt',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python313\\site-packages\\setuptools\\_vendor\\importlib_metadata-8.0.0.dist-info\\top_level.txt',
   'DATA'),
  ('pyproj\\_network.c',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python313\\site-packages\\pyproj\\_network.c',
   'DATA'),
  ('pyproj\\_crs.pyx',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python313\\site-packages\\pyproj\\_crs.pyx',
   'DATA'),
  ('pyproj\\proj_dir\\share\\proj\\usage',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python313\\site-packages\\pyproj\\proj_dir\\share\\proj\\usage',
   'DATA'),
  ('pyproj\\_transformer.pxd',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python313\\site-packages\\pyproj\\_transformer.pxd',
   'DATA'),
  ('pyproj\\_geod.pyx',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python313\\site-packages\\pyproj\\_geod.pyx',
   'DATA'),
  ('pyproj\\proj_dir\\share\\proj\\CH',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python313\\site-packages\\pyproj\\proj_dir\\share\\proj\\CH',
   'DATA'),
  ('pyproj\\base.pxi',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python313\\site-packages\\pyproj\\base.pxi',
   'DATA'),
  ('pyproj\\proj_dir\\share\\proj\\proj-targets-debug.cmake',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python313\\site-packages\\pyproj\\proj_dir\\share\\proj\\proj-targets-debug.cmake',
   'DATA'),
  ('pyproj\\proj_dir\\share\\proj\\vcpkg.spdx.json',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python313\\site-packages\\pyproj\\proj_dir\\share\\proj\\vcpkg.spdx.json',
   'DATA'),
  ('pyproj\\_crs.c',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python313\\site-packages\\pyproj\\_crs.c',
   'DATA'),
  ('pyproj\\_compat.c',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python313\\site-packages\\pyproj\\_compat.c',
   'DATA'),
  ('pyproj\\_compat.pxd',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python313\\site-packages\\pyproj\\_compat.pxd',
   'DATA'),
  ('pyproj\\database.pyi',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python313\\site-packages\\pyproj\\database.pyi',
   'DATA'),
  ('pyproj\\proj.pxi',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python313\\site-packages\\pyproj\\proj.pxi',
   'DATA'),
  ('pyproj\\proj_dir\\share\\proj\\triangulation.schema.json',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python313\\site-packages\\pyproj\\proj_dir\\share\\proj\\triangulation.schema.json',
   'DATA'),
  ('pyproj\\proj_dir\\share\\proj\\proj-config-version.cmake',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python313\\site-packages\\pyproj\\proj_dir\\share\\proj\\proj-config-version.cmake',
   'DATA'),
  ('pyproj\\_context.pxd',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python313\\site-packages\\pyproj\\_context.pxd',
   'DATA'),
  ('pyproj\\proj_dir\\share\\proj\\ITRF2014',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python313\\site-packages\\pyproj\\proj_dir\\share\\proj\\ITRF2014',
   'DATA'),
  ('pyproj\\_geod.pyi',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python313\\site-packages\\pyproj\\_geod.pyi',
   'DATA'),
  ('pyproj\\_sync.c',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python313\\site-packages\\pyproj\\_sync.c',
   'DATA'),
  ('pyproj\\proj_dir\\share\\proj\\other.extra',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python313\\site-packages\\pyproj\\proj_dir\\share\\proj\\other.extra',
   'DATA'),
  ('pyproj\\list.pyi',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python313\\site-packages\\pyproj\\list.pyi',
   'DATA'),
  ('pyproj\\proj_dir\\share\\proj\\projjson.schema.json',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python313\\site-packages\\pyproj\\proj_dir\\share\\proj\\projjson.schema.json',
   'DATA'),
  ('pyproj\\_transformer.c',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python313\\site-packages\\pyproj\\_transformer.c',
   'DATA'),
  ('pyproj\\_network.pyx',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python313\\site-packages\\pyproj\\_network.pyx',
   'DATA'),
  ('pyproj\\proj_dir\\share\\proj\\nad.lst',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python313\\site-packages\\pyproj\\proj_dir\\share\\proj\\nad.lst',
   'DATA'),
  ('pyproj\\proj_dir\\share\\proj\\proj.db',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python313\\site-packages\\pyproj\\proj_dir\\share\\proj\\proj.db',
   'DATA'),
  ('pyproj\\proj_dir\\share\\proj\\vcpkg_abi_info.txt',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python313\\site-packages\\pyproj\\proj_dir\\share\\proj\\vcpkg_abi_info.txt',
   'DATA'),
  ('pyproj\\_context.c',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python313\\site-packages\\pyproj\\_context.c',
   'DATA'),
  ('pyproj\\proj_dir\\share\\proj\\ITRF2020',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python313\\site-packages\\pyproj\\proj_dir\\share\\proj\\ITRF2020',
   'DATA'),
  ('pyproj\\proj_dir\\share\\proj\\proj-config.cmake',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python313\\site-packages\\pyproj\\proj_dir\\share\\proj\\proj-config.cmake',
   'DATA'),
  ('pyproj\\proj_dir\\share\\proj\\ITRF2000',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python313\\site-packages\\pyproj\\proj_dir\\share\\proj\\ITRF2000',
   'DATA'),
  ('pyproj\\_transformer.pyi',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python313\\site-packages\\pyproj\\_transformer.pyi',
   'DATA'),
  ('pyproj\\_compat.pyx',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python313\\site-packages\\pyproj\\_compat.pyx',
   'DATA'),
  ('pyproj\\_sync.pyx',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python313\\site-packages\\pyproj\\_sync.pyx',
   'DATA'),
  ('pyproj\\_context.pyi',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python313\\site-packages\\pyproj\\_context.pyi',
   'DATA'),
  ('pyproj\\py.typed',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python313\\site-packages\\pyproj\\py.typed',
   'DATA'),
  ('pyproj\\list.c',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python313\\site-packages\\pyproj\\list.c',
   'DATA'),
  ('pyproj\\proj_dir\\share\\proj\\ITRF2008',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python313\\site-packages\\pyproj\\proj_dir\\share\\proj\\ITRF2008',
   'DATA'),
  ('pyproj\\database.pyx',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python313\\site-packages\\pyproj\\database.pyx',
   'DATA'),
  ('pyproj\\proj_dir\\share\\proj\\proj4-targets-debug.cmake',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python313\\site-packages\\pyproj\\proj_dir\\share\\proj\\proj4-targets-debug.cmake',
   'DATA'),
  ('pyproj\\_version.c',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python313\\site-packages\\pyproj\\_version.c',
   'DATA'),
  ('pyproj\\_crs.pyi',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python313\\site-packages\\pyproj\\_crs.pyi',
   'DATA'),
  ('pyproj\\_version.pyi',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python313\\site-packages\\pyproj\\_version.pyi',
   'DATA'),
  ('pyproj\\_network.pyi',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python313\\site-packages\\pyproj\\_network.pyi',
   'DATA'),
  ('pyproj\\proj_dir\\share\\proj\\proj-targets-release.cmake',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python313\\site-packages\\pyproj\\proj_dir\\share\\proj\\proj-targets-release.cmake',
   'DATA'),
  ('pyproj\\_geod.c',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python313\\site-packages\\pyproj\\_geod.c',
   'DATA'),
  ('pyproj\\proj_dir\\share\\proj\\deformation_model.schema.json',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python313\\site-packages\\pyproj\\proj_dir\\share\\proj\\deformation_model.schema.json',
   'DATA'),
  ('pyproj\\_geod.pxd',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python313\\site-packages\\pyproj\\_geod.pxd',
   'DATA'),
  ('pyproj\\_sync.pyi',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python313\\site-packages\\pyproj\\_sync.pyi',
   'DATA'),
  ('pyproj\\_transformer.pyx',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python313\\site-packages\\pyproj\\_transformer.pyx',
   'DATA'),
  ('pyproj\\proj_dir\\share\\proj\\world',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python313\\site-packages\\pyproj\\proj_dir\\share\\proj\\world',
   'DATA'),
  ('pyproj\\list.pyx',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python313\\site-packages\\pyproj\\list.pyx',
   'DATA'),
  ('pyproj\\_context.pyx',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python313\\site-packages\\pyproj\\_context.pyx',
   'DATA'),
  ('pyproj\\_crs.pxd',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python313\\site-packages\\pyproj\\_crs.pxd',
   'DATA'),
  ('pyproj\\database.c',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python313\\site-packages\\pyproj\\database.c',
   'DATA'),
  ('pyproj\\proj_dir\\share\\proj\\nad83',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python313\\site-packages\\pyproj\\proj_dir\\share\\proj\\nad83',
   'DATA'),
  ('pyproj\\_version.pyx',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python313\\site-packages\\pyproj\\_version.pyx',
   'DATA'),
  ('pyproj\\proj_dir\\share\\proj\\proj-targets.cmake',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python313\\site-packages\\pyproj\\proj_dir\\share\\proj\\proj-targets.cmake',
   'DATA'),
  ('pyproj\\proj_dir\\share\\proj\\proj4-targets.cmake',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python313\\site-packages\\pyproj\\proj_dir\\share\\proj\\proj4-targets.cmake',
   'DATA'),
  ('pyproj\\_compat.pyi',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python313\\site-packages\\pyproj\\_compat.pyi',
   'DATA'),
  ('pyproj\\proj_dir\\share\\proj\\GL27',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python313\\site-packages\\pyproj\\proj_dir\\share\\proj\\GL27',
   'DATA'),
  ('pyproj\\proj_dir\\share\\proj\\nad27',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python313\\site-packages\\pyproj\\proj_dir\\share\\proj\\nad27',
   'DATA'),
  ('pyproj\\proj_dir\\share\\proj\\proj.ini',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python313\\site-packages\\pyproj\\proj_dir\\share\\proj\\proj.ini',
   'DATA'),
  ('pyproj\\proj_dir\\share\\proj\\copyright',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python313\\site-packages\\pyproj\\proj_dir\\share\\proj\\copyright',
   'DATA'),
  ('pyproj\\proj_dir\\share\\proj\\proj4-targets-release.cmake',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python313\\site-packages\\pyproj\\proj_dir\\share\\proj\\proj4-targets-release.cmake',
   'DATA'),
  ('certifi\\py.typed',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python313\\site-packages\\certifi\\py.typed',
   'DATA'),
  ('certifi\\cacert.pem',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python313\\site-packages\\certifi\\cacert.pem',
   'DATA'),
  ('fiona\\gdal_data\\tms_MapML_CBMTILE.json',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python313\\site-packages\\fiona\\gdal_data\\tms_MapML_CBMTILE.json',
   'DATA'),
  ('fiona\\gdal_data\\grib2_table_4_2_4_4.csv',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python313\\site-packages\\fiona\\gdal_data\\grib2_table_4_2_4_4.csv',
   'DATA'),
  ('fiona\\gdal_data\\grib2_table_4_2_4_9.csv',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python313\\site-packages\\fiona\\gdal_data\\grib2_table_4_2_4_9.csv',
   'DATA'),
  ('fiona\\gdal_data\\epsg.wkt',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python313\\site-packages\\fiona\\gdal_data\\epsg.wkt',
   'DATA'),
  ('fiona\\gdal_data\\inspire_cp_CadastralZoning.gfs',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python313\\site-packages\\fiona\\gdal_data\\inspire_cp_CadastralZoning.gfs',
   'DATA'),
  ('fiona\\gdal_data\\jpfgdgml_ElevPt.gfs',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python313\\site-packages\\fiona\\gdal_data\\jpfgdgml_ElevPt.gfs',
   'DATA'),
  ('fiona\\gdal_data\\grib2_table_4_2_20_0.csv',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python313\\site-packages\\fiona\\gdal_data\\grib2_table_4_2_20_0.csv',
   'DATA'),
  ('fiona\\gdal_data\\grib2_table_4_5.csv',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python313\\site-packages\\fiona\\gdal_data\\grib2_table_4_5.csv',
   'DATA'),
  ('fiona\\gdal_data\\tms_MapML_APSTILE.json',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python313\\site-packages\\fiona\\gdal_data\\tms_MapML_APSTILE.json',
   'DATA'),
  ('fiona\\proj_data\\proj.ini',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python313\\site-packages\\fiona\\proj_data\\proj.ini',
   'DATA'),
  ('fiona\\gdal_data\\pdfcomposition.xsd',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python313\\site-packages\\fiona\\gdal_data\\pdfcomposition.xsd',
   'DATA'),
  ('fiona\\gdal_data\\inspire_cp_CadastralParcel.gfs',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python313\\site-packages\\fiona\\gdal_data\\inspire_cp_CadastralParcel.gfs',
   'DATA'),
  ('fiona\\gdal_data\\jpfgdgml_CommBdry.gfs',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python313\\site-packages\\fiona\\gdal_data\\jpfgdgml_CommBdry.gfs',
   'DATA'),
  ('fiona\\gdal_data\\grib2_table_4_2_local_NDFD.csv',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python313\\site-packages\\fiona\\gdal_data\\grib2_table_4_2_local_NDFD.csv',
   'DATA'),
  ('fiona\\gdal_data\\grib2_table_4_2_4_8.csv',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python313\\site-packages\\fiona\\gdal_data\\grib2_table_4_2_4_8.csv',
   'DATA'),
  ('fiona\\gdal_data\\grib2_table_4_2_1_2.csv',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python313\\site-packages\\fiona\\gdal_data\\grib2_table_4_2_1_2.csv',
   'DATA'),
  ('fiona\\proj_data\\ITRF2014',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python313\\site-packages\\fiona\\proj_data\\ITRF2014',
   'DATA'),
  ('fiona\\gdal_data\\seed_2d.dgn',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python313\\site-packages\\fiona\\gdal_data\\seed_2d.dgn',
   'DATA'),
  ('fiona\\gdal_data\\grib2_table_4_2_20_1.csv',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python313\\site-packages\\fiona\\gdal_data\\grib2_table_4_2_20_1.csv',
   'DATA'),
  ('fiona\\gdal_data\\jpfgdgml_LeveeEdge.gfs',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python313\\site-packages\\fiona\\gdal_data\\jpfgdgml_LeveeEdge.gfs',
   'DATA'),
  ('fiona\\gdal_data\\tms_NZTM2000.json',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python313\\site-packages\\fiona\\gdal_data\\tms_NZTM2000.json',
   'DATA'),
  ('fiona\\gdal.pxi',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python313\\site-packages\\fiona\\gdal.pxi',
   'DATA'),
  ('fiona\\proj_data\\triangulation.schema.json',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python313\\site-packages\\fiona\\proj_data\\triangulation.schema.json',
   'DATA'),
  ('fiona\\gdal_data\\grib2_table_4_2_3_6.csv',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python313\\site-packages\\fiona\\gdal_data\\grib2_table_4_2_3_6.csv',
   'DATA'),
  ('fiona\\proj_data\\CH',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python313\\site-packages\\fiona\\proj_data\\CH',
   'DATA'),
  ('fiona\\proj_data\\world',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python313\\site-packages\\fiona\\proj_data\\world',
   'DATA'),
  ('fiona\\gdal_data\\jpfgdgml_GCP.gfs',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python313\\site-packages\\fiona\\gdal_data\\jpfgdgml_GCP.gfs',
   'DATA'),
  ('fiona\\ogrext3.pxd',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python313\\site-packages\\fiona\\ogrext3.pxd',
   'DATA'),
  ('fiona\\gdal_data\\gdalmdiminfo_output.schema.json',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python313\\site-packages\\fiona\\gdal_data\\gdalmdiminfo_output.schema.json',
   'DATA'),
  ('fiona\\gdal_data\\gt_ellips.csv',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python313\\site-packages\\fiona\\gdal_data\\gt_ellips.csv',
   'DATA'),
  ('fiona\\gdal_data\\vdv452.xml',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python313\\site-packages\\fiona\\gdal_data\\vdv452.xml',
   'DATA'),
  ('fiona\\gdal_data\\jpfgdgml_AdmPt.gfs',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python313\\site-packages\\fiona\\gdal_data\\jpfgdgml_AdmPt.gfs',
   'DATA'),
  ('fiona\\proj_data\\other.extra',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python313\\site-packages\\fiona\\proj_data\\other.extra',
   'DATA'),
  ('fiona\\gdal_data\\grib2_table_4_2_local_MRMS.csv',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python313\\site-packages\\fiona\\gdal_data\\grib2_table_4_2_local_MRMS.csv',
   'DATA'),
  ('fiona\\gdal_data\\grib2_table_4_2_3_1.csv',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python313\\site-packages\\fiona\\gdal_data\\grib2_table_4_2_3_1.csv',
   'DATA'),
  ('fiona\\gdal_data\\jpfgdgml_RdArea.gfs',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python313\\site-packages\\fiona\\gdal_data\\jpfgdgml_RdArea.gfs',
   'DATA'),
  ('fiona\\gdal_data\\GDALLogoBW.svg',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python313\\site-packages\\fiona\\gdal_data\\GDALLogoBW.svg',
   'DATA'),
  ('fiona\\gdal_data\\inspire_cp_CadastralBoundary.gfs',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python313\\site-packages\\fiona\\gdal_data\\inspire_cp_CadastralBoundary.gfs',
   'DATA'),
  ('fiona\\ogrext1.pxd',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python313\\site-packages\\fiona\\ogrext1.pxd',
   'DATA'),
  ('fiona\\gdal_data\\cubewerx_extra.wkt',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python313\\site-packages\\fiona\\gdal_data\\cubewerx_extra.wkt',
   'DATA'),
  ('fiona\\proj_data\\proj-targets-release.cmake',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python313\\site-packages\\fiona\\proj_data\\proj-targets-release.cmake',
   'DATA'),
  ('fiona\\gdal_data\\GDALConfig.cmake',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python313\\site-packages\\fiona\\gdal_data\\GDALConfig.cmake',
   'DATA'),
  ('fiona\\gdal_data\\grib2_table_4_2_10_0.csv',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python313\\site-packages\\fiona\\gdal_data\\grib2_table_4_2_10_0.csv',
   'DATA'),
  ('fiona\\gdal_data\\grib2_table_4_2_3_3.csv',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python313\\site-packages\\fiona\\gdal_data\\grib2_table_4_2_3_3.csv',
   'DATA'),
  ('fiona\\gdal_data\\grib2_table_4_2_0_5.csv',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python313\\site-packages\\fiona\\gdal_data\\grib2_table_4_2_0_5.csv',
   'DATA'),
  ('fiona\\proj_data\\proj-config.cmake',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python313\\site-packages\\fiona\\proj_data\\proj-config.cmake',
   'DATA'),
  ('fiona\\gdal_data\\header.dxf',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python313\\site-packages\\fiona\\gdal_data\\header.dxf',
   'DATA'),
  ('fiona\\gdal_data\\grib2_table_4_2_local_HPC.csv',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python313\\site-packages\\fiona\\gdal_data\\grib2_table_4_2_local_HPC.csv',
   'DATA'),
  ('fiona\\gdal_data\\grib2_table_versions.csv',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python313\\site-packages\\fiona\\gdal_data\\grib2_table_versions.csv',
   'DATA'),
  ('fiona\\gdal_data\\vdv452.xsd',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python313\\site-packages\\fiona\\gdal_data\\vdv452.xsd',
   'DATA'),
  ('fiona\\gdal_data\\grib2_table_4_2_0_16.csv',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python313\\site-packages\\fiona\\gdal_data\\grib2_table_4_2_0_16.csv',
   'DATA'),
  ('fiona\\gdal_data\\grib2_table_4_2_0_17.csv',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python313\\site-packages\\fiona\\gdal_data\\grib2_table_4_2_0_17.csv',
   'DATA'),
  ('fiona\\gdal_data\\pds4_template.xml',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python313\\site-packages\\fiona\\gdal_data\\pds4_template.xml',
   'DATA'),
  ('fiona\\proj_data\\proj4-targets-release.cmake',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python313\\site-packages\\fiona\\proj_data\\proj4-targets-release.cmake',
   'DATA'),
  ('fiona\\gdal_data\\grib2_table_4_2_0_191.csv',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python313\\site-packages\\fiona\\gdal_data\\grib2_table_4_2_0_191.csv',
   'DATA'),
  ('fiona\\gdal_data\\vicar.json',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python313\\site-packages\\fiona\\gdal_data\\vicar.json',
   'DATA'),
  ('fiona\\gdal_data\\grib2_table_4_2_3_4.csv',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python313\\site-packages\\fiona\\gdal_data\\grib2_table_4_2_3_4.csv',
   'DATA'),
  ('fiona\\gdal_data\\jpfgdgml_CommPt.gfs',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python313\\site-packages\\fiona\\gdal_data\\jpfgdgml_CommPt.gfs',
   'DATA'),
  ('fiona\\gdal_data\\grib2_table_4_2_local_Canada.csv',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python313\\site-packages\\fiona\\gdal_data\\grib2_table_4_2_local_Canada.csv',
   'DATA'),
  ('fiona\\proj_data\\ITRF2008',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python313\\site-packages\\fiona\\proj_data\\ITRF2008',
   'DATA'),
  ('fiona\\gdal_data\\grib2_table_4_2_4_1.csv',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python313\\site-packages\\fiona\\gdal_data\\grib2_table_4_2_4_1.csv',
   'DATA'),
  ('fiona\\gdal_data\\ozi_datum.csv',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python313\\site-packages\\fiona\\gdal_data\\ozi_datum.csv',
   'DATA'),
  ('fiona\\gdal_data\\esri_StatePlane_extra.wkt',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python313\\site-packages\\fiona\\gdal_data\\esri_StatePlane_extra.wkt',
   'DATA'),
  ('fiona\\_csl.pxd',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python313\\site-packages\\fiona\\_csl.pxd',
   'DATA'),
  ('fiona\\gdal_data\\grib2_table_4_2_10_2.csv',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python313\\site-packages\\fiona\\gdal_data\\grib2_table_4_2_10_2.csv',
   'DATA'),
  ('fiona\\gdal_data\\jpfgdgml_WStrL.gfs',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python313\\site-packages\\fiona\\gdal_data\\jpfgdgml_WStrL.gfs',
   'DATA'),
  ('fiona\\gdal_data\\grib2_table_4_2_1_0.csv',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python313\\site-packages\\fiona\\gdal_data\\grib2_table_4_2_1_0.csv',
   'DATA'),
  ('fiona\\gdal_data\\grib2_table_4_2_4_7.csv',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python313\\site-packages\\fiona\\gdal_data\\grib2_table_4_2_4_7.csv',
   'DATA'),
  ('fiona\\gdal_data\\GDAL-targets.cmake',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python313\\site-packages\\fiona\\gdal_data\\GDAL-targets.cmake',
   'DATA'),
  ('fiona\\gdal_data\\grib2_table_4_2_0_21.csv',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python313\\site-packages\\fiona\\gdal_data\\grib2_table_4_2_0_21.csv',
   'DATA'),
  ('fiona\\gdal_data\\grib2_table_4_2_0_14.csv',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python313\\site-packages\\fiona\\gdal_data\\grib2_table_4_2_0_14.csv',
   'DATA'),
  ('fiona\\gdal_data\\GDAL-targets-debug.cmake',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python313\\site-packages\\fiona\\gdal_data\\GDAL-targets-debug.cmake',
   'DATA'),
  ('fiona\\gdal_data\\GDALLogoGS.svg',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python313\\site-packages\\fiona\\gdal_data\\GDALLogoGS.svg',
   'DATA'),
  ('fiona\\gdal_data\\s57attributes.csv',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python313\\site-packages\\fiona\\gdal_data\\s57attributes.csv',
   'DATA'),
  ('fiona\\gdal_data\\grib2_table_4_2_4_0.csv',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python313\\site-packages\\fiona\\gdal_data\\grib2_table_4_2_4_0.csv',
   'DATA'),
  ('fiona\\gdal_data\\grib2_table_4_2_10_3.csv',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python313\\site-packages\\fiona\\gdal_data\\grib2_table_4_2_10_3.csv',
   'DATA'),
  ('fiona\\gdal_data\\ruian_vf_v1.gfs',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python313\\site-packages\\fiona\\gdal_data\\ruian_vf_v1.gfs',
   'DATA'),
  ('fiona\\_geometry.pxd',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python313\\site-packages\\fiona\\_geometry.pxd',
   'DATA'),
  ('fiona\\gdal_data\\grib2_table_4_2_4_6.csv',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python313\\site-packages\\fiona\\gdal_data\\grib2_table_4_2_4_6.csv',
   'DATA'),
  ('fiona\\gdal_data\\grib2_center.csv',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python313\\site-packages\\fiona\\gdal_data\\grib2_center.csv',
   'DATA'),
  ('fiona\\gdal_data\\grib2_table_4_2_4_2.csv',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python313\\site-packages\\fiona\\gdal_data\\grib2_table_4_2_4_2.csv',
   'DATA'),
  ('fiona\\gdal_data\\osmconf.ini',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python313\\site-packages\\fiona\\gdal_data\\osmconf.ini',
   'DATA'),
  ('fiona\\gdal_data\\grib2_table_4_2_2_5.csv',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python313\\site-packages\\fiona\\gdal_data\\grib2_table_4_2_2_5.csv',
   'DATA'),
  ('fiona\\gdal_data\\grib2_table_4_2_10_4.csv',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python313\\site-packages\\fiona\\gdal_data\\grib2_table_4_2_10_4.csv',
   'DATA'),
  ('fiona\\gdal_data\\jpfgdgml_AdmArea.gfs',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python313\\site-packages\\fiona\\gdal_data\\jpfgdgml_AdmArea.gfs',
   'DATA'),
  ('fiona\\gdal_data\\grib2_table_4_2_10_191.csv',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python313\\site-packages\\fiona\\gdal_data\\grib2_table_4_2_10_191.csv',
   'DATA'),
  ('fiona\\gdal_data\\jpfgdgml_Cstline.gfs',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python313\\site-packages\\fiona\\gdal_data\\jpfgdgml_Cstline.gfs',
   'DATA'),
  ('fiona\\gdal_data\\grib2_table_4_2_20_2.csv',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python313\\site-packages\\fiona\\gdal_data\\grib2_table_4_2_20_2.csv',
   'DATA'),
  ('fiona\\proj_data\\proj4-targets-debug.cmake',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python313\\site-packages\\fiona\\proj_data\\proj4-targets-debug.cmake',
   'DATA'),
  ('fiona\\gdal_data\\grib2_table_4_2_0_190.csv',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python313\\site-packages\\fiona\\gdal_data\\grib2_table_4_2_0_190.csv',
   'DATA'),
  ('fiona\\gdal_data\\grib2_table_4_2_0_3.csv',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python313\\site-packages\\fiona\\gdal_data\\grib2_table_4_2_0_3.csv',
   'DATA'),
  ('fiona\\gdal_data\\nitf_spec.xml',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python313\\site-packages\\fiona\\gdal_data\\nitf_spec.xml',
   'DATA'),
  ('fiona\\gdal_data\\grib2_table_4_2_0_20.csv',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python313\\site-packages\\fiona\\gdal_data\\grib2_table_4_2_0_20.csv',
   'DATA'),
  ('fiona\\_err.pxd',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python313\\site-packages\\fiona\\_err.pxd',
   'DATA'),
  ('fiona\\proj_data\\nad27',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python313\\site-packages\\fiona\\proj_data\\nad27',
   'DATA'),
  ('fiona\\gdal_data\\grib2_table_4_2_0_6.csv',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python313\\site-packages\\fiona\\gdal_data\\grib2_table_4_2_0_6.csv',
   'DATA'),
  ('fiona\\gdal_data\\gdalicon.png',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python313\\site-packages\\fiona\\gdal_data\\gdalicon.png',
   'DATA'),
  ('fiona\\gdal_data\\stateplane.csv',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python313\\site-packages\\fiona\\gdal_data\\stateplane.csv',
   'DATA'),
  ('fiona\\gdal_data\\jpfgdgml_BldL.gfs',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python313\\site-packages\\fiona\\gdal_data\\jpfgdgml_BldL.gfs',
   'DATA'),
  ('fiona\\gdal_data\\nitf_spec.xsd',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python313\\site-packages\\fiona\\gdal_data\\nitf_spec.xsd',
   'DATA'),
  ('fiona\\gdal_data\\jpfgdgml_RdCompt.gfs',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python313\\site-packages\\fiona\\gdal_data\\jpfgdgml_RdCompt.gfs',
   'DATA'),
  ('fiona\\proj_data\\proj.db',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python313\\site-packages\\fiona\\proj_data\\proj.db',
   'DATA'),
  ('fiona\\gdal_data\\grib2_table_4_2_1_1.csv',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python313\\site-packages\\fiona\\gdal_data\\grib2_table_4_2_1_1.csv',
   'DATA'),
  ('fiona\\gdal_data\\tms_LINZAntarticaMapTileGrid.json',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python313\\site-packages\\fiona\\gdal_data\\tms_LINZAntarticaMapTileGrid.json',
   'DATA'),
  ('fiona\\gdal_data\\usage',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python313\\site-packages\\fiona\\gdal_data\\usage',
   'DATA'),
  ('fiona\\gdal_data\\s57objectclasses.csv',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python313\\site-packages\\fiona\\gdal_data\\s57objectclasses.csv',
   'DATA'),
  ('fiona\\gdal_data\\gdaltileindex.xsd',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python313\\site-packages\\fiona\\gdal_data\\gdaltileindex.xsd',
   'DATA'),
  ('fiona\\proj_data\\deformation_model.schema.json',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python313\\site-packages\\fiona\\proj_data\\deformation_model.schema.json',
   'DATA'),
  ('fiona\\proj_data\\nad83',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python313\\site-packages\\fiona\\proj_data\\nad83',
   'DATA'),
  ('fiona\\gdal_data\\jpfgdgml_SBBdry.gfs',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python313\\site-packages\\fiona\\gdal_data\\jpfgdgml_SBBdry.gfs',
   'DATA'),
  ('fiona\\gdal_data\\netcdf_config.xsd',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python313\\site-packages\\fiona\\gdal_data\\netcdf_config.xsd',
   'DATA'),
  ('fiona\\gdal_data\\s57agencies.csv',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python313\\site-packages\\fiona\\gdal_data\\s57agencies.csv',
   'DATA'),
  ('fiona\\gdal_data\\jpfgdgml_WA.gfs',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python313\\site-packages\\fiona\\gdal_data\\jpfgdgml_WA.gfs',
   'DATA'),
  ('fiona\\gdal_data\\jpfgdgml_WStrA.gfs',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python313\\site-packages\\fiona\\gdal_data\\jpfgdgml_WStrA.gfs',
   'DATA'),
  ('fiona\\gdal_data\\GDAL-targets-release.cmake',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python313\\site-packages\\fiona\\gdal_data\\GDAL-targets-release.cmake',
   'DATA'),
  ('fiona\\gdal_data\\bag_template.xml',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python313\\site-packages\\fiona\\gdal_data\\bag_template.xml',
   'DATA'),
  ('fiona\\_cpl.pxd',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python313\\site-packages\\fiona\\_cpl.pxd',
   'DATA'),
  ('fiona\\gdal_data\\grib2_table_4_2_0_1.csv',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python313\\site-packages\\fiona\\gdal_data\\grib2_table_4_2_0_1.csv',
   'DATA'),
  ('fiona\\gdal_data\\ecw_cs.wkt',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python313\\site-packages\\fiona\\gdal_data\\ecw_cs.wkt',
   'DATA'),
  ('fiona\\gdal_data\\grib2_table_4_2_0_18.csv',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python313\\site-packages\\fiona\\gdal_data\\grib2_table_4_2_0_18.csv',
   'DATA'),
  ('fiona\\gdal_data\\grib2_table_4_2_0_4.csv',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python313\\site-packages\\fiona\\gdal_data\\grib2_table_4_2_0_4.csv',
   'DATA'),
  ('fiona\\proj_data\\projjson.schema.json',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python313\\site-packages\\fiona\\proj_data\\projjson.schema.json',
   'DATA'),
  ('fiona\\gdal_data\\grib2_table_4_2_3_2.csv',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python313\\site-packages\\fiona\\gdal_data\\grib2_table_4_2_3_2.csv',
   'DATA'),
  ('fiona\\gdal_data\\s57expectedinput.csv',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python313\\site-packages\\fiona\\gdal_data\\s57expectedinput.csv',
   'DATA'),
  ('fiona\\gdal_data\\default.rsc',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python313\\site-packages\\fiona\\gdal_data\\default.rsc',
   'DATA'),
  ('fiona\\gdal_data\\grib2_table_4_2_0_0.csv',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python313\\site-packages\\fiona\\gdal_data\\grib2_table_4_2_0_0.csv',
   'DATA'),
  ('fiona\\gdal_data\\gml_registry.xsd',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python313\\site-packages\\fiona\\gdal_data\\gml_registry.xsd',
   'DATA'),
  ('fiona\\gdal_data\\gfs.xsd',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python313\\site-packages\\fiona\\gdal_data\\gfs.xsd',
   'DATA'),
  ('fiona\\gdal_data\\trailer.dxf',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python313\\site-packages\\fiona\\gdal_data\\trailer.dxf',
   'DATA'),
  ('fiona\\gdal_data\\grib2_table_4_2_local_NCEP.csv',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python313\\site-packages\\fiona\\gdal_data\\grib2_table_4_2_local_NCEP.csv',
   'DATA'),
  ('fiona\\gdal_data\\grib2_table_4_2_3_5.csv',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python313\\site-packages\\fiona\\gdal_data\\grib2_table_4_2_3_5.csv',
   'DATA'),
  ('fiona\\gdal_data\\grib2_table_4_2_2_0.csv',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python313\\site-packages\\fiona\\gdal_data\\grib2_table_4_2_2_0.csv',
   'DATA'),
  ('fiona\\gdal_data\\grib2_process.csv',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python313\\site-packages\\fiona\\gdal_data\\grib2_process.csv',
   'DATA'),
  ('fiona\\gdal_data\\grib2_table_4_2_10_1.csv',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python313\\site-packages\\fiona\\gdal_data\\grib2_table_4_2_10_1.csv',
   'DATA'),
  ('fiona\\proj_data\\usage',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python313\\site-packages\\fiona\\proj_data\\usage',
   'DATA'),
  ('fiona\\gdal_data\\jpfgdgml_SBAPt.gfs',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python313\\site-packages\\fiona\\gdal_data\\jpfgdgml_SBAPt.gfs',
   'DATA'),
  ('fiona\\gdal_data\\GDALConfigVersion.cmake',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python313\\site-packages\\fiona\\gdal_data\\GDALConfigVersion.cmake',
   'DATA'),
  ('fiona\\gdal_data\\plscenesconf.json',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python313\\site-packages\\fiona\\gdal_data\\plscenesconf.json',
   'DATA'),
  ('fiona\\gdal_data\\grib2_table_4_2_local_index.csv',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python313\\site-packages\\fiona\\gdal_data\\grib2_table_4_2_local_index.csv',
   'DATA'),
  ('fiona\\gdal_data\\jpfgdgml_AdmBdry.gfs',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python313\\site-packages\\fiona\\gdal_data\\jpfgdgml_AdmBdry.gfs',
   'DATA'),
  ('fiona\\gdal_data\\ogrvrt.xsd',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python313\\site-packages\\fiona\\gdal_data\\ogrvrt.xsd',
   'DATA'),
  ('fiona\\gdal_data\\vcpkg_abi_info.txt',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python313\\site-packages\\fiona\\gdal_data\\vcpkg_abi_info.txt',
   'DATA'),
  ('fiona\\gdal_data\\jpfgdgml_RdSgmtA.gfs',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python313\\site-packages\\fiona\\gdal_data\\jpfgdgml_RdSgmtA.gfs',
   'DATA'),
  ('fiona\\gdal_data\\grib2_table_4_2_0_2.csv',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python313\\site-packages\\fiona\\gdal_data\\grib2_table_4_2_0_2.csv',
   'DATA'),
  ('fiona\\gdal_data\\ogrinfo_output.schema.json',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python313\\site-packages\\fiona\\gdal_data\\ogrinfo_output.schema.json',
   'DATA'),
  ('fiona\\gdal_data\\vcpkg.spdx.json',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python313\\site-packages\\fiona\\gdal_data\\vcpkg.spdx.json',
   'DATA'),
  ('fiona\\gdal_data\\gt_datum.csv',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python313\\site-packages\\fiona\\gdal_data\\gt_datum.csv',
   'DATA'),
  ('fiona\\gdal_data\\ruian_vf_st_v1.gfs',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python313\\site-packages\\fiona\\gdal_data\\ruian_vf_st_v1.gfs',
   'DATA'),
  ('fiona\\gdal_data\\copyright',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python313\\site-packages\\fiona\\gdal_data\\copyright',
   'DATA'),
  ('fiona\\_vsiopener.pxd',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python313\\site-packages\\fiona\\_vsiopener.pxd',
   'DATA'),
  ('fiona\\gdal_data\\gdalinfo_output.schema.json',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python313\\site-packages\\fiona\\gdal_data\\gdalinfo_output.schema.json',
   'DATA'),
  ('fiona\\proj_data\\nad.lst',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python313\\site-packages\\fiona\\proj_data\\nad.lst',
   'DATA'),
  ('fiona\\ogrext2.pxd',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python313\\site-packages\\fiona\\ogrext2.pxd',
   'DATA'),
  ('fiona\\gdal_data\\jpfgdgml_RdASL.gfs',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python313\\site-packages\\fiona\\gdal_data\\jpfgdgml_RdASL.gfs',
   'DATA'),
  ('fiona\\gdal_data\\LICENSE.TXT',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python313\\site-packages\\fiona\\gdal_data\\LICENSE.TXT',
   'DATA'),
  ('fiona\\gdal_data\\jpfgdgml_RvrMgtBdry.gfs',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python313\\site-packages\\fiona\\gdal_data\\jpfgdgml_RvrMgtBdry.gfs',
   'DATA'),
  ('fiona\\gdal_data\\grib2_table_4_2_2_6.csv',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python313\\site-packages\\fiona\\gdal_data\\grib2_table_4_2_2_6.csv',
   'DATA'),
  ('fiona\\proj_data\\proj-targets.cmake',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python313\\site-packages\\fiona\\proj_data\\proj-targets.cmake',
   'DATA'),
  ('fiona\\gdal_data\\gdalvrt.xsd',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python313\\site-packages\\fiona\\gdal_data\\gdalvrt.xsd',
   'DATA'),
  ('fiona\\gdal_data\\grib2_table_4_2_2_4.csv',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python313\\site-packages\\fiona\\gdal_data\\grib2_table_4_2_2_4.csv',
   'DATA'),
  ('fiona\\gdal_data\\jpfgdgml_SBArea.gfs',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python313\\site-packages\\fiona\\gdal_data\\jpfgdgml_SBArea.gfs',
   'DATA'),
  ('fiona\\gdal_data\\vcpkg-cmake-wrapper.cmake',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python313\\site-packages\\fiona\\gdal_data\\vcpkg-cmake-wrapper.cmake',
   'DATA'),
  ('fiona\\gdal_data\\jpfgdgml_RailCL.gfs',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python313\\site-packages\\fiona\\gdal_data\\jpfgdgml_RailCL.gfs',
   'DATA'),
  ('fiona\\proj_data\\vcpkg_abi_info.txt',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python313\\site-packages\\fiona\\proj_data\\vcpkg_abi_info.txt',
   'DATA'),
  ('fiona\\gdal_data\\inspire_cp_BasicPropertyUnit.gfs',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python313\\site-packages\\fiona\\gdal_data\\inspire_cp_BasicPropertyUnit.gfs',
   'DATA'),
  ('fiona\\proj_data\\proj4-targets.cmake',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python313\\site-packages\\fiona\\proj_data\\proj4-targets.cmake',
   'DATA'),
  ('fiona\\gdal_data\\jpfgdgml_WL.gfs',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python313\\site-packages\\fiona\\gdal_data\\jpfgdgml_WL.gfs',
   'DATA'),
  ('fiona\\crs.pxd',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python313\\site-packages\\fiona\\crs.pxd',
   'DATA'),
  ('fiona\\gdal_data\\ruian_vf_ob_v1.gfs',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python313\\site-packages\\fiona\\gdal_data\\ruian_vf_ob_v1.gfs',
   'DATA'),
  ('fiona\\gdal_data\\jpfgdgml_RdMgtBdry.gfs',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python313\\site-packages\\fiona\\gdal_data\\jpfgdgml_RdMgtBdry.gfs',
   'DATA'),
  ('fiona\\gdal_data\\GDALLogoColor.svg',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python313\\site-packages\\fiona\\gdal_data\\GDALLogoColor.svg',
   'DATA'),
  ('fiona\\gdal_data\\pci_ellips.txt',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python313\\site-packages\\fiona\\gdal_data\\pci_ellips.txt',
   'DATA'),
  ('fiona\\gdal_data\\pci_datum.txt',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python313\\site-packages\\fiona\\gdal_data\\pci_datum.txt',
   'DATA'),
  ('fiona\\gdal_data\\grib2_subcenter.csv',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python313\\site-packages\\fiona\\gdal_data\\grib2_subcenter.csv',
   'DATA'),
  ('fiona\\gdal_data\\jpfgdgml_RdEdg.gfs',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python313\\site-packages\\fiona\\gdal_data\\jpfgdgml_RdEdg.gfs',
   'DATA'),
  ('fiona\\gdal_data\\grib2_table_4_2_4_3.csv',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python313\\site-packages\\fiona\\gdal_data\\grib2_table_4_2_4_3.csv',
   'DATA'),
  ('fiona\\gdal_data\\grib2_table_4_2_0_19.csv',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python313\\site-packages\\fiona\\gdal_data\\grib2_table_4_2_0_19.csv',
   'DATA'),
  ('fiona\\gdal_data\\grib2_table_4_2_0_15.csv',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python313\\site-packages\\fiona\\gdal_data\\grib2_table_4_2_0_15.csv',
   'DATA'),
  ('fiona\\proj_data\\copyright',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python313\\site-packages\\fiona\\proj_data\\copyright',
   'DATA'),
  ('fiona\\gdal_data\\MM_m_idofic.csv',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python313\\site-packages\\fiona\\gdal_data\\MM_m_idofic.csv',
   'DATA'),
  ('fiona\\gdal_data\\grib2_table_4_2_0_13.csv',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python313\\site-packages\\fiona\\gdal_data\\grib2_table_4_2_0_13.csv',
   'DATA'),
  ('fiona\\gdal_data\\grib2_table_4_2_0_7.csv',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python313\\site-packages\\fiona\\gdal_data\\grib2_table_4_2_0_7.csv',
   'DATA'),
  ('fiona\\gdal_data\\gml_registry.xml',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python313\\site-packages\\fiona\\gdal_data\\gml_registry.xml',
   'DATA'),
  ('fiona\\proj_data\\GL27',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python313\\site-packages\\fiona\\proj_data\\GL27',
   'DATA'),
  ('fiona\\proj_data\\ITRF2000',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python313\\site-packages\\fiona\\proj_data\\ITRF2000',
   'DATA'),
  ('fiona\\gdal_data\\jpfgdgml_Cntr.gfs',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python313\\site-packages\\fiona\\gdal_data\\jpfgdgml_Cntr.gfs',
   'DATA'),
  ('fiona\\proj_data\\proj-config-version.cmake',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python313\\site-packages\\fiona\\proj_data\\proj-config-version.cmake',
   'DATA'),
  ('fiona\\_env.pxd',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python313\\site-packages\\fiona\\_env.pxd',
   'DATA'),
  ('fiona\\proj_data\\vcpkg.spdx.json',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python313\\site-packages\\fiona\\proj_data\\vcpkg.spdx.json',
   'DATA'),
  ('fiona\\gdal_data\\grib2_table_4_2_4_10.csv',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python313\\site-packages\\fiona\\gdal_data\\grib2_table_4_2_4_10.csv',
   'DATA'),
  ('fiona\\gdal_data\\grib2_table_4_2_4_5.csv',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python313\\site-packages\\fiona\\gdal_data\\grib2_table_4_2_4_5.csv',
   'DATA'),
  ('fiona\\proj_data\\proj-targets-debug.cmake',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python313\\site-packages\\fiona\\proj_data\\proj-targets-debug.cmake',
   'DATA'),
  ('fiona\\gdal_data\\grib2_table_4_2_3_0.csv',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python313\\site-packages\\fiona\\gdal_data\\grib2_table_4_2_3_0.csv',
   'DATA'),
  ('fiona\\gdal_data\\ruian_vf_st_uvoh_v1.gfs',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python313\\site-packages\\fiona\\gdal_data\\ruian_vf_st_uvoh_v1.gfs',
   'DATA'),
  ('fiona\\gdal_data\\ozi_ellips.csv',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python313\\site-packages\\fiona\\gdal_data\\ozi_ellips.csv',
   'DATA'),
  ('fiona\\gdal_data\\jpfgdgml_BldA.gfs',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python313\\site-packages\\fiona\\gdal_data\\jpfgdgml_BldA.gfs',
   'DATA'),
  ('fiona\\gdal_data\\grib2_table_4_2_2_3.csv',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python313\\site-packages\\fiona\\gdal_data\\grib2_table_4_2_2_3.csv',
   'DATA'),
  ('fiona\\gdal_data\\eedaconf.json',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python313\\site-packages\\fiona\\gdal_data\\eedaconf.json',
   'DATA'),
  ('fiona\\gdal_data\\seed_3d.dgn',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python313\\site-packages\\fiona\\gdal_data\\seed_3d.dgn',
   'DATA'),
  ('numpy-2.2.5.dist-info\\DELVEWHEEL',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python313\\site-packages\\numpy-2.2.5.dist-info\\DELVEWHEEL',
   'DATA'),
  ('numpy-2.2.5.dist-info\\LICENSE.txt',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python313\\site-packages\\numpy-2.2.5.dist-info\\LICENSE.txt',
   'DATA'),
  ('attrs-25.3.0.dist-info\\METADATA',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python313\\site-packages\\attrs-25.3.0.dist-info\\METADATA',
   'DATA'),
  ('attrs-25.3.0.dist-info\\licenses\\LICENSE',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python313\\site-packages\\attrs-25.3.0.dist-info\\licenses\\LICENSE',
   'DATA'),
  ('setuptools\\_vendor\\wheel-0.45.1.dist-info\\METADATA',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python313\\site-packages\\setuptools\\_vendor\\wheel-0.45.1.dist-info\\METADATA',
   'DATA'),
  ('setuptools\\_vendor\\wheel-0.45.1.dist-info\\entry_points.txt',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python313\\site-packages\\setuptools\\_vendor\\wheel-0.45.1.dist-info\\entry_points.txt',
   'DATA'),
  ('numpy-2.2.5.dist-info\\RECORD',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python313\\site-packages\\numpy-2.2.5.dist-info\\RECORD',
   'DATA'),
  ('setuptools\\_vendor\\wheel-0.45.1.dist-info\\RECORD',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python313\\site-packages\\setuptools\\_vendor\\wheel-0.45.1.dist-info\\RECORD',
   'DATA'),
  ('setuptools\\_vendor\\wheel-0.45.1.dist-info\\INSTALLER',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python313\\site-packages\\setuptools\\_vendor\\wheel-0.45.1.dist-info\\INSTALLER',
   'DATA'),
  ('numpy-2.2.5.dist-info\\entry_points.txt',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python313\\site-packages\\numpy-2.2.5.dist-info\\entry_points.txt',
   'DATA'),
  ('setuptools\\_vendor\\wheel-0.45.1.dist-info\\LICENSE.txt',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python313\\site-packages\\setuptools\\_vendor\\wheel-0.45.1.dist-info\\LICENSE.txt',
   'DATA'),
  ('attrs-25.3.0.dist-info\\INSTALLER',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python313\\site-packages\\attrs-25.3.0.dist-info\\INSTALLER',
   'DATA'),
  ('attrs-25.3.0.dist-info\\RECORD',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python313\\site-packages\\attrs-25.3.0.dist-info\\RECORD',
   'DATA'),
  ('attrs-25.3.0.dist-info\\WHEEL',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python313\\site-packages\\attrs-25.3.0.dist-info\\WHEEL',
   'DATA'),
  ('numpy-2.2.5.dist-info\\INSTALLER',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python313\\site-packages\\numpy-2.2.5.dist-info\\INSTALLER',
   'DATA'),
  ('setuptools\\_vendor\\wheel-0.45.1.dist-info\\REQUESTED',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python313\\site-packages\\setuptools\\_vendor\\wheel-0.45.1.dist-info\\REQUESTED',
   'DATA'),
  ('numpy-2.2.5.dist-info\\METADATA',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python313\\site-packages\\numpy-2.2.5.dist-info\\METADATA',
   'DATA'),
  ('numpy-2.2.5.dist-info\\WHEEL',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python313\\site-packages\\numpy-2.2.5.dist-info\\WHEEL',
   'DATA'),
  ('setuptools\\_vendor\\wheel-0.45.1.dist-info\\WHEEL',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python313\\site-packages\\setuptools\\_vendor\\wheel-0.45.1.dist-info\\WHEEL',
   'DATA'),
  ('base_library.zip',
   'E:\\YC\\每日任务\\2025\\07\\0731\\北斗网格转换\\build\\北斗网格转换工具\\base_library.zip',
   'DATA')])
