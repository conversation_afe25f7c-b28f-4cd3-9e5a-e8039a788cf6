# -*- mode: python ; coding: utf-8 -*-


a = Analysis(
    ['main.py'],
    pathex=[],
    binaries=[],
    datas=[('E:\\YC\\每日任务\\2025\\07\\0731\\北斗网格转换\\测试数据', '测试数据'), ('E:\\YC\\每日任务\\2025\\07\\0731\\北斗网格转换\\README.md', '.'), ('E:\\YC\\每日任务\\2025\\07\\0731\\北斗网格转换\\北斗网格工具操作手册.docx', '.'), ('E:\\YC\\每日任务\\2025\\07\\0731\\北斗网格转换\\北斗网格工具操作手册txt版.txt', '.')],
    hiddenimports=['rasterio', 'rasterio.drivers', 'rasterio._shim', 'rasterio.control', 'rasterio.crs', 'rasterio.dtypes', 'rasterio.env', 'rasterio.errors', 'rasterio.features', 'rasterio.fill', 'rasterio.mask', 'rasterio.merge', 'rasterio.plot', 'rasterio.profiles', 'rasterio.rio', 'rasterio.sample', 'rasterio.transform', 'rasterio.warp', 'rasterio.windows', 'fiona', 'fiona.drivers', 'fiona.crs', 'fiona.transform', 'shapely', 'shapely.geometry', 'shapely.ops', 'shapely.prepared', 'pyproj', 'pyproj.crs', 'pyproj.transformer', 'numpy', 'scipy', 'scipy.ndimage', 'scipy.spatial', 'scipy.interpolate', 'tkinter', 'tkinter.ttk', 'tkinter.filedialog', 'tkinter.messagebox', 'core', 'core.aggregation', 'core.coordinate_utils', 'core.grid_calculator', 'data', 'data.data_validator', 'data.raster_io', 'data.vector_io', 'gui', 'gui.main_window', 'gui.parameter_panel', 'gui.progress_dialog', 'gui.window_utils'],
    hookspath=[],
    hooksconfig={},
    runtime_hooks=[],
    excludes=['matplotlib', 'IPython', 'jupyter', 'notebook', 'pandas', 'seaborn', 'plotly'],
    noarchive=False,
    optimize=0,
)
pyz = PYZ(a.pure)

exe = EXE(
    pyz,
    a.scripts,
    a.binaries,
    a.datas,
    [],
    name='北斗网格转换工具',
    debug=False,
    bootloader_ignore_signals=False,
    strip=False,
    upx=True,
    upx_exclude=[],
    runtime_tmpdir=None,
    console=False,
    disable_windowed_traceback=False,
    argv_emulation=False,
    target_arch=None,
    codesign_identity=None,
    entitlements_file=None,
)
