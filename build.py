#!/usr/bin/env python3
"""
北斗网格转换工具打包脚本
使用PyInstaller将Python项目打包为exe文件

使用方法:
1. 安装依赖: pip install -r requirements.txt
2. 运行打包: python build.py
3. 或者指定选项: python build.py --onefile --console

作者: 北斗网格转换工具开发团队
版本: 2.0
"""

import os
import sys
import shutil
import subprocess
import argparse
from pathlib import Path


class BeidouGridBuilder:
    """北斗网格转换工具打包器"""
    
    def __init__(self):
        self.project_root = Path(__file__).parent.absolute()
        self.dist_dir = self.project_root / "dist"
        self.build_dir = self.project_root / "build"
        self.spec_file = self.project_root / "main.spec"
        
    def clean_build(self):
        """清理之前的构建文件"""
        print("清理构建目录...")
        
        # 删除构建目录
        if self.build_dir.exists():
            shutil.rmtree(self.build_dir)
            print(f"已删除: {self.build_dir}")
            
        if self.dist_dir.exists():
            shutil.rmtree(self.dist_dir)
            print(f"已删除: {self.dist_dir}")
            
        # 删除spec文件
        if self.spec_file.exists():
            self.spec_file.unlink()
            print(f"已删除: {self.spec_file}")
            
        print("清理完成!")
    
    def check_dependencies(self):
        """检查依赖是否安装"""
        print("检查依赖...")

        required_packages = [
            'rasterio',
            'fiona',
            'shapely',
            'numpy',
            'scipy',
            'pyproj'
        ]

        missing_packages = []

        # 检查PyInstaller
        try:
            import PyInstaller
            print("✓ pyinstaller")
        except ImportError:
            try:
                import pyinstaller
                print("✓ pyinstaller")
            except ImportError:
                # 尝试通过subprocess检查
                try:
                    result = subprocess.run(['pyinstaller', '--version'],
                                          capture_output=True, text=True, check=True)
                    print("✓ pyinstaller")
                except (subprocess.CalledProcessError, FileNotFoundError):
                    missing_packages.append('pyinstaller')
                    print("✗ pyinstaller (缺失)")

        # 检查其他包
        for package in required_packages:
            try:
                __import__(package)
                print(f"✓ {package}")
            except ImportError:
                missing_packages.append(package)
                print(f"✗ {package} (缺失)")

        if missing_packages:
            print(f"\n错误: 缺少以下依赖包: {', '.join(missing_packages)}")
            print("请运行: pip install -r requirements.txt")
            return False

        print("依赖检查通过!")
        return True
    
    def get_hidden_imports(self):
        """获取隐藏导入列表"""
        return [
            # 地理空间库
            'rasterio',
            'rasterio.drivers',
            'rasterio._shim',
            'rasterio.control',
            'rasterio.crs',
            'rasterio.dtypes',
            'rasterio.env',
            'rasterio.errors',
            'rasterio.features',
            'rasterio.fill',
            'rasterio.mask',
            'rasterio.merge',
            'rasterio.plot',
            'rasterio.profiles',
            'rasterio.rio',
            'rasterio.sample',
            'rasterio.transform',
            'rasterio.warp',
            'rasterio.windows',
            'fiona',
            'fiona.drivers',
            'fiona.crs',
            'fiona.transform',
            'shapely',
            'shapely.geometry',
            'shapely.ops',
            'shapely.prepared',
            'pyproj',
            'pyproj.crs',
            'pyproj.transformer',
            
            # 科学计算库
            'numpy',
            'scipy',
            'scipy.ndimage',
            'scipy.spatial',
            'scipy.interpolate',
            'scipy.stats',
            'scipy.sparse',
            'scipy.sparse.csgraph',
            'scipy.sparse.linalg',
            'scipy.linalg',
            'scipy.optimize',
            'scipy.integrate',
            'scipy.special',
            'scipy._lib',
            'scipy._lib._util',
            'scipy._cyutility',
            'scipy.sparse._matrix',
            'scipy.sparse._base',
            'scipy.sparse._csparsetools',
            'scipy.sparse._sparsetools',
            
            # GUI相关
            'tkinter',
            'tkinter.ttk',
            'tkinter.filedialog',
            'tkinter.messagebox',
            
            # 项目模块
            'core',
            'core.aggregation',
            'core.coordinate_utils', 
            'core.grid_calculator',
            'data',
            'data.data_validator',
            'data.raster_io',
            'data.vector_io',
            'gui',
            'gui.main_window',
            'gui.parameter_panel',
            'gui.progress_dialog',
            'gui.window_utils',
        ]
    
    def get_data_files(self):
        """获取需要包含的数据文件"""
        data_files = []

        # 不包含测试数据和文档文件，保持exe文件精简

        return data_files
    
    def build_exe(self, onefile=True, console=False, debug=False):
        """构建exe文件"""
        print("开始构建exe文件...")
        
        # 构建PyInstaller命令
        cmd = [
            sys.executable, "-m", "PyInstaller",
            "--name=北斗网格转换工具",
            "--clean",
            "--noconfirm",
        ]
        
        # 添加选项
        if onefile:
            cmd.append("--onefile")
        else:
            cmd.append("--onedir")
            
        if not console:
            cmd.append("--windowed")
        else:
            cmd.append("--console")
            
        if debug:
            cmd.append("--debug=all")
        
        # 添加隐藏导入
        for import_name in self.get_hidden_imports():
            cmd.extend(["--hidden-import", import_name])
        
        # 添加数据文件
        for src, dst in self.get_data_files():
            cmd.extend(["--add-data", f"{src};{dst}"])
        
        # 添加图标（如果存在）
        icon_path = self.project_root / "icon.ico"
        if icon_path.exists():
            cmd.extend(["--icon", str(icon_path)])
        
        # 排除不需要的模块
        exclude_modules = [
            "matplotlib",
            "IPython",
            "jupyter",
            "notebook",
            "pandas",
            "seaborn",
            "plotly"
        ]
        
        for module in exclude_modules:
            cmd.extend(["--exclude-module", module])
        
        # 添加主文件
        cmd.append("main.py")
        
        print(f"执行命令: {' '.join(cmd)}")
        
        # 执行构建
        try:
            result = subprocess.run(cmd, cwd=self.project_root, check=True, 
                                  capture_output=True, text=True)
            print("构建成功!")
            print(result.stdout)
            return True
            
        except subprocess.CalledProcessError as e:
            print(f"构建失败: {e}")
            print(f"错误输出: {e.stderr}")
            return False
    
    def post_build_cleanup(self):
        """构建后清理"""
        print("执行构建后清理...")
        
        # 删除不需要的文件
        cleanup_patterns = [
            "*.pyc",
            "__pycache__",
            "*.pyo",
            "*.pyd"
        ]
        
        if self.dist_dir.exists():
            for pattern in cleanup_patterns:
                for file_path in self.dist_dir.rglob(pattern):
                    if file_path.is_file():
                        file_path.unlink()
                        print(f"删除: {file_path}")
                    elif file_path.is_dir():
                        shutil.rmtree(file_path)
                        print(f"删除目录: {file_path}")
        
        print("清理完成!")
    
    def create_installer_info(self):
        """创建安装说明文件"""
        info_content = """
北斗网格转换工具 v2.0 - 安装说明

1. 系统要求:
   - Windows 7/8/10/11 (64位)
   - 至少 4GB 内存
   - 至少 1GB 可用磁盘空间

2. 安装步骤:
   - 将exe文件复制到任意目录
   - 双击运行即可

3. 使用说明:
   - GUI模式: 直接双击exe文件
   - 命令行模式: 在命令提示符中运行 "北斗网格转换工具.exe --cli --help"

4. 注意事项:
   - 首次运行可能需要较长时间初始化
   - 建议将exe文件放在英文路径下
   - 如遇到问题，请查看操作手册

5. 技术支持:
   - 详细操作手册: 北斗网格工具操作手册.docx
   - 测试数据: 测试数据文件夹

版本: 2.0
构建时间: {build_time}
        """.strip()
        
        from datetime import datetime
        build_time = datetime.now().strftime("%Y-%m-%d %H:%M:%S")
        
        info_file = self.dist_dir / "安装说明.txt"
        with open(info_file, 'w', encoding='utf-8') as f:
            f.write(info_content.format(build_time=build_time))
        
        print(f"创建安装说明: {info_file}")


def main():
    """主函数"""
    parser = argparse.ArgumentParser(description="北斗网格转换工具打包脚本")
    parser.add_argument("--onefile", action="store_true", default=True,
                       help="打包为单个exe文件 (默认)")
    parser.add_argument("--onedir", dest="onefile", action="store_false",
                       help="打包为目录形式")
    parser.add_argument("--console", action="store_true", default=False,
                       help="显示控制台窗口")
    parser.add_argument("--debug", action="store_true", default=False,
                       help="启用调试模式")
    parser.add_argument("--clean-only", action="store_true", default=False,
                       help="仅清理构建文件")
    
    args = parser.parse_args()
    
    # 创建构建器
    builder = BeidouGridBuilder()
    
    # 仅清理模式
    if args.clean_only:
        builder.clean_build()
        return
    
    print("=" * 60)
    print("北斗网格转换工具 v2.0 - 打包脚本")
    print("=" * 60)
    
    try:
        # 1. 清理之前的构建
        builder.clean_build()
        
        # 2. 检查依赖
        if not builder.check_dependencies():
            sys.exit(1)
        
        # 3. 构建exe
        success = builder.build_exe(
            onefile=args.onefile,
            console=args.console,
            debug=args.debug
        )
        
        if not success:
            print("构建失败!")
            sys.exit(1)
        
        # 4. 构建后清理
        builder.post_build_cleanup()
        
        # 5. 创建安装说明
        builder.create_installer_info()
        
        print("=" * 60)
        print("构建完成!")
        print(f"输出目录: {builder.dist_dir}")
        
        # 显示输出文件
        if builder.dist_dir.exists():
            print("\n生成的文件:")
            for file_path in builder.dist_dir.iterdir():
                size = file_path.stat().st_size / (1024 * 1024)  # MB
                print(f"  {file_path.name} ({size:.1f} MB)")
        
        print("=" * 60)
        
    except KeyboardInterrupt:
        print("\n构建被用户中断")
        sys.exit(1)
    except Exception as e:
        print(f"构建过程中发生错误: {e}")
        import traceback
        traceback.print_exc()
        sys.exit(1)


if __name__ == "__main__":
    main()
